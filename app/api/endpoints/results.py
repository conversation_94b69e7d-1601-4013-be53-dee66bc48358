from fastapi import APIRouter, Depends
from uuid import UUID
from typing import Any

from app.services.persistence import PersistenceService
from app.services.injector import get_persistence_service

# 创建路由器
router = APIRouter()

@router.get("/{request_id}")
async def get_ocr_result(
    request_id: UUID,
    service: PersistenceService = Depends(get_persistence_service)
) -> Any:
    """根据请求ID获取OCR结果
    
    Args:
        request_id: OCR请求的唯一标识
        service: 持久化服务实例（通过依赖注入）
        
    Returns:
        dict: OCR结果数据
    """
    return service.get_result(request_id) 