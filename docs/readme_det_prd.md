# 产品需求文档 (PRD): 文本定位独立模块 (ocr_det)
文档版本: 1.0
目标读者: AI编程工具 (例如：Cursor)

## 1. 概述
### 1.1. 项目目标
将现有项目 ocr_inference 中的文本定位（Text Detection）算法，完整地剥离并封装成一个名为 ocr_det 的独立Python模块。该模块应能被其他任何项目或服务直接集成和调用。

### 1.2. 核心原则
严格复用（Strict Reuse）。此原则为最高优先级。

- 禁止新增逻辑：除非绝对必要，不得在模块中编写 ocr_inference 项目中不存在的新功能或算法逻辑。

- 精确复制行为：模块的所有行为，包括其内部处理流程、参数使用、返回值以及错误处理方式，都必须是 ocr_inference 项目中对应功能的精确复制品。

## 2. 模块定义
### 2.1. 模块形式
模块的核心应以一个名为 OcrDet 的Python类来提供。

### 2.2. 技术栈约束
- 推理引擎：仅实现基于 ONNX 推理引擎的逻辑。ocr_inference 中可能存在的其他推理引擎（如 MNN）相关代码应被忽略和排除。

- 编程语言：Python

## 3. 接口规约 (Interface Specification)
### 3.1. 类的初始化 (__init__)
- 构造函数定义：OcrDet() 构造函数不接受任何外部传入的参数。

- 构造函数职责：在实例化 detector = OcrDet() 时，构造函数内部必须完成以下操作：

    1. 从一个固定的、预定义的路径加载模块的配置文件。

    2. 根据配置文件中的信息，加载ONNX模型文件到内存中。

### 3.2. 核心处理方法 (detect)
OcrDet 类必须提供一个公开的核心处理方法。

- 方法签名:

```python
def detect(self, image: numpy.ndarray) -> List[List[List[int]]]
```

### 3.2.1. 输入 (Input)

- 参数: image

- 数据类型: numpy.ndarray

- 规约:

    - 必须是一个三通道的NumPy数组。

    - 颜色空间顺序必须是 BGR。

    - 不接受文件路径或原始二进制数据。

### 3.2.2. 输出 (Output)

- 返回类型: List[List[List[int]]]

- 规约:

    - 返回值是一个列表，其中每个子元素代表一个检测到的文本边界框。

    - 每个边界框是一个由4个点坐标组成的列表。

    - 每个点是一个由2个整数（x, y坐标）组成的列表。

    - 坐标顺序: 4个点的顺序必须严格遵循 [[左上角], [右上角], [右下角], [左下角]] 的格式。

    - 内容约束: 输出的边界框信息中，不得包含置信度分数。

## 4. 行为与逻辑规约
### 4.1. 配置加载
- 加载机制: 模块必须通过一个在代码中硬编码的、固定的文件路径来查找并加载其主配置文件。

- 路径来源: 该固定文件路径的字符串值，必须通过分析 ocr_inference 项目获得。

- 配置文件内容:

    1. 该配置文件必须包含文本定位流程中所需的所有超参数（例如：预处理的缩放尺寸、后处理的阈值等）。这些参数的名称和值必须与 ocr_inference 项目中的定义完全一致。

    2. 该配置文件中必须包含一个字段，用于指定所需 .onnx 模型文件的具体路径。

### 4.2. 内部处理逻辑
- 逻辑来源: OcrDet.detect 方法的内部实现，包括图像预处理、ONNX模型推理、推理结果后处理等所有步骤，都必须通过分析 ocr_inference 项目的源代码来确定，并进行原样移植。

### 4.3. 行为契约 (Behavioral Contract)
- 总体要求: 模块在所有场景下的行为表现，都必须与 ocr_inference 中原始的文本定位功能保持完全一致。

- 场景1: 未检测到文本

    - 规约: 当输入图像中无法检测到任何文本时，detect 方法的返回值必须与原始代码的返回值完全相同。此返回值（预期为一个空列表 []）需通过分析源代码来确认。

- 场景2: 接收到非法输入

    - 规约: 当 detect 方法接收到不符合 numpy.ndarray BGR图像格式的非法输入时，其响应行为（例如：是抛出 ValueError 异常，还是返回 None）必须与原始代码的错误处理机制完全相同。

## 5. 依赖项
### 5.1. Python库
- 任务: 需分析 ocr_inference 项目中与ONNX文本定位功能相关的所有Python库依赖。

- 产出: 创建一个 requirements.txt 文件，清晰地列出所有必需的第三方库及其版本（例如：onnxruntime, numpy, opencv-python 等）。

### 5.2. 资产文件
- 模块的正确运行依赖于以下外部文件：

    1. .onnx 模型文件。（从主配置文件中获得）

    2. 主配置文件（例如 config.yml 或 .ini 格式，具体格式需参照原始项目）。