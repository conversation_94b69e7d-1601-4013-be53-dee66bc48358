import logging
import time
from typing import List, Dict, Any

import numpy as np
import torch
from PIL import Image

from .model_loader import load_model, load_tokenizer
from .preprocessor import preprocess_cropped_images
from .postprocessor import postprocess_batch_results, extract_confidence_from_generation
from .device_utils import get_device
from .constants import MAX_TOKEN_SIZE

logger = logging.getLogger(__name__)


class TextellerEngine:
    """
    Core inference engine for Texteller formula recognition.
    
    This class coordinates model loading, image preprocessing, inference,
    and postprocessing to provide a complete formula recognition pipeline
    compatible with the existing FormulaRecognitionService interface.
    """
    
    def __init__(self, model_path: str = None, use_onnx: bool = False, repo_name: str = "OleehyO/TexTeller"):
        """
        Initialize the Texteller engine.
        
        Args:
            model_path: Path to local model files. If None, uses default HuggingFace model.
            use_onnx: Whether to use ONNX optimized version for faster inference.
            repo_name: HuggingFace repository name for the model.
        """
        logger.info("Initializing Texteller engine...")
        
        # Store configuration
        self.model_path = model_path
        self.use_onnx = use_onnx
        self.repo_name = repo_name
        
        # Initialize device
        self.device = get_device()
        logger.info(f"Using device: {self.device}")
        
        # Load model and tokenizer
        self._load_model_and_tokenizer()
        
        logger.info("Texteller engine initialization complete")
    
    def _load_model_and_tokenizer(self):
        """Load the Texteller model and tokenizer."""
        try:
            # Load model
            logger.info("Loading Texteller model...")
            self.model = load_model(
                model_dir=self.model_path,
                use_onnx=self.use_onnx,
                repo_name=self.repo_name
            )
            
            # Move model to device if not using ONNX
            if not self.use_onnx and hasattr(self.model, 'to'):
                self.model = self.model.to(self.device)
            
            # Set model to evaluation mode
            if hasattr(self.model, 'eval'):
                self.model.eval()
            
            # Load tokenizer
            logger.info("Loading Texteller tokenizer...")
            self.tokenizer = load_tokenizer(
                tokenizer_dir=self.model_path,
                repo_name=self.repo_name
            )
            
            logger.info("Model and tokenizer loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model or tokenizer: {e}")
            raise
    
    def recognize(self, image: np.ndarray, formula_bboxes: List[List[int]]) -> List[Dict[str, Any]]:
        """
        Recognize formulas in the specified regions of an image.
        
        Args:
            image: BGR format image array (H, W, C)
            formula_bboxes: List of bounding boxes, each as [x_min, y_min, x_max, y_max]
            
        Returns:
            List of recognition results, each containing:
                - latex_string: LaTeX format formula string
                - confidence: Recognition confidence score
        """
        start_time = time.time()
        
        # Handle empty input
        if not formula_bboxes:
            logger.info("No formula bounding boxes provided, returning empty list")
            return []
        
        try:
            # Convert BGR numpy array to RGB PIL Image
            image_rgb = self._convert_bgr_to_rgb_pil(image)
            
            # Crop images for each bounding box
            cropped_images = self._crop_images(image_rgb, formula_bboxes)
            
            # Preprocess images for model input
            preprocessed_tensor = preprocess_cropped_images(cropped_images)
            
            # Move tensor to device
            preprocessed_tensor = preprocessed_tensor.to(self.device)
            
            # Perform inference
            with torch.no_grad():
                generated_ids = self.model.generate(
                    preprocessed_tensor,
                    max_length=MAX_TOKEN_SIZE,
                    num_beams=1,
                    do_sample=False,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                )
            
            # Decode results
            raw_texts = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
            
            # Postprocess results
            formatted_texts = postprocess_batch_results(raw_texts, keep_style=False)
            
            # Extract confidence scores (placeholder implementation)
            confidences = extract_confidence_from_generation(generated_ids, self.tokenizer)
            
            # Construct final results
            results = []
            for latex_string, confidence in zip(formatted_texts, confidences):
                results.append({
                    "latex_string": latex_string,
                    "confidence": confidence
                })
            
            processing_time = (time.time() - start_time) * 1000
            logger.info(f"Texteller processed {len(formula_bboxes)} formulas in {processing_time:.2f} ms")
            
            return results
            
        except Exception as e:
            logger.error(f"Error during formula recognition: {e}")
            # Return empty results with error indication
            return [{"latex_string": "", "confidence": 0.0} for _ in formula_bboxes]
    
    def _convert_bgr_to_rgb_pil(self, image: np.ndarray) -> Image.Image:
        """Convert BGR numpy array to RGB PIL Image."""
        import cv2
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return Image.fromarray(image_rgb)
    
    def _crop_images(self, image: Image.Image, bboxes: List[List[int]]) -> List[Image.Image]:
        """
        Crop images based on bounding boxes.
        
        Args:
            image: PIL Image object
            bboxes: List of bounding boxes [x_min, y_min, x_max, y_max]
            
        Returns:
            List of cropped PIL Images
        """
        cropped_images = []
        for bbox in bboxes:
            x1, y1, x2, y2 = tuple(map(int, bbox))
            cropped_image = image.crop((x1, y1, x2, y2))
            cropped_images.append(cropped_image)
        
        return cropped_images
