# CycleCenterNet表格识别

本文档介绍基于CycleCenterNet的表格识别功能实现。

## 概述

CycleCenterNet表格识别是一个基于ModelScope的表格结构识别服务，使用了`iic/cv_dla34_table-structure-recognition_cycle-centernet`模型。该服务能够识别图像中的表格结构，提取单元格边界框，并结合OCR技术识别单元格内的文本内容。

## 文件结构

```
app/processing/
├── table_rec_cycle_centernet.py      # CycleCenterNet表格识别主实现
├── test_table_rec_cycle_centernet.py # 测试文件
├── example_cycle_centernet.py        # 使用示例
└── README_cycle_centernet.md         # 本文档
```

## 核心功能

### 1. 表格结构识别
- 使用CycleCenterNet模型识别表格中的单元格
- 输出多边形格式的单元格边界框
- 自动计算单元格的行列位置和跨度信息

### 2. 文本识别集成
- 支持集成文本检测和识别服务
- 自动将识别的文本内容匹配到对应单元格
- 支持多文本块的合并处理

### 3. 坐标系统处理
- 支持表格区域裁剪和坐标偏移处理
- 自动处理多边形到矩形边界框的转换
- 精确的文本-单元格匹配算法

## 主要类和方法

### CycleCenterNetTable

主要的表格识别服务类，继承自`TableRecognitionService`。

#### 初始化参数
- `config`: 配置字典
- `text_recognition`: 文本识别服务实例
- `text_detection`: 文本检测服务实例（可选）

#### 主要方法

##### `recognize_table(image_bytes, table_bbox=None)`
识别图像中的表格结构和内容。

**参数：**
- `image_bytes`: 图像的二进制数据
- `table_bbox`: 可选的表格边界框 [x_min, y_min, x_max, y_max]

**返回：**
```python
{
    "confidence": 0.9,
    "cells": [
        {
            "bbox": [x1, y1, x2, y2, x3, y3, x4, y4],  # 多边形边界框
            "row_start": 0,      # 起始行
            "col_start": 0,      # 起始列
            "row_span": 1,       # 行跨度
            "col_span": 1,       # 列跨度
            "text": "单元格文本", # 文本内容
            "confidence": 0.95   # 置信度
        }
    ]
}
```

## 使用示例

### 基本使用

```python
from app.processing.table_rec_cycle_centernet import CycleCenterNetTable
from app.processing.text_recognition import TextRecognitionService

# 配置
config = {"USE_CUDA": False}

# 创建服务实例
text_recognition = YourTextRecognitionService()
table_recognition = CycleCenterNetTable(
    config=config,
    text_recognition=text_recognition
)

# 读取图像
with open("table_image.png", "rb") as f:
    image_bytes = f.read()

# 识别表格
result = table_recognition.recognize_table(image_bytes)

# 处理结果
for cell in result["cells"]:
    print(f"行{cell['row_start']}, 列{cell['col_start']}: {cell['text']}")
```

### 带表格边界框的使用

```python
# 定义表格区域
table_bbox = [100, 50, 800, 600]  # [x_min, y_min, x_max, y_max]

# 识别指定区域的表格
result = table_recognition.recognize_table(image_bytes, table_bbox)
```

## 依赖关系

### 核心依赖
- `modelscope`: ModelScope框架
- `numpy`: 数值计算
- `opencv-python`: 图像处理

### 内部依赖
- `app.processing.table_recognition.TableRecognitionService`: 基础表格识别服务
- `app.processing.text_recognition.TextRecognitionService`: 文本识别服务
- `app.processing.text_detection.TextDetectionService`: 文本检测服务
- `app.utils.table_logic`: 表格逻辑处理工具

## 配置说明

### 基本配置
```python
config = {
    "USE_CUDA": False,  # 是否使用GPU加速
}
```

### 模型配置
模型使用ModelScope的预训练模型：
- 模型ID: `iic/cv_dla34_table-structure-recognition_cycle-centernet`
- 任务类型: `Tasks.table_recognition`

## 性能特点

### 优势
1. **高精度**: 基于深度学习的表格结构识别
2. **多边形支持**: 能够处理倾斜和变形的表格
3. **灵活集成**: 支持多种OCR服务集成
4. **坐标精确**: 精确的坐标系统和边界框处理

### 适用场景
- 文档图像中的表格识别
- 扫描件表格结构分析
- 复杂表格的自动化处理
- 表格数据提取和结构化

## 测试

运行测试文件：
```bash
python app/processing/test_table_rec_cycle_centernet.py
```

运行使用示例：
```bash
python app/processing/example_cycle_centernet.py
```

## 注意事项

1. **图像质量**: 确保输入图像质量良好，表格边界清晰
2. **模型下载**: 首次使用时会自动下载模型文件
3. **内存使用**: 处理大图像时注意内存使用情况
4. **临时文件**: 系统会创建临时图像文件，会自动清理
5. **文本匹配**: 文本与单元格的匹配基于重叠面积，阈值为80%

## 扩展和定制

### 自定义文本匹配策略
可以通过修改`_convert_to_table_result`方法中的匹配逻辑来自定义文本匹配策略。

### 集成其他OCR服务
通过实现`TextRecognitionService`和`TextDetectionService`接口，可以集成任何OCR服务。

### 性能优化
- 使用GPU加速（设置`USE_CUDA=True`）
- 预处理图像以提高识别精度
- 批量处理时复用模型实例
