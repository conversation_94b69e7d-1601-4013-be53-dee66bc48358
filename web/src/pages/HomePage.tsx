import React, { useState } from 'react';
import { Layout, Typography, Select, Space, Alert, Spin, Empty, Breadcrumb } from 'antd';
import { HomeOutlined, FileImageOutlined } from '@ant-design/icons';
import { ImageUploader } from '../components/ImageUploader';
import { ResultDisplay } from '../components/ResultDisplay';
import { useOcrAnalysis } from '../hooks/useOcrAnalysis';
import { ViewType } from '../types/ocr';

const { Header, Content } = Layout;
const { Title } = Typography;
const { Option } = Select;

const VIEW_TYPE_OPTIONS = [
  { value: 'text_detection', label: '文字定位' },
  { value: 'text_recognition', label: '文字识别' },
  { value: 'layout_analysis', label: '版面分析' },
  { value: 'table_recognition', label: '表格识别' },
  { value: 'formula_recognition', label: '公式识别' },
  { value: 'full_result', label: '最终组合结果' },
];

export const HomePage: React.FC = () => {
  const [viewType, setViewType] = useState<ViewType>('full_result');
  const analysisMutation = useOcrAnalysis();

  const handleImageUpload = (file: File) => {
    analysisMutation.mutate(file);
  };

  const handleViewTypeChange = (value: ViewType) => {
    setViewType(value);
  };

  const renderContent = () => {
    if (analysisMutation.isPending) {
      return (
        <div style={{ textAlign: 'center', padding: '80px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', fontSize: '16px' }}>
            正在处理图片，请稍候...
          </div>
        </div>
      );
    }

    if (analysisMutation.isError) {
      return (
        <Alert
          message="处理失败"
          description={analysisMutation.error?.message || '图片处理失败，请重试'}
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      );
    }

    if (analysisMutation.data && analysisMutation.data.status === 'ok') {
      return (
        <ResultDisplay 
          result={analysisMutation.data} 
          viewType={viewType} 
        />
      );
    }

    if (analysisMutation.data && analysisMutation.data.status !== 'ok') {
      return (
        <Alert
          message="处理失败"
          description={analysisMutation.data.error_message || '未知错误'}
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      );
    }

    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="请上传图片开始OCR处理"
        style={{ margin: '80px 0' }}
      />
    );
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        backgroundColor: '#fff', 
        borderBottom: '1px solid #f0f0f0',
        padding: '0 24px',
        display: 'flex',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          OCR 全流程处理系统
        </Title>
      </Header>

      <Content style={{ padding: '24px' }}>
        <Breadcrumb style={{ marginBottom: '24px' }}>
          <Breadcrumb.Item>
            <HomeOutlined />
            <span>首页</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <FileImageOutlined />
            <span>OCR处理</span>
          </Breadcrumb.Item>
        </Breadcrumb>

        {/* 操作区域 */}
        <div style={{ 
          backgroundColor: '#fff', 
          padding: '24px', 
          borderRadius: '8px',
          marginBottom: '24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
        }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <ImageUploader 
              onUpload={handleImageUpload}
              isLoading={analysisMutation.isPending}
            />
            
            <div>
              <span style={{ marginRight: '12px', fontWeight: 500 }}>
                选择查看结果：
              </span>
              <Select
                value={viewType}
                onChange={handleViewTypeChange}
                disabled={!analysisMutation.data || analysisMutation.data.status !== 'ok'}
                style={{ width: '200px' }}
              >
                {VIEW_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Space>
        </div>

        {/* 结果展示区域 */}
        <div style={{ 
          backgroundColor: '#fff', 
          padding: '24px', 
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
        }}>
          {renderContent()}
        </div>
      </Content>
    </Layout>
  );
}; 