model:
  default_repo_name: "OleehyO/TexTeller"
  default_path: null  # Use HuggingFace model by default
  use_onnx: false
  max_tokens: 1024
  num_beams: 1
  no_repeat_ngram_size: 0

preprocessing:
  image_size: 448
  channels: 1  # grayscale
  normalize_mean: 0.9545467
  normalize_std: 0.15394445

postprocessing:
  output_format: "latex"
  keep_style: false
  confidence_threshold: 0.5

batch:
  size: 4
  timeout: 30.0

cache:
  dir: "~/.cache/texteller"
