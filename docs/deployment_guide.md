# AgileOCR 部署指南

## 概述

本文档描述了AgileOCR项目的CI/CD部署流程，该项目支持同时运行后端API服务和前端Web服务。

## 架构说明

### 服务架构
- **后端服务**: FastAPI应用，监听端口6000
- **前端服务**: 静态文件服务，监听端口5000
- **进程管理**: 使用Supervisor管理多服务进程

### 部署环境
- **测试环境**: agileocr-test-inner.zhhainiao.com
- **容器平台**: Kubernetes
- **镜像仓库**: harbor-aliyun.zhhainiao.com

## 部署流程

### 1. 代码提交与标签

#### 首次部署
```bash
git tag v1.0.0-test
git push origin v1.0.0-test
```

#### 增量更新
```bash
git tag v1.0.1-update-test
git push origin v1.0.1-update-test
```

### 2. CI/CD流程

#### 构建阶段 (k8s_build_test)
- 自动触发条件: 标签匹配 `v.*-test$`
- 构建Docker镜像
- 推送到镜像仓库

#### 部署阶段
1. **Ingress部署** (volc_k8s_test_ingress)
   - 手动触发 (仅增量更新时)
   - 配置路由规则

2. **服务部署** (volc_k8s_test_deploy)
   - 首次部署: 自动触发
   - 增量更新: 手动触发

## 服务配置

### 环境变量
```bash
TORCH_CUDA_ARCH_LIST=8.6
BACKEND_HOST=localhost
BACKEND_PORT=6000
FRONTEND_PORT=5000
```

### 端口配置
- 后端API: 6000
- 前端Web: 5000

### 健康检查
- **存活检查**: `/api/v1/health`
- **就绪检查**: `/api/v1/ready`
- **详细检查**: `/api/v1/live`

## 访问地址

### 测试环境
- **前端页面**: https://agileocr-test-inner.zhhainiao.com/
- **后端API**: https://agileocr-test-inner.zhhainiao.com/api/

### API端点示例
```bash
# 健康检查
curl https://agileocr-test-inner.zhhainiao.com/api/v1/health

# OCR处理
curl -X POST https://agileocr-test-inner.zhhainiao.com/api/v1/web/ocr/full_result \
  -H "Content-Type: application/json" \
  -d '{"image_base64": "..."}'
```

## 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查容器日志
   kubectl logs -f deployment/agileocr-test -n kanpic
   
   # 检查Supervisor状态
   kubectl exec -it deployment/agileocr-test -n kanpic -- supervisorctl status
   ```

2. **前端无法访问后端**
   - 检查环境变量配置
   - 验证网络连通性
   - 查看Supervisor日志

3. **健康检查失败**
   ```bash
   # 手动执行健康检查
   kubectl exec -it deployment/agileocr-test -n kanpic -- ./deploy/scripts/health_check.sh
   ```

### 日志查看
```bash
# Supervisor主日志
tail -f /var/log/supervisor/supervisord.log

# 后端服务日志
tail -f /var/log/supervisor/backend.out.log

# 前端服务日志
tail -f /var/log/supervisor/frontend.out.log
```

## 运维操作

### 重启服务
```bash
# 重启所有服务
kubectl rollout restart deployment/agileocr-test -n kanpic

# 重启特定服务
kubectl exec -it deployment/agileocr-test -n kanpic -- supervisorctl restart backend
kubectl exec -it deployment/agileocr-test -n kanpic -- supervisorctl restart frontend
```

### 扩缩容
```bash
# 扩容到3个副本
kubectl scale deployment agileocr-test --replicas=3 -n kanpic

# 缩容到1个副本
kubectl scale deployment agileocr-test --replicas=1 -n kanpic
```

### 回滚部署
```bash
# 查看部署历史
kubectl rollout history deployment/agileocr-test -n kanpic

# 回滚到上一个版本
kubectl rollout undo deployment/agileocr-test -n kanpic
```

## 监控指标

### 关键指标
- 服务可用性: 99.9%
- API响应时间: < 2s
- 内存使用率: < 80%
- CPU使用率: < 70%

### 告警规则
- 服务不可用超过1分钟
- API响应时间超过5秒
- 内存使用率超过90%
- 错误率超过5%

## 安全注意事项

1. **网络安全**
   - 仅允许内网访问
   - 使用HTTPS协议

2. **数据安全**
   - 敏感数据加密存储
   - 定期备份重要数据

3. **访问控制**
   - 限制管理员权限
   - 定期更新访问密钥

## 联系方式

如有问题，请联系：
- 开发团队: <EMAIL>
- 运维团队: <EMAIL>
