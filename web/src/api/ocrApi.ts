// 添加ImportMeta接口扩展，解决TypeScript错误
/// <reference types="vite/client" />

import axios from 'axios';
import { OCRResponse, ViewType } from '../types/ocr';

// 构建API基础URL
const backendHost = import.meta.env.VITE_BACKEND_HOST || '127.0.0.1';
const backendPort = import.meta.env.VITE_BACKEND_PORT || '6000';
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || `http://${backendHost}:${backendPort}/api/v1/web`;

// 定义OCR API错误类（参考ocr_api.py的OcrApiError）
export class OcrApiError extends Error {
  httpCode: number;
  statusCode: string;
  errorMessage: string;
  requestId?: string;

  constructor(httpCode: number, statusCode: string, message: string, requestId?: string) {
    super(`OCR API错误 [HTTP ${httpCode}] [${statusCode}]: ${message}`);
    this.name = 'OcrApiError';
    this.httpCode = httpCode;
    this.statusCode = statusCode;
    this.errorMessage = message;
    this.requestId = requestId;
  }
}

// 辅助函数：从Blob中解析JSON
async function parseJsonFromBlob(blob: Blob): Promise<any> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        resolve(JSON.parse(reader.result as string));
      } catch (e) {
        reject(e);
      }
    };
    reader.onerror = () => reject(new Error('读取响应失败'));
    reader.readAsText(blob);
  });
}

// 处理错误响应的函数（遵循ocr_api.py中的逻辑）
export async function handleErrorResponse(error: any): Promise<never> {
  if (!axios.isAxiosError(error) || !error.response) {
    throw error; // 非Axios错误或没有响应，直接抛出
  }
  
  const response = error.response;
  let statusCode = 'unknown';
  let errorMessage = '未知错误';
  let requestId = undefined;

  try {
    // 处理不同格式的响应数据
    if (response.data instanceof Blob) {
      // 如果是Blob格式，尝试解析为JSON
      try {
        const contentType = response.headers['content-type'] || '';
        if (contentType.includes('application/json')) {
          const jsonData = await parseJsonFromBlob(response.data);
          statusCode = jsonData.status || 'unknown';
          errorMessage = jsonData.error_message || '未知错误';
          requestId = jsonData.request_id;
        }
      } catch (e) {
        console.error('解析Blob响应失败', e);
      }
    } else if (typeof response.data === 'object') {
      // 如果已经是对象，直接使用
      statusCode = response.data.status || 'unknown';
      errorMessage = response.data.error_message || '未知错误';
      requestId = response.data.request_id;
    }
  } catch (e) {
    console.error('处理错误响应失败', e);
  }
  
  throw new OcrApiError(
    response.status,
    statusCode,
    errorMessage,
    requestId
  );
}

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
});

export const ocrApi = {
  /**
   * 上传图片并获取完整OCR结果
   */
  async uploadImage(file: File): Promise<OCRResponse> {
    try {
      // 将文件转换为base64
      const base64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          // 移除data:image/...;base64,前缀，只保留base64字符串
          const base64String = result.split(',')[1];
          resolve(base64String);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      const response = await apiClient.post<OCRResponse>('/ocr/full_result', {
        image_base64: base64,
        filename: file.name
      });
      
      // 检查API状态码，即使HTTP状态是200
      if (response.data.status !== 'ok') {
        throw new OcrApiError(
          response.status,
          response.data.status || 'unknown',
          response.data.error_message || '未知错误',
          response.data.request_id
        );
      }
      
      return response.data;
    } catch (error) {
      return handleErrorResponse(error);
    }
  },

  /**
   * 获取原始上传的图片
   */
  async getOriginalImage(requestId: string): Promise<string> {
    try {
      const response = await apiClient.get(`/images/${requestId}`, {
        responseType: 'blob'
      });
      
      return URL.createObjectURL(response.data);
    } catch (error) {
      return handleErrorResponse(error);
    }
  },

  /**
   * 获取可视化结果图片
   */
  async getVisualizationImage(requestId: string, viewType: ViewType): Promise<string> {
    try {
      const response = await apiClient.get(`/visualizations/${requestId}`, {
        params: { view_type: viewType },
        responseType: 'blob'
      });
      
      // 检查内容类型，确保是图片
      const contentType = response.headers['content-type'] || '';
      if (!contentType.includes('image/')) {
        // 如果不是图片，可能是API返回了成功但内容错误
        try {
          if (response.data instanceof Blob) {
            const jsonData = await parseJsonFromBlob(response.data);
            if (jsonData.status && jsonData.status !== 'ok') {
              throw new OcrApiError(
                response.status,
                jsonData.status,
                jsonData.error_message || '未知错误',
                jsonData.request_id
              );
            }
          }
        } catch (e) {
          if (e instanceof OcrApiError) throw e;
          throw new Error('服务器返回了非图片数据');
        }
      }
      
      return URL.createObjectURL(response.data);
    } catch (error) {
      return handleErrorResponse(error);
    }
  }
}; 