#!/usr/bin/python3
# -*- coding: utf-8 -*-

import requests
import logging
from typing import Tuple
from urllib.parse import urlparse
import mimetypes

from app.core import errors

logger = logging.getLogger(__name__)

# 支持的图像MIME类型
SUPPORTED_IMAGE_TYPES = {
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
    'image/bmp', 'image/webp', 'image/tiff'
}

# 支持的图像文件扩展名
SUPPORTED_IMAGE_EXTENSIONS = {
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'
}

class ImageDownloadError(Exception):
    """图像下载相关错误"""
    pass


def download_image_from_url(image_url: str, timeout: int = 30, max_size: int = 50 * 1024 * 1024) -> bytes:
    """从URL下载图像
    
    Args:
        image_url: 图像URL
        timeout: 请求超时时间（秒），默认30秒
        max_size: 最大文件大小（字节），默认50MB
        
    Returns:
        bytes: 图像的二进制数据
        
    Raises:
        ImageDownloadError: 当下载失败或图像格式不支持时
        errors.ClientErr: 当URL格式无效或其他客户端错误时
    """
    # 验证URL格式
    if not image_url or not isinstance(image_url, str):
        raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "image_url must be a non-empty string")
    
    # 解析URL
    try:
        parsed_url = urlparse(image_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "invalid image_url format")
        
        # 只允许HTTP和HTTPS协议
        if parsed_url.scheme.lower() not in ['http', 'https']:
            raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "only HTTP and HTTPS protocols are supported")
            
    except Exception as e:
        logger.error(f"URL解析失败: {image_url}, 错误: {str(e)}")
        raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, f"invalid image_url: {str(e)}")
    
    # 检查文件扩展名（可选验证）
    path_lower = parsed_url.path.lower()
    if path_lower and not any(path_lower.endswith(ext) for ext in SUPPORTED_IMAGE_EXTENSIONS):
        logger.warning(f"URL路径不包含支持的图像扩展名: {image_url}")
    
    try:
        logger.info(f"开始下载图像: {image_url}")
        
        # 设置请求头
        headers = {
            'User-Agent': 'AgileOCR/1.0 (Image Downloader)',
            'Accept': 'image/*',
        }
        
        # 发送HEAD请求检查内容类型和大小
        try:
            head_response = requests.head(image_url, headers=headers, timeout=timeout, allow_redirects=True)
            
            # 检查内容类型
            content_type = head_response.headers.get('content-type', '').lower()
            if content_type and not any(supported_type in content_type for supported_type in SUPPORTED_IMAGE_TYPES):
                logger.warning(f"可能不支持的内容类型: {content_type}")
            
            # 检查文件大小
            content_length = head_response.headers.get('content-length')
            if content_length:
                file_size = int(content_length)
                if file_size > max_size:
                    raise ImageDownloadError(f"文件太大: {file_size} bytes (最大允许: {max_size} bytes)")
                logger.info(f"文件大小: {file_size} bytes")
                
        except requests.RequestException as e:
            logger.warning(f"HEAD请求失败，继续尝试GET请求: {str(e)}")
        
        # 发送GET请求下载图像
        response = requests.get(image_url, headers=headers, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # 检查响应的内容类型
        content_type = response.headers.get('content-type', '').lower()
        if content_type and not any(supported_type in content_type for supported_type in SUPPORTED_IMAGE_TYPES):
            logger.warning(f"响应的内容类型可能不是图像: {content_type}")
        
        # 分块下载并检查大小
        image_data = b''
        downloaded_size = 0
        
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                downloaded_size += len(chunk)
                if downloaded_size > max_size:
                    raise ImageDownloadError(f"下载的文件太大: {downloaded_size} bytes (最大允许: {max_size} bytes)")
                image_data += chunk
        
        if not image_data:
            raise ImageDownloadError("下载的图像数据为空")
        
        logger.info(f"成功下载图像: {len(image_data)} bytes from {image_url}")
        return image_data
        
    except requests.exceptions.Timeout:
        logger.error(f"下载图像超时: {image_url}")
        raise ImageDownloadError(f"下载超时 (超过 {timeout} 秒)")
        
    except requests.exceptions.ConnectionError:
        logger.error(f"连接错误: {image_url}")
        raise ImageDownloadError("无法连接到图像服务器")
        
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP错误: {e.response.status_code} for {image_url}")
        raise ImageDownloadError(f"HTTP错误: {e.response.status_code}")
        
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)} for {image_url}")
        raise ImageDownloadError(f"下载失败: {str(e)}")
        
    except Exception as e:
        logger.error(f"未知错误: {str(e)} for {image_url}")
        raise ImageDownloadError(f"下载失败: {str(e)}")


def validate_image_data(image_data: bytes) -> bool:
    """验证图像数据的基本格式
    
    Args:
        image_data: 图像的二进制数据
        
    Returns:
        bool: 如果是有效的图像数据返回True，否则返回False
    """
    if not image_data or len(image_data) < 10:
        return False
    
    # 检查常见图像格式的文件头
    image_signatures = [
        b'\xff\xd8\xff',  # JPEG
        b'\x89PNG\r\n\x1a\n',  # PNG
        b'GIF87a',  # GIF87a
        b'GIF89a',  # GIF89a
        b'BM',  # BMP
        b'RIFF',  # WebP (需要进一步检查)
        b'II*\x00',  # TIFF (little endian)
        b'MM\x00*',  # TIFF (big endian)
    ]
    
    for signature in image_signatures:
        if image_data.startswith(signature):
            # 对于WebP，需要额外检查
            if signature == b'RIFF' and len(image_data) >= 12:
                if image_data[8:12] == b'WEBP':
                    return True
                else:
                    continue
            return True
    
    return False
