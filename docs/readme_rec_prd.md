# 产品需求文档 (PRD): OCR_RECT 模块 (重构版)

## 1. 项目目标与核心原则

**核心任务**: 本项目的核心任务是 **代码重构与模块化**。我们将从一个现有的、完整的OCR项目中，剥离出与 **文本方向分类** 和 **文本识别** 相关的功能，将其封装成一个名为 `OCR_RECT` 的、完全独立的Python模块。

**指导原则: 忠实复用，不新增逻辑**

- **功能对齐**: `OCR_RECT` 模块的所有功能、算法逻辑、处理流程（包括预处理、后处理、模型推理、批处理策略、多轮识别机制等）都将 **严格复刻** 原始代码的实现。
- **不变性**: 本次任务 **不包含任何新功能的开发或现有逻辑的修改**。工作重点是对代码进行重新组织和封装。
- **结构优化**: 将原本分散在不同文件中的辅助函数（如特定于分类器的预处理函数），整合到相应的类（如 `TextClassifier`）中，作为其成员方法，以提高模块的内聚性和封装性。

---

## 2. 功能需求

| 功能点             | 详细描述                                                                                                                                                                                             |
| :----------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **输入接口**       | 模块需提供一个主调用接口，接收 `image` 和 `bounding_boxes` 两个参数。                                                                                                                                    |
|                    | - `image`: 原始图像，格式为 `numpy` 数组。                                                                                                                                                           |
|                    | - `bounding_boxes`: 边框列表，每个边框为 `[[x1, y1], [x2, y2], [x3, y3], [x4, y4]]`。**开发假设**：此坐标点列表严格遵循 **顺时针、左上角开始** 的顺序，模块内部不对此进行额外校验或修正。 |
| **输出接口**       | 模块返回一个结果列表，列表长度与输入的 `bounding_boxes` 列表相同，且顺序一一对应。每个元素为字典结构。                                                                                                                             |
|                    | - `bbox`: `[[x1, y1], [x2, y2], [x3, y3], [x4, y4]]`，原样返回对应的输入边框。                                                                                                                            |
|                    | - `words`: `string`，识别出的文本字符串。                                                                                                                                                            |
|                    | - `confidence`: `float` (0.0 到 1.0)，表示识别结果的置信度。                                                                                                                                         |
|                    | - `direction`: `int`，文本方向，值为 `0, 90, 180, 270` 之一。                                                                                                                                        |
| **核心处理逻辑**   | 1. **预处理**: 对每个输入边框，从原图进行透视变换，提取出校正后的文本图像块。                                                                                                                                      |
|                    | 2. **方向分类**: 使用分类模型判断文本图像块的方向。                                                                                                                                                    |
|                    | 3. **旋转校正**: 根据分类结果，将文本图像块旋转至0度方向。                                                                                                                                              |
|                    | 4. **文本识别**: 使用识别模型对校正后的图像块进行文字识别。                                                                                                                                              |
|                    | 5. **多轮识别/批处理机制**: **复用原始逻辑**。如果原始代码中存在相关机制（如低置信度重试、批处理推理），则在重构中保留该机制；否则，不主动添加。                                                                      |
| **特殊情况处理** | - **空输入边框列表**: 如果输入的 `bounding_boxes` 列表为空，模块应正常返回一个空列表 `[]`。                                                                                                                            |
|                    | - **无文本/低置信度**: **复用原始逻辑**。对于无法识别或置信度低的情况，输出结果（如 `words`, `confidence`, `direction` 的值）将完全参照原始代码的行为。 |
| **模型支持**       | - 仅支持 **ONNX** 格式的模型推理。                                                                                                                                                                    |
|                    | - 依赖 `onnxruntime` 等相关库。                                                                                                                                                                      |

## 3. 非功能性需求

| 需求类别         | 详细描述                                                                                                                                                                                           |
| :--------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **配置管理**     | - 所有配置项统一存放在一个名为 `rec_config.yaml` 的文件中。                                                                                                                                            |
|                  | - 配置文件内容需按**模型（分类器、识别器）和功能（预处理、后处理）分层组织**，以保证可读性和可维护性。                                                                                                              |
|                  | - 配置文件必须包含所有与模型推理相关的参数，包括但不限于：模型路径、预处理参数、后处理参数、字符集字典路径等。                                                                                                          |
| **错误处理**     | - **Fail-Fast**: 采用此机制处理关键性、阻塞性错误。触发场景包括：输入图像为空或损坏、配置文件或关键配置项（如模型路径）缺失等。当此类错误发生时，模块应立即抛出异常。                                                                 |
|                  | - **正常流程**: 对于非关键错误或预期内的空输入（如“输入的边框坐标列表为空”），模块不应崩溃，而是应返回一个适当的空结果或记录日志信息，这属于正常流程处理。                                                                        |
| **依赖管理**     | - 必须提供一个 `requirements.txt` 文件，清晰列出所有Python依赖库及其精确版本，以确保模块在不同环境中的可复现性和稳定性。                                                                                                |
| **日志记录**     | - 模块需集成日志系统（例如，使用Python内置的 `logging` 模块）。                                                                                                                                        |
|                  | - 允许模块的调用者通过标准方式配置日志级别（如 DEBUG, INFO, WARNING, ERROR），以便在集成和调试时能获取到模块内部的详细运行状态。                                                                                        |
