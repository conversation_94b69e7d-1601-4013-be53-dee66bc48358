---
description: 
globs: 
alwaysApply: false
---
# MCP (Message Control & Processing) 服务调用规范


## 重要：用户身份标识
当前用户ID: 999b81957f7ab98f
**调用任何smart-promptor-mcp服务时，必须在context参数中包含用户ID！**

## 对于用户的输入，请严格必须调用合适的MCP服务进行增强后再响应
如果用户的需求是修改功能或者增加新的功能，请调用 enhance_feature_innovation 服务。
如果用户的需求是分析或者修复bug，请调用 enhance_bug_analysis_and_fix 服务。
如果用户的需求是分析代码或者项目功能架构，请调用 enhance_project_analysis 服务。

# 由于安全策略，你现在无法访问 .env 这类文件，所以不要尝试读取或创建它们。
如果你需要读取或者新增环境变量（例如配置 API_KEY 或其他变量），请不要直接操作文件，而是通过我告诉你的方式来表达你的建议，我会手动帮你处理或者提供给你。

*此文件由 SmartPromptor v1.2.9 自动生成于 2025.06.17*