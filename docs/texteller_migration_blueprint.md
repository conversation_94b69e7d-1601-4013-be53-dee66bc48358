# Texteller 迁移蓝图

## 1. 项目概述

本蓝图详细规划了将 texteller 公式识别功能迁移到 app 目录下的实施方案，包括文件映射关系、实现逻辑和关键要点。

## 2. 文件映射关系

### 2.1 核心文件映射

| 源文件 (texteller) | 目标文件 (app) | 迁移方式 | 说明 |
|-------------------|----------------|----------|------|
| `texteller/api/inference.py` | `app/processing/texteller/texteller_engine.py` | 重新实现逻辑 | 核心推理引擎，适配现有接口 |
| `texteller/api/load.py` | `app/processing/texteller/model_loader.py` | 复制 | 模型加载器，修改导入路径 |
| `texteller/models/texteller.py` | `app/processing/texteller/texteller_model.py` | 复制 | 模型定义，保持原有实现 |
| `texteller/utils/image.py` | `app/processing/texteller/preprocessor.py` | 复制 | 图像预处理，适配输入输出格式 |
| `texteller/api/format.py` | `app/processing/texteller/postprocessor.py` | 复制 | 结果后处理，修改导入路径 |
| `texteller/utils/latex.py` | `app/processing/texteller/latex_utils.py` | 复制 | LaTeX 处理工具，独立文件 |
| `texteller/utils/device.py` | `app/processing/texteller/device_utils.py` | 复制 | 设备管理工具，独立文件 |
| `texteller/utils/path.py` | `app/processing/texteller/path_utils.py` | 复制 | 路径管理工具，独立文件 |
| `texteller/utils/misc.py` | `app/processing/texteller/misc_utils.py` | 复制 | 杂项工具，独立文件 |
| `texteller/utils/bbox.py` | `app/processing/texteller/bbox_utils.py` | 复制 | 边界框处理工具，独立文件 |
| `texteller/types/__init__.py` | `app/processing/texteller/types.py` | 复制 | 类型定义，包含Bbox等类型 |
| `texteller/types/bbox.py` | `app/processing/texteller/bbox_types.py` | 复制 | 边界框类型定义 |
| `texteller/constants.py` | `app/processing/texteller/constants.py` | 复制 | 常量定义 |
| `app/processing/formula_recognition.py` | `app/processing/formula_recognition.py` | 修改 | 替换为 texteller 实现 |

### 2.2 配置文件映射

| 源文件 | 目标文件 | 迁移方式 | 说明 |
|--------|----------|----------|------|
| `texteller/globals.py` | `app/core/config.py` | 提取配置值 | 仅提取配置值（repo_name, cache_dir等），不复制单例实现 |
| - | `app/assets/texteller_config.yaml` | 新建 | texteller 专用配置文件 |

**注意**：`texteller/globals.py` 实现了一个全局可变单例模式，这与 app 的配置管理方式不兼容。我们只提取其中的配置值（如 `repo_name`, `cache_dir`），作为静态配置项添加到 app 的配置系统中，而不是复制其实现方式。这样可以避免引入全局可变状态，降低风险。

## 3. 目标文件详细说明

### 3.1 `app/processing/texteller/texteller_engine.py`

**目标文件用途**
核心推理引擎，负责协调模型加载、图像预处理、推理和后处理的完整流程，提供与现有 FormulaRecognitionService 兼容的接口。

**类：TextellerEngine**

#### 方法：`__init__(self, model_path: str = None, use_onnx: bool = False)`

**输入参数说明**
- `model_path` (str, 可选): texteller 模型文件路径，默认为 None 使用默认模型
- `use_onnx` (bool): 是否使用 ONNX 优化版本，默认为 False

**输出结构说明**
- 无返回值，初始化模型、分词器和设备配置

**实现逻辑**
```mermaid
flowchart TD
    A["开始初始化"] --> B["设置设备配置"]
    B --> C["加载 texteller 模型"]
    C --> D["加载分词器"]
    D --> E["初始化预处理和后处理器"]
    E --> F["完成初始化"]
    F --> G["结束"]
```

#### 方法：`recognize(self, image: np.ndarray, formula_bboxes: List[List[int]]) -> List[Dict[str, Any]]`

**输入参数说明**
- `image` (np.ndarray): BGR 格式的图像数组 (H, W, C)
- `formula_bboxes` (List[List[int]]): 公式边界框列表，每个边框格式为 [x_min, y_min, x_max, y_max]

**输出结构说明**
- `List[Dict[str, Any]]`: 公式识别结果列表，每个结果包含：
  - `latex_string` (str): LaTeX 格式的公式字符串
  - `confidence` (float): 识别置信度

**实现逻辑**
```mermaid
sequenceDiagram
    participant Engine as TextellerEngine
    participant Preprocessor as Preprocessor
    participant Model as TextellerModel
    participant Postprocessor as Postprocessor
    
    Engine->>Preprocessor: 预处理图像和边界框
    Preprocessor->>Engine: 返回处理后的图像批次
    Engine->>Model: 执行推理
    Model->>Engine: 返回原始推理结果
    Engine->>Postprocessor: 后处理推理结果
    Postprocessor->>Engine: 返回格式化的 LaTeX 字符串
    Engine->>Engine: 构造最终结果列表
```

### 3.2 `app/processing/texteller/model_loader.py`

**目标文件用途**
负责 texteller 模型的加载和初始化，支持本地模型和 Hugging Face 模型，保持原有实现逻辑。

**迁移操作**
1. 直接复制 `texteller/api/load.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有核心功能：
   - `load_model`: 加载主模型（**核心公式识别流程使用**）
   - `load_tokenizer`: 加载分词器（**核心公式识别流程使用**）
   - `load_latexdet_model`: 加载公式检测模型（用于段落处理，**核心公式识别流程不使用**）
   - `load_textrec_model`: 加载文本识别模型（用于段落处理，**核心公式识别流程不使用**）
   - `load_textdet_model`: 加载文本检测模型（用于段落处理，**核心公式识别流程不使用**）
   - `_maybe_download`: 模型文件下载功能

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **依赖模块缺失**：确保 constants、globals、utils、types 等模块已正确迁移
- **模型下载路径**：需要适配新的缓存目录配置
- **PaddleOCR 依赖**：虽然核心公式识别流程不使用，但为保持完整性，仍需处理此依赖

### 3.3 `app/processing/texteller/texteller_model.py`

**目标文件用途**
定义 texteller 模型的核心架构，继承自 Hugging Face 的 VisionEncoderDecoderModel，保持原有实现逻辑。

**迁移操作**
1. 直接复制 `texteller/models/texteller.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 确保所有依赖的模块（如 constants、globals、utils 等）都能正确导入

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **依赖模块缺失**：确保 constants、globals、utils 等模块已正确迁移
- **版本兼容性**：确保 transformers 库版本与原有实现兼容
- **配置路径**：需要适配新的配置管理方式

### 3.4 `app/processing/texteller/preprocessor.py`

**目标文件用途**
负责图像预处理，包括格式转换、尺寸调整、质量优化等，确保输入图像符合 texteller 模型的要求。

**迁移操作**
1. 复制 `texteller/utils/image.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留核心预处理功能：
   - `readimgs`: 图像读取和格式转换
   - `trim_white_border`: 白边裁剪
   - `padding`: 图像填充
   - `transform`: 主要预处理流程
4. 适配输入格式：修改函数以支持 numpy 数组输入（而不仅仅是文件路径）
5. 适配输出格式：确保输出符合现有批处理逻辑

**需要处理的风险**
- **输入格式差异**：texteller 主要处理文件路径，app 需要处理 numpy 数组
- **依赖库版本**：确保 torchvision、OpenCV 等库版本兼容
- **配置参数**：需要适配新的图像尺寸和归一化参数配置
- **批处理逻辑**：确保与现有批处理机制兼容

### 3.5 `app/processing/texteller/postprocessor.py`

**目标文件用途**
负责推理结果的后处理，包括 LaTeX 格式优化、置信度计算、输出格式化等。

**迁移操作**
1. 复制 `texteller/api/format.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留核心格式化功能：
   - `format_latex`: 主要的 LaTeX 格式化函数
   - `_format_latex`: 内部格式化逻辑
   - 所有辅助函数和数据结构
4. 适配新的配置管理方式

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **依赖模块缺失**：确保所有依赖的模块已正确迁移
- **配置适配**：需要适配新的配置管理方式
- **性能影响**：格式化功能可能影响推理性能，可考虑配置开关

### 3.6 `app/processing/texteller/latex_utils.py`

**目标文件用途**
提供 LaTeX 文本处理工具，包括样式移除、换行处理、文本替换等功能。

**迁移操作**
1. 直接复制 `texteller/utils/latex.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有核心功能：
   - `remove_style`: 移除 LaTeX 样式标记
   - `add_newlines`: 在适当位置添加换行符
   - `change_all`: 复杂的 LaTeX 命令替换
   - `_change`: 内部替换函数
   - `_find_substring_positions`: 查找子字符串位置

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **依赖缺失**：确保所有依赖的第三方库都已安装
- **功能完整性**：确保所有 LaTeX 处理功能正常工作

### 3.7 `app/processing/texteller/device_utils.py`

**目标文件用途**
提供设备管理工具，包括设备检测、设备选择等功能。

**迁移操作**
1. 直接复制 `texteller/utils/device.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有核心功能：
   - `get_device`: 自动检测最佳可用设备
   - `cuda_available`: 检查 CUDA 可用性
   - `mps_available`: 检查 MPS 可用性
   - `str2device`: 字符串转设备对象

**需要处理的风险**
- **功能重复**：与 app 现有设备管理功能可能重复
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **版本兼容性**：确保与现有 PyTorch 版本兼容

### 3.8 `app/processing/texteller/path_utils.py`

**目标文件用途**
提供路径管理工具，包括路径解析、目录创建等功能。

**迁移操作**
1. 直接复制 `texteller/utils/path.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有核心功能：
   - `resolve_path`: 解析路径
   - `touch`: 创建文件
   - `mkdir`: 创建目录
   - `rmfile`: 删除文件
   - `rmdir`: 删除目录

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **日志依赖**：需要适配现有的日志系统

### 3.9 `app/processing/texteller/misc_utils.py`

**目标文件用途**
提供杂项工具函数。

**迁移操作**
1. 直接复制 `texteller/utils/misc.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留核心功能：
   - `lines_dedent`: 文本缩进处理

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **功能简单**：风险较低

### 3.10 `app/processing/texteller/bbox_utils.py`

**目标文件用途**
提供边界框处理工具，包括边界框合并、冲突处理、图像切片等功能。

**迁移操作**
1. 直接复制 `texteller/utils/bbox.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有核心功能：
   - `mask_img`: 图像掩码处理
   - `bbox_merge`: 边界框合并（用于段落处理，**核心公式识别流程不使用**）
   - `split_conflict`: 冲突处理（用于段落处理，**核心公式识别流程不使用**）
   - `slice_from_image`: 图像切片（用于段落处理，**核心公式识别流程不使用**）
   - `draw_bboxes`: 绘制边界框

**需要处理的风险**
- **功能冲突**：与 app 现有边界框处理逻辑可能存在冲突
- **类型依赖**：依赖 `texteller.types.Bbox` 类型定义，必须确保 `types.py` 和 `bbox_types.py` 已正确迁移
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **使用场景**：主要用于段落处理（`paragraph2md`），核心公式识别流程（`img2latex`）不直接使用，但为保持完整性仍需迁移

### 3.11 `app/processing/formula_recognition.py` (修改)

**目标文件用途**
替换现有的公式识别实现，使用 texteller 引擎进行公式识别，保持与现有 API 的完全兼容。

**修改操作**
1. 保留现有的类结构和方法签名
2. 替换 `__init__` 方法中的模型初始化逻辑，使用 texteller 引擎
3. 修改 `recognize` 方法，调用 texteller 引擎进行推理
4. 保持输入输出格式完全一致，确保 API 兼容性
5. 更新日志记录，适配新的实现

**需要处理的风险**
- **API 兼容性**：确保输入输出格式与现有实现完全一致
- **性能影响**：监控推理速度，确保不低于现有性能
- **错误处理**：保持与现有错误处理机制一致
- **批处理逻辑**：确保批处理功能正常工作
- **依赖注入**：确保服务能正确通过依赖注入系统初始化

### 3.12 `app/processing/texteller/types.py`

**目标文件用途**
提供核心类型定义，包括基础类型和接口定义。

**迁移操作**
1. 直接复制 `texteller/types/__init__.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有类型定义，特别是：
   - `TexTellerModel` 类型：定义模型接口
   - 其他可能的类型定义

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **类型兼容性**：确保类型定义与现有代码兼容
- **依赖关系**：确保类型定义不引入不必要的依赖

### 3.13 `app/processing/texteller/bbox_types.py`

**目标文件用途**
提供边界框相关的类型定义，主要是 `Bbox` 类，用于边界框处理。

**迁移操作**
1. 直接复制 `texteller/types/bbox.py` 到目标位置
2. 修改导入路径，将 `texteller.` 前缀替换为相对导入或绝对导入
3. 保留所有类型定义，特别是：
   - `Bbox` 类：边界框表示
   - `Point` 类：点坐标表示
   - 其他相关类型

**需要处理的风险**
- **导入路径问题**：需要修改所有 `texteller.` 开头的导入语句
- **类型兼容性**：确保类型定义与现有代码兼容
- **依赖关系**：这些类型被 `bbox_utils.py` 直接依赖，必须确保正确迁移

## 4. 配置管理

### 4.1 新增配置文件：`app/assets/texteller_config.yaml`

```yaml
model:
  default_path: "path/to/texteller/model"
  use_onnx: false
  max_tokens: 512
  num_beams: 1
  no_repeat_ngram_size: 3

preprocessing:
  image_size: 224
  channels: 3
  normalize_mean: [0.485, 0.456, 0.406]
  normalize_std: [0.229, 0.224, 0.225]

postprocessing:
  output_format: "latex"
  keep_style: false
  confidence_threshold: 0.5

batch:
  size: 4
  timeout: 30.0
```

### 4.2 集成到现有配置：`app/core/config.py`

在现有配置类中添加 texteller 相关配置项：

```python
class Settings:
    # 现有配置...
    
    # Texteller 配置
    TEXTELLER_MODEL_PATH: str = "path/to/texteller/model"
    TEXTELLER_USE_ONNX: bool = False
    TEXTELLER_MAX_TOKENS: int = 512
    TEXTELLER_BATCH_SIZE: int = 4
```

## 5. 迁移实施要点

### 5.1 优先级策略
- **优先复制**：对于 texteller_model.py、model_loader.py、preprocessor.py、postprocessor.py、latex_utils.py、device_utils.py、path_utils.py、misc_utils.py、types.py、bbox_types.py、bbox_utils.py、constants.py 等核心文件，优先采用复制方式
- **谨慎重构**：只对 texteller_engine.py 进行重新实现，适配现有接口
- **风险控制**：保持原有实现逻辑，减少引入新问题的风险
- **依赖顺序**：先迁移基础类型定义（types.py、bbox_types.py），再迁移依赖这些类型的模块

### 5.2 测试验证
- 确保输出格式与现有 API 完全兼容


## 6. 迁移检查清单

- [ ] 创建 texteller 目录结构
- [ ] 复制 texteller_model.py（修改导入路径）
- [ ] 复制 model_loader.py（修改导入路径）
- [ ] 复制 preprocessor.py（适配输入输出格式）
- [ ] 复制 postprocessor.py（修改导入路径）
- [ ] 复制 latex_utils.py（修改导入路径）
- [ ] 复制 device_utils.py（修改导入路径）
- [ ] 复制 path_utils.py（修改导入路径）
- [ ] 复制 misc_utils.py（修改导入路径）
- [ ] 复制 types.py（修改导入路径）
- [ ] 复制 bbox_types.py（修改导入路径）
- [ ] 复制 bbox_utils.py（修改导入路径）
- [ ] 复制 constants.py
- [ ] 实现核心推理引擎
- [ ] 修改现有公式识别服务
- [ ] 更新配置文件
- [ ] 更新依赖注入
- [ ] 文档更新

## 7. 风险评估和缓解

### 7.1 主要风险
- **导入路径错误**：复制文件时可能遗漏修改导入路径
- **功能重复冲突**：device_utils.py 中可能存在与现有功能重复的函数
- **版本兼容性**：texteller 依赖的库版本可能与现有环境不兼容
- **边界框处理冲突**：bbox_utils.py 的边界框处理逻辑与 app 现有逻辑可能存在冲突
- **格式化性能影响**：postprocessor.py 的格式化功能可能影响推理性能
- **文件数量增加**：工具函数拆分为多个文件，增加了管理复杂度
- **类型定义依赖**：bbox_utils.py 依赖于 types.py 和 bbox_types.py，必须确保这些文件正确迁移
- **全局状态管理**：避免引入全局可变状态（如 globals.py 中的单例模式）

### 7.2 缓解措施
- 渐进式迁移，保持回滚能力
- 优先复制核心文件，减少重构风险
- 仔细检查导入路径和依赖关系
- 提供配置开关，允许禁用格式化功能以提升性能
- 隔离边界框处理逻辑，避免与现有功能冲突
- 建立清晰的模块依赖关系，避免循环导入
- 优先使用 latex_utils.py，其他工具文件按需使用
- 确保按正确的依赖顺序迁移文件：先迁移基础类型定义，再迁移依赖这些类型的模块
- 将 globals.py 中的配置值转换为静态配置，避免引入全局可变状态
 