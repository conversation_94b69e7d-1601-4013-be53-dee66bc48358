import unittest
import os
import json
import numpy as np
import cv2
from pathlib import Path
from unittest.mock import MagicMock, patch
from app.core.config import proj_dir, settings

from app.processing.table_recognition import TableRecognitionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService


class TestTableRecognitionService(unittest.TestCase):
    """TableRecognitionService的单元测试类"""
    
    @classmethod
    def setUpClass(cls):
        """在所有测试开始前执行一次，用于加载模型"""
        # 获取项目根目录
        cls.proj_dir = Path(__file__).parent.parent.parent
        cls.test_image_path = proj_dir / "app/assets/test02.png"
        
        with open(cls.test_image_path, "rb") as f:
            cls.test_image_bytes = f.read()
        
        # 创建文本识别服务
        cls.text_recognition = TextRecognitionService()
        cls.text_detection = TextDetectionService()
        
        # 创建配置
        cls.config = {
            "UNITABLE_ENCODER_WEIGHTS": settings.UNITABLE_ENCODER_WEIGHTS,
            "UNITABLE_DECODER_WEIGHTS": settings.UNITABLE_DECODER_WEIGHTS,
            "UNITABLE_VOCAB_PATH": settings.UNITABLE_VOCAB_PATH,
            "USE_CUDA": settings.USE_CUDA
        }
        
        # 创建表格识别服务
        cls.service = TableRecognitionService(cls.config, cls.text_recognition, cls.text_detection)
    
    @classmethod
    def tearDownClass(cls):
        """在所有测试结束后执行一次，用于清理资源"""
        # 如果有需要清理的资源，可以在这里添加
        pass
    
    def test_adapt_config(self):
        """测试_adapt_config方法，确保正确转换配置格式"""
        # 创建测试配置
        flat_config = {
            "UNITABLE_ENCODER_WEIGHTS": settings.UNITABLE_ENCODER_WEIGHTS,
            "UNITABLE_DECODER_WEIGHTS": settings.UNITABLE_DECODER_WEIGHTS,
            "UNITABLE_VOCAB_PATH": settings.UNITABLE_VOCAB_PATH,
            "USE_CUDA": False,
        }
        
        # 创建服务实例（只为了调用_adapt_config方法）
        text_recognition = TextRecognitionService()
        service = TableRecognitionService(flat_config, text_recognition)
        
        # 调用被测方法
        nested_config = service._adapt_config(flat_config)
        
        # 验证结果
        self.assertEqual(nested_config["model_path"]["encoder"], settings.UNITABLE_ENCODER_WEIGHTS)
        self.assertEqual(nested_config["model_path"]["decoder"], settings.UNITABLE_DECODER_WEIGHTS)
        self.assertEqual(nested_config["model_path"]["vocab"], settings.UNITABLE_VOCAB_PATH)
        self.assertEqual(nested_config["use_cuda"], False)
    
    def test_prepare_image(self):
        """测试_prepare_image方法，确保正确解码图像"""
        # 读取测试图像
        with open(self.test_image_path, "rb") as f:
            image_bytes = f.read()
        
        # 创建服务实例
        text_recognition = TextRecognitionService()
        service = TableRecognitionService(self.config, text_recognition)
        
        # 调用被测方法
        image = service._prepare_image(image_bytes)
        
        # 验证结果
        self.assertIsInstance(image, np.ndarray)
        self.assertEqual(len(image.shape), 3)  # 应该是三维数组 (高度, 宽度, 通道)
        self.assertEqual(image.shape[2], 3)    # 应该有3个通道 (BGR)
    
    def test_bbox_cropping_and_offset(self):
        """测试表格边界框裁剪和坐标偏移调整功能"""
        # 创建模拟的依赖项
        text_recognition = MagicMock()
        text_recognition.recognize.return_value = [{"words": "Test", "confidence": 0.95, "bbox": [10, 10, 50, 30]}]
        
        text_detection = MagicMock()
        text_detection.detect.return_value = [[10, 20, 60, 40]]
        
        # 创建模拟的unitable实例
        mock_unitable = MagicMock()
        mock_unitable.return_value = (
            ["<tr>", "<td></td>", "</tr>"],  # structure_str_list
            np.array([[10, 20, 60, 40]]),    # bboxes
            0.1                              # processing_time
        )
        
        # 创建模拟的matcher实例
        mock_matcher = MagicMock()
        mock_matcher.decode_logic_points.return_value = [[0, 0, 0, 0]]
        
        # 创建测试图像
        test_image = np.zeros((300, 400, 3), dtype=np.uint8)  # 300x400 黑色图像
        test_image_bytes = cv2.imencode('.png', test_image)[1].tobytes()
        
        service = TableRecognitionService(self.config, text_recognition, text_detection)
        service.unitable = mock_unitable
        service.matcher = mock_matcher
        
        # 定义测试表格边界框 [x_min, y_min, x_max, y_max]
        table_bbox = [50, 60, 250, 200]
        
        # 调用被测方法
        result = service.recognize_table(test_image_bytes, table_bbox)
        
        # 验证unitable是否被调用，并且传入的是裁剪后的图像
        args, _ = mock_unitable.call_args
        cropped_image = args[0]
        self.assertEqual(cropped_image.shape[0], 140)  # 高度应为 200-60=140
        self.assertEqual(cropped_image.shape[1], 200)  # 宽度应为 250-50=200
        
        # 验证text_detection是否被调用
        text_detection.detect.assert_called_once()
        
        # 验证text_recognition是否被调用
        text_recognition.recognize.assert_called_once()
    
    def test_convert_to_table_result(self):
        """测试_convert_to_table_result方法，确保正确转换结果格式"""
        # 创建服务实例
        text_recognition = TextRecognitionService()
        service = TableRecognitionService(self.config, text_recognition)
        
        # 创建测试数据
        structure_str_list = ["<tr>", "<td></td>", "</tr>"]
        bboxes = np.array([[10, 20, 100, 50]])
        text_results = [{"words": "Test", "confidence": 0.95, "bbox": [10, 20, 100, 50]}]
        
        # 调用被测方法
        result = service._convert_to_table_result(bboxes, structure_str_list, text_results)
        
        # 验证结果
        self.assertEqual(result["confidence"], 0.9)
        self.assertEqual(len(result["cells"]), 1)
        
        cell = result["cells"][0]
        self.assertEqual(cell["bbox"], [10, 20, 100, 50])
        self.assertEqual(cell["row_start"], 0)
        self.assertEqual(cell["col_start"], 0)
        self.assertEqual(cell["row_span"], 1)
        self.assertEqual(cell["col_span"], 1)
        self.assertEqual(cell["text"]["value"], "Test")
        self.assertEqual(cell["text"]["confidence"], 0.95)

    def test_recognize_table_test03(self):
        """测试对test03.png的表格识别结果是否与预期一致"""
        # 1. 准备测试数据
        test_image_path = proj_dir / "app/assets/test03.png"
        with open(test_image_path, "rb") as f:
            test_image_bytes = f.read()
            
        # 读取预期的JSON结果
        expected_result_path = proj_dir / "output/test03.png.json"
        with open(expected_result_path, "r", encoding="utf-8") as f:
            expected_result = json.load(f)
        
        # 2. 执行表格识别
        print("开始表格识别...")
        result = self.service.recognize_table(test_image_bytes)
        print(f"表格识别完成，识别到 {len(result.get('cells', []))} 个单元格")
        result_file_path = proj_dir / f"output/unittest03_result.json"
        
        result_file_path.parent.mkdir(exist_ok=True, parents=True)
        with open(result_file_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        # 3. 比较结果
        # 为了更准确地比较，我们将结果转换为集合后比较
        found_cells = []
        expected_cells = []
        
        # 提取识别结果的核心信息
        for cell in result.get("cells", []):
            found_cells.append({
                "row_start": cell.get("row_start"),
                "col_start": cell.get("col_start"),
                "row_span": cell.get("row_span"),
                "col_span": cell.get("col_span"),
                "text": cell.get("text")
            })
        
        # 提取预期结果的核心信息
        for cell in expected_result.get("cells", []):
            expected_cells.append({
                "row_start": cell.get("row_start"),
                "col_start": cell.get("col_start"),
                "row_span": cell.get("row_span"),
                "col_span": cell.get("col_span"),
                "text": cell.get("text")
            })
            
        # 比较单元格数量
        self.assertEqual(
            len(expected_cells), 
            len(found_cells), 
            f"单元格数量不匹配: 期望 {len(expected_cells)}, 实际 {len(found_cells)}"
        )
        
        # 如果单元格数量相同，则检查具体内容
        if len(expected_cells) == len(found_cells):
            # 按行列排序单元格以确保比较顺序一致
            def cell_sort_key(cell):
                return (cell["row_start"], cell["col_start"])
                
            expected_cells.sort(key=cell_sort_key)
            found_cells.sort(key=cell_sort_key)
            
            # 逐个比较单元格
            for i, (expected_cell, found_cell) in enumerate(zip(expected_cells, found_cells)):
                # 检查行列位置和跨度
                self.assertEqual(
                    expected_cell["row_start"], 
                    found_cell["row_start"], 
                    f"单元格 {i} 行起始位置不匹配: 期望 {expected_cell['row_start']}, 实际 {found_cell['row_start']}"
                )
                self.assertEqual(
                    expected_cell["col_start"], 
                    found_cell["col_start"], 
                    f"单元格 {i} 列起始位置不匹配: 期望 {expected_cell['col_start']}, 实际 {found_cell['col_start']}"
                )
                self.assertEqual(
                    expected_cell["row_span"], 
                    found_cell["row_span"], 
                    f"单元格 {i} 行跨度不匹配: 期望 {expected_cell['row_span']}, 实际 {found_cell['row_span']}"
                )
                self.assertEqual(
                    expected_cell["col_span"], 
                    found_cell["col_span"], 
                    f"单元格 {i} 列跨度不匹配: 期望 {expected_cell['col_span']}, 实际 {found_cell['col_span']}"
                )
                # 检查文本内容
                # self.assertEqual(
                #     expected_cell["text"], 
                #     found_cell["text"], 
                #     f"单元格 {i} 文本内容不匹配: 期望 '{expected_cell['text']}', 实际 '{found_cell['text']}'"
                # )
        else:
            # 如果单元格数量不同，直接输出差异以便调试
            print("\n===== 单元格内容对比 =====")
            print("预期单元格列表:")
            for cell in expected_cells:
                print(f"  行:{cell['row_start']} 列:{cell['col_start']} 跨行:{cell['row_span']} 跨列:{cell['col_span']} 文本:'{cell['text']}'")
            
            print("\n实际单元格列表:")
            for cell in found_cells:
                text_display = cell['text']['value'] if isinstance(cell['text'], dict) else cell['text']
                print(f"  行:{cell['row_start']} 列:{cell['col_start']} 跨行:{cell['row_span']} 跨列:{cell['col_span']} 文本:'{text_display}'")
            print("===========================")
        
        # 如果测试值与预期不一致，输出完整的识别结果以便调试
        # if found_cells != expected_cells:
        #     print("\n===== 表格识别完整结果 =====")
        #     print(json.dumps(result, ensure_ascii=False, indent=2))
        #     print("=============================")


if __name__ == "__main__":
    unittest.main() 