# rec_config_template.yaml

# ===================================================================
# 方向分类器配置
# 来源: ./configs/cls/default_onnx.yaml
# ===================================================================
TextClassifier:
#  model_path: "./configs/text_cls_default_paddle.onnx"
  # 分类器在原始 pipeline 中逐个处理，因此 batch_size 为 1
  batch_size: 4
  device_type: "cuda"
  device_id: 0
  
  # 预处理参数 (复用自 cls_pre_proc.py 和 default_onnx.yaml)
  pre_process:
    # 图像缩放尺寸 [H, W]，来自 default_onnx.yaml
    image_shape: [80, 160] 
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
    
  # 后处理参数 (复用自 cls_post_proc.py 和 default_onnx.yaml)
  post_process:
    # 标签列表，来自 default_onnx.yaml -> Global: label_map
    label_list: ["0", "180"]
    # 分类置信度阈值。此为新增配置项，提取自原始代码 cls_post_proc.py 中的硬编码值 0.9
    threshold: 0.9 

# ===================================================================
# 文本识别器配置
# 来源: ./configs/rec/bq_svtr_v2_7M_onnx.yaml
# ===================================================================
TextRecognizer:
#  model_path: "./configs/test_rec_bq_svtr_v2_7M.onnx"
  # 批量大小，来自 bq_svtr_v2_7M_onnx.yaml -> Global: infer_batch
  batch_size: 4
  device_type: "cuda"
  device_id: 0

  # 预处理参数 (复用自 rec_pre_proc.py 和 bq_svtr_v2_7M_onnx.yaml)
  pre_process:
    # 图像缩放尺寸 [C, H, W]
    image_shape: [3, 48, 320]
    mean: [0.5, 0.5, 0.5]
    std: [0.5, 0.5, 0.5]
    
  # 后处理参数 (复用自 rec_post_proc.py 和 bq_svtr_v2_7M_onnx.yaml)
  post_process:
#    character_dict_path: "./configs/bq_ocr_keys_v2.txt"
    use_space_char: true
    # 识别置信度阈值。此为新增配置项，提取自原始代码 ocr_pipeline.py 中用于二次识别的硬编码值 0.8
    threshold: 0.8


# ===================================================================
# Pipeline 流水线参数
# 来源: modules/pipelines/ocr_pipeline.py 中的默认参数
# ===================================================================
Pipeline:
  # 是否重复尝试不同旋转角度来识别文本，来自 ocr_pipeline.py 第56行
  repeat_rotate: true
  # OCR流水线整体置信度阈值，来自 ocr_pipeline.py 第57行
  pipeline_threshold: 0.8
  # 文本识别结果的置信度阈值，来自 recognizer.py 默认参数
  rec_conf_threshold: 0.75
  # 文本旋转方向预测的置信度阈值，来自 ocr_pipeline.py 第59行
  rotate_conf_threshold: 0.9
  # 全局旋转方向判断的置信度阈值，来自 ocr_pipeline.py 第60行
  global_rotate_threshold: 0.85
