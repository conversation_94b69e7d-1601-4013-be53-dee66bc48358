
class AppError(Exception):
    def __init__(self, code: str, msg: str=None, log: str=None):
        self.code = code
        self.msg = msg
        self.log = log
        if not log:
            self.log = msg
    
    def __str__(self) -> str:
        log = ""
        if self.log != self.msg:
            log = f" , log: {self.log}"

        return f"{self.__class__.__name__}: err_code: {self.code}, msg: {self.msg}{log}"

class ClientErr(AppError):
    pass

class ServerError(AppError):
    pass

class ErrCode:
    OK = "ok"

    SRV_INTERNAL = "srv/internal"

    CLIENT_IMAGE = "cli/img"
    CLIENT_NOT_FOUND = "cli/not_found"
    CLIENT_BAD_PARAMS = "cli/bad_params"
    CLIENT_FORBIDDEN = "cli/forbidden"