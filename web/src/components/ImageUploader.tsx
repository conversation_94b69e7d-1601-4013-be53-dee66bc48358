import React from 'react';
import { Upload, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';

const { Dragger } = Upload;

interface ImageUploaderProps {
  onUpload: (file: File) => void;
  isLoading: boolean;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({ onUpload, isLoading }) => {
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.jpg,.jpeg,.png,.bmp',
    disabled: isLoading,
    showUploadList: false,
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件！');
        return false;
      }

      // 检查文件大小（限制为50MB）
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        message.error('图片大小不能超过50MB！');
        return false;
      }

      // 调用父组件的上传处理函数
      onUpload(file);
      
      // 返回false阻止组件的默认上传行为
      return false;
    },
    onDrop: (e) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">
        {isLoading ? '正在处理中...' : '点击或拖拽图片到此区域上传'}
      </p>
      <p className="ant-upload-hint">
        支持JPG、PNG、BMP格式，文件大小不超过50MB
      </p>
    </Dragger>
  );
}; 