# OCR文本定位独立模块(ocr_det) - 概要设计文档 (修订版)

## 架构概览

系统采用**单文件统一封装**设计，所有处理逻辑集中在`OcrDet`类中：

- **统一接口层**: `OcrDet`类作为唯一入口，内部封装所有处理逻辑
- **内部方法层**: 将原始项目的各个处理组件作为类的私有方法
- **资源管理层**: 配置和模型文件的加载管理

```mermaid
sequenceDiagram
    participant Client
    participant OcrDet
    
    Client->>OcrDet: __init__()
    OcrDet->>OcrDet: _load_config()
    OcrDet->>OcrDet: _init_model()
    OcrDet-->>Client: initialized instance
    
    Client->>OcrDet: detect(image)
    OcrDet->>OcrDet: _preprocess(image)
    OcrDet->>OcrDet: _inference(preprocessed_data)
    OcrDet->>OcrDet: _postprocess(predictions)
    OcrDet-->>Client: List[List[List[int]]]
```

## 组件拆分(Components) - 基于代码复制来源

### 核心类设计
- **OcrDet**: 统一封装类
  - 职责：提供完整的文本检测功能
  - 文件位置：`ocr_det/ocr_det.py`

### 内部方法及其复制来源

#### 1. 初始化相关方法
- **`__init__(self)`** - 新封装方法
  - 复制来源：`modules/predictors/detector.py` 中的 `TextDetector.__init__`
  - 复制来源：`modules/predictors/base.py` 中的 `BaseOCR.__init__`
  - **必要变更**：合并两个初始化流程，移除config_path参数（改为硬编码路径）

- **`_load_config(self)`** - 直接复制
  - 复制来源：`modules/predictors/base.py` 中的 `_load_config` 方法
  - **必要变更**：硬编码配置文件路径，移除参数传递

- **`_init_model(self)`** - 直接复制
  - 复制来源：`modules/predictors/detector.py` 中的 `init_model` 方法
  - **无变更**：完全复制原始逻辑

#### 2. 预处理相关方法
- **`_preprocess(self, image)`** - 封装方法
  - 复制来源：`modules/utils/det_pre_proc.py` 中的 `DBDetPreProc.__call__` 方法
  - **必要变更**：从独立类方法改为OcrDet的成员方法

- **`_normalize(self, image)`** - 直接复制
  - 复制来源：`modules/utils/det_pre_proc.py` 中的相关方法
  - **无变更**：完全复制原始逻辑

#### 3. 推理相关方法
- **`_inference(self, input_data)`** - 封装方法
  - 复制来源：`modules/predictors/detector.py` 中的 `detect` 方法的推理部分
  - **必要变更**：提取推理逻辑为独立方法

#### 4. 后处理相关方法
- **`_postprocess(self, predictions, batch_data)`** - 封装方法
  - 复制来源：`modules/utils/det_post_proc.py` 中的 `DBPostProcess.__call__` 方法
  - **必要变更**：从独立类方法改为OcrDet的成员方法

- **`_boxes_from_bitmap(self, pred, bitmap, dest_width, dest_height)`** - 直接复制
  - 复制来源：`modules/utils/det_post_proc.py` 中的 `boxes_from_bitmap` 方法
  - **无变更**：完全复制原始逻辑

- **`_get_mini_boxes(self, contour)`** - 直接复制
  - 复制来源：`modules/utils/det_post_proc.py` 中的 `get_mini_boxes` 方法
  - **无变更**：完全复制原始逻辑

- **`_box_score_fast(self, pred, box)`** - 直接复制
  - 复制来源：`modules/utils/det_post_proc.py` 中的 `box_score_fast` 方法
  - **无变更**：完全复制原始逻辑

- **`_unclip(self, box)`** - 直接复制
  - 复制来源：`modules/utils/det_post_proc.py` 中的 `unclip` 方法
  - **无变更**：完全复制原始逻辑

#### 5. 工具方法
- **`_create_onnx_session(self, model_path)`** - 直接复制
  - 复制来源：`modules/predictors/base.py` 中的 `create_onnx_session` 方法
  - **无变更**：完全复制原始逻辑

## 目录结构树(Directory Tree)

```
ocr_det/
├── __init__.py                 # 模块入口，导出OcrDet类
├── ocr_det.py                  # 统一实现文件，包含所有逻辑
├── config/
│   └── det_config.yaml         # 硬编码配置文件
├── models/
│   └── det_model.onnx          # ONNX检测模型
└── requirements.txt            # 依赖清单
```

## 数据流(Data Flow) - 基于原始调用链

**核心场景**: 单张图像文本检测流程（严格按照原始调用链）

```mermaid
sequenceDiagram
    participant Client
    participant OcrDet
    
    Note over OcrDet: 初始化阶段
    Client->>OcrDet: __init__()
    OcrDet->>OcrDet: _load_config() [复制自BaseOCR._load_config]
    OcrDet->>OcrDet: _init_model() [复制自TextDetector.init_model]
    OcrDet->>OcrDet: _create_onnx_session() [复制自BaseOCR.create_onnx_session]
    
    Note over OcrDet: 检测阶段
    Client->>OcrDet: detect(image)
    OcrDet->>OcrDet: _preprocess(image) [复制自DBDetPreProc.__call__]
    OcrDet->>OcrDet: _inference(data) [复制自TextDetector.detect推理部分]
    OcrDet->>OcrDet: _postprocess(preds) [复制自DBPostProcess.__call__]
    OcrDet->>OcrDet: _boxes_from_bitmap() [复制自DBPostProcess.boxes_from_bitmap]
    OcrDet-->>Client: List[List[List[int]]]
```

## 数据模型设计(Data Model Design)

```mermaid
erDiagram
    OcrDet {
        dict config
        object session
        string model_path
        dict preprocess_config
        dict postprocess_config
    }
    
    INPUT_IMAGE {
        ndarray raw_image
        tuple original_shape
        ndarray preprocessed_tensor
    }
    
    INFERENCE_RESULT {
        ndarray prediction_maps
        ndarray binary_mask
        list contours
    }
    
    TEXT_BOXES {
        list coordinates
        int x1
        int y1
        int x2
        int y2
        int x3
        int y3
        int x4
        int y4
    }
    
    OcrDet ||--|| INPUT_IMAGE : processes
    INPUT_IMAGE ||--|| INFERENCE_RESULT : transforms_to
    INFERENCE_RESULT ||--o{ TEXT_BOXES : extracts
```

## API接口定义

### 公开接口
- **`OcrDet.__init__()`**
  - 方法：构造函数
  - 说明：无参数初始化，内部硬编码配置路径

- **`OcrDet.detect(image: numpy.ndarray) -> List[List[List[int]]]`**
  - 方法：核心检测接口
  - 说明：输入BGR图像，返回四点坐标框列表

### 内部方法（私有）
所有以`_`开头的方法均为内部实现，不对外暴露

## 必要的代码变更说明

### 1. 初始化流程合并
**变更原因**: 原始代码中`TextDetector.__init__`和`BaseOCR.__init__`是继承关系，需要合并为单一初始化方法
**变更内容**: 
- 将两个`__init__`方法的逻辑合并到`OcrDet.__init__`
- 移除config_path参数，改为硬编码路径

### 2. 类方法转换为实例方法
**变更原因**: 原始代码中`DBDetPreProc`和`DBPostProcess`是独立类，需要转换为`OcrDet`的成员方法
**变更内容**:
- 将`DBDetPreProc.__call__`转换为`OcrDet._preprocess`
- 将`DBPostProcess.__call__`转换为`OcrDet._postprocess`
- 保持所有内部逻辑完全不变

### 3. 配置加载路径硬编码
**变更原因**: 需求要求硬编码配置文件路径
**变更内容**:
- 在`_load_config`方法中硬编码配置文件路径
- 路径值需要通过分析原始项目确定

### 4. 推理逻辑提取
**变更原因**: 将`TextDetector.detect`方法中的推理部分提取为独立方法，便于代码组织
**变更内容**:
- 提取ONNX推理调用为`_inference`方法
- 保持推理逻辑完全不变

## 迭代演进依据

### 1. 代码复制保障
- **来源明确**: 每个方法都明确标注复制来源，确保逻辑一致性
- **变更最小**: 仅进行必要的封装变更，核心逻辑保持不变
- **行为一致**: 所有场景下与原始功能保持完全一致

### 2. 单文件优势
- **部署简单**: 单文件模块便于集成和部署
- **依赖清晰**: 所有逻辑集中，依赖关系明确
- **维护方便**: 统一文件便于代码维护和问题定位

### 3. 扩展性考虑
- **方法独立**: 各处理步骤方法化，便于单独优化
- **配置灵活**: 配置文件外置，支持参数调整
- **模型替换**: ONNX会话封装便于模型升级

---

**关键确认**: 本设计严格遵循"代码复制为主"原则，所有核心逻辑都有明确的复制来源，仅进行必要的封装变更以实现统一文件设计目标。
