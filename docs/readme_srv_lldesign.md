## 项目结构与总体设计 (Project Structure & Overall Design)

### 目录结构树 (Directory Tree)

```
ocr_system/
├── app/                  # FastAPI 应用核心代码
│   ├── __init__.py
│   ├── main.py           # FastAPI 应用入口，Uvicorn 启动点
│   │
│   ├── api/              # API 表示层
│   │   ├── __init__.py
│   │   └── endpoints/
│   │       ├── __init__.py
│   │       ├── ocr.py      # 定义所有 POST /api/v1/ocr/* 路由
│   │       ├── results.py  # 定义 GET /api/v1/results/{request_id} 路由
│   │       ├── images.py   # 定义 GET /api/v1/images/{request_id} 路由
│   │       └── visualizations.py  # 定义 GET /api/v1/visualizations/{request_id} 路由
│   │
│   ├── services/         # 应用层
│   │   ├── __init__.py
│   │   ├── orchestration.py # 核心编排服务逻辑
│   │   ├── persistence.py   # 结果持久化服务逻辑
│   │   ├── image_service.py # 图片存储和获取服务
│   │   └── visualization_service.py # 可视化结果生成服务
│   │
│   ├── processing/       # 处理层，封装模型调用
│   │   ├── __init__.py
│   │   ├── text_detection.py
│   │   ├── text_recognition.py
│   │   ├── layout_analysis.py
│   │   ├── table_recognition.py
│   │   └── formula_recognition.py
│   │
│   ├── schemas/          # Pydantic 数据模型
│   │   ├── __init__.py
│   │   └── ocr_results.py # 定义 API 输入和输出的 JSON 结构
│   │
│   ├── utils/            # 工具函数模块
│   │   ├── image_locator.py # 图像坐标处理工具
│   │   └── visualization.py # 可视化工具函数
│   │
│   └── core/             # 共享配置、工具等
│       ├── __init__.py
│       └── config.py     # 应用配置 (如结果存储路径)
│
├── results/              # 存放OCR结果的JSON文件
│   └── .gitignore
│
├── images/               # 存放原始上传图片
│   └── .gitignore
│
├── visualizations/       # 存放生成的可视化结果图
│   └── .gitignore
│
├── static/               # 存放编译后的 React 前端文件
│   ├── index.html
│   └── assets/
│
├── tests/                # 测试代码
│   ├── __init__.py
│   └── test_api.py
│
├── poetry.lock
└── pyproject.toml
```

---

### 整体逻辑和交互时序图 (Overall Logic & Sequence Diagram)

-   **核心工作流程**: 整个系统的核心流程始于 `main.py` 注册的 API 端点。当一个 `/api/v1/ocr/full_result` 请求到达时，`ocr.py` 中的路由函数被触发。该函数首先解析和验证请求体，然后调用 `orchestration.py` 中的 `OrchestrationService`。编排服务负责整个业务逻辑，它生成一个唯一的请求ID，按顺序调用 `processing/` 目录下的各个处理服务（如`TextDetectionService`, `TextRecognitionService`等）来执行模型推理。获得所有中间结果后，编排服务将其整合成最终的JSON结构，并调用 `persistence.py` 中的 `PersistenceService` 将结果以 `{request_id}.json` 的格式保存到磁盘。最后，将最终的JSON结果返回给API层，再由API层响应给客户端。

-   **Mermaid `sequenceDiagram`**:
    ```mermaid
    sequenceDiagram
        participant main as main.py
        participant ocr_endpoint as api/endpoints/ocr.py
        participant orchestration as services/orchestration.py
        participant processing as processing/*.py
        participant persistence as services/persistence.py

        main->>ocr_endpoint: uvicorn anropft und registriert Router
        Note over main, ocr_endpoint: (Application Startup)

        Client->>+ocr_endpoint: POST /api/v1/ocr/full_result (request_body)
        ocr_endpoint->>+orchestration: process_full_result(image_bytes=decoded_image)
        orchestration->>orchestration: request_id = uuid.uuid4()
        orchestration->>+processing: detect_text(image_bytes)
        processing-->>-orchestration: text_bboxes
        orchestration->>+processing: recognize_text(image_bytes, text_bboxes)
        processing-->>-orchestration: recognized_texts
        orchestration->>+processing: ... (weitere Verarbeitungsdienste)
        processing-->>-orchestration: ... (weitere Ergebnisse)
        orchestration->>orchestration: final_json = format_results(request_id, ...)
        orchestration->>+persistence: save_result(request_id, final_json)
        persistence-->>-orchestration: True
        orchestration-->>-ocr_endpoint: final_json_model
        ocr_endpoint-->>-Client: 200 OK (final_json_model)
    ```

---

### 数据库表结构深化 (Detailed Database Schema)

本系统在 MVP 阶段不使用数据库，而是采用文件系统进行持久化。因此，本节不适用。

---

### 配置项 (Configuration)

配置项将通过 Pydantic 的 `BaseSettings` 进行管理，允许从环境变量或 `.env` 文件加载。

| 环境变量/参数  | 文件 (`app/core/config.py`) | 描述                           | 默认值          |
| :------------- | :-------------------------- | :----------------------------- | :-------------- |
| `RESULTS_DIR`  | `Settings.RESULTS_DIR`      | 存储 OCR 结果 JSON 文件的目录。 | `./results/`    |
| `IMAGES_DIR`   | `Settings.IMAGES_DIR`       | 存储原始上传图片的目录。       | `./images/`     |
| `VISUALIZATIONS_DIR` | `Settings.VISUALIZATIONS_DIR` | 存储可视化结果图的目录。   | `./visualizations/` |
| `API_V1_STR`   | `Settings.API_V1_STR`       | API 版本前缀。                 | `/api/v1`       |

---

## 模块化文件详解 (File-by-File Breakdown)

### `app/core/config.py`

a. **文件用途说明**
管理应用的全局配置。使用 Pydantic 的 `BaseSettings`，可以方便地从环境变量中读取配置，并提供默认值。

b. **文件内容概览 (类与函数)**
-   `Settings` (class): 继承自 `pydantic_settings.BaseSettings`，定义所有配置变量。
-   `settings` (instance): `Settings` 类的一个全局实例，供应用其他部分导入和使用。

c. **文件内类图 (Mermaid `classDiagram`)**
```mermaid
classDiagram
    class BaseSettings {
        <<pydantic_settings>>
    }
    class Settings {
        +str RESULTS_DIR
        +str API_V1_STR
    }
    BaseSettings <|-- Settings
```

#### 函数/方法详解
该文件主要定义配置类，不包含复杂函数。`Settings` 类会自动从环境变量加载同名（不区分大小写）的变量值。

### `app/schemas/ocr_results.py`

a. **文件用途说明**
定义所有与 API 交互相关的 Pydantic 数据模型。这包括 API 请求体、响应体以及嵌套在其中的复杂数据结构。这确保了数据在进出 API 时是类型安全和结构正确的。

b. **文件内容概览 (类与函数)**
定义一系列继承自 `pydantic.BaseModel` 的类，以匹配 HLD 中 ERD 图定义的逻辑结构。
-   `OCRRequest`: API `POST` 请求的输入模型。
-   `TextRecognitionItem`: 单个文本识别结果。
-   `TableCell`: 表格中的单个单元格。
-   `TableContent`: 结构化的表格内容。
-   `FormulaContent`: 公式识别结果。
-   `LayoutAnalysisItem`: 单个版面分析结果，可能包含表格或公式内容。
-   `BaseResponse`: 所有 OCR 响应的基类，包含 `request_id`。
-   `TextDetectionResponse`, `TextRecognitionResponse`, `LayoutAnalysisResponse`, `TableRecognitionResponse`, `FormulaRecognitionResponse`, `FullOCRResponse`: 对应每个 API 端点的具体响应模型。

c. **文件内类图 (Mermaid `classDiagram`)**
```mermaid
classDiagram
    direction LR
    class BaseModel {
        <<pydantic>>
    }

    class OCRRequest {
        +str image_base64
        +str filename
    }

    class BaseResponse {
        +UUID request_id
    }

    class FullOCRResponse {
        +List~LayoutAnalysisItem~ layout
        +List~TextRecognitionItem~ text_lines
    }

    class LayoutAnalysisItem {
        +List~int~ bbox
        +str type
        +float confidence
        +Optional~TableContent~ table_content
        +Optional~FormulaContent~ formula_content
    }

    class TextRecognitionItem {
        +List~int~ bbox
        +str words
        +float confidence
    }

    class TableContent {
       +List~TableCell~ cells
    }
    
    class FormulaContent {
        +str latex_string
    }

    BaseModel <|-- OCRRequest
    BaseModel <|-- BaseResponse
    BaseResponse <|-- FullOCRResponse
    BaseModel <|-- LayoutAnalysisItem
    BaseModel <|-- TextRecognitionItem
    BaseModel <|-- TableContent
    BaseModel <|-- FormulaContent

    FullOCRResponse "1" o-- "*" LayoutAnalysisItem
    FullOCRResponse "1" o-- "*" TextRecognitionItem
    LayoutAnalysisItem "1" o-- "0..1" TableContent
    LayoutAnalysisItem "1" o-- "0..1" FormulaContent
```

### `app/services/persistence.py`

a. **文件用途说明**
封装所有与磁盘读写相关的持久化逻辑。它提供了一个简单的服务，用于根据 `request_id` 保存和检索 JSON 结果文件。

b. **文件内容概览 (类与函数)**
-   `PersistenceService` (class): 管理结果文件的读写。

c. **文件内类图 (Mermaid `classDiagram`)**
```mermaid
classDiagram
    class PersistenceService {
        -Path results_dir
        +__init__(results_dir: str)
        +save_result(request_id: UUID, result_data: BaseModel) bool
        +get_result(request_id: UUID) dict
    }
```

#### 函数/方法详解
-   **`__init__(self, results_dir: str)`**
    -   **输入参数**: `results_dir` (str) - 从配置中传入的结果存储目录路径。
    -   **输出**: None.
    -   **实现步骤**:
        1.  将 `results_dir` 字符串转换为 `pathlib.Path` 对象，并存储为 `self.results_dir`。
        2.  调用 `self.results_dir.mkdir(parents=True, exist_ok=True)` 确保目录存在。

-   **`save_result(self, request_id: UUID, result_data: BaseModel) -> bool`**
    -   **输入参数**:
        -   `request_id` (UUID): 请求的唯一标识符。
        -   `result_data` (BaseModel): Pydantic 模型实例，待保存的数据。
    -   **输出**: `bool` - `True` 表示保存成功。
    -   **实现步骤**:
        1.  构造文件路径: `file_path = self.results_dir / f"{request_id}.json"`。
        2.  将 Pydantic 模型 `result_data` 序列化为 JSON 字符串。推荐使用 `result_data.model_dump_json(indent=2)`。
        3.  使用 `file_path.write_text(json_string, encoding='utf-8')` 将 JSON 字符串写入文件。
        4.  返回 `True`。在实际应用中可以加入 `try...except` 块来处理 IO 错误。

-   **`get_result(self, request_id: UUID) -> dict`**
    -   **输入参数**: `request_id` (UUID): 要检索的请求ID。
    -   **输出**: `dict` - 从 JSON 文件读取的内容。
    -   **实现步骤**:
        1.  构造文件路径: `file_path = self.results_dir / f"{request_id}.json"`。
        2.  检查文件是否存在: `if not file_path.exists():`。如果不存在，则引发 `FastAPI.HTTPException(status_code=404, detail="Result not found")`。
        3.  读取文件内容: `json_string = file_path.read_text(encoding='utf-8')`。
        4.  使用 `json.loads(json_string)` 将 JSON 字符串解析为 Python 字典。
        5.  返回该字典。

### `app/processing/*.py` (通用模板)

a. **文件用途说明**
每个 `processing` 文件（如 `text_detection.py`, `table_recognition.py` 等）都封装了一个独立的 AI 模型调用。在 MVP 阶段，这些文件将包含模拟（mock）实现，返回符合其 Pydantic 输出模型的硬编码数据。这允许在没有实际模型的情况下进行完整的端到端集成测试。

b. **文件内容概览 (类与函数)**
-   `<ServiceName>Service` (class): 例如 `TextDetectionService`, `TableRecognitionService`。
-   `detect`/`recognize`/`analyze` (method): 执行核心处理的公共方法。

c. **文件内类图 (Mermaid `classDiagram` - 以 `TextDetectionService` 为例)**
```mermaid
classDiagram
    class TextDetectionService {
        +detect(image_bytes: bytes) List~List~int~~
    }
```

#### 函数/方法详解 (以 `text_detection.py` 的 `detect` 为例)
-   **`detect(self, image_bytes: bytes) -> list[list[int]]`**
    -   **输入参数**: `image_bytes` (bytes): 从 Base64 字符串解码后的原始图像二进制数据。
    -   **输出**: `list[list[int]]` - 一个列表，其中每个元素是代表一个边界框（bbox）的列表 `[x_min, y_min, x_max, y_max]`。
    -   **实现步骤 (MVP Mock)**:
        1.  （可选）可以使用 `PIL.Image.open(io.BytesIO(image_bytes))` 来验证图像数据是否有效。
        2.  打印一条日志，表明该函数已被调用，例如 `logging.info("Mocking text detection...")`。
        3.  返回一个硬编码的边界框列表，例如 `return [[10, 10, 100, 30], [10, 40, 200, 60]]`。
        4.  **注意**: 其他处理服务（如 `text_recognition.py`）的方法将遵循类似的模式，接受图像字节和必要的附加上下文（如边界框），并返回其定义的 Pydantic 模型或数据结构的模拟实例。

### `app/services/orchestration.py`

a. **文件用途说明**
系统的核心业务逻辑层。它负责编排对不同处理服务的调用，管理数据流，并最终将结果组装成统一的响应格式。

b. **文件内容概览 (类与函数)**
-   `OrchestrationService` (class): 包含所有处理流程的方法。

c. **文件内类图 (Mermaid `classDiagram`)**
```mermaid
classDiagram
    class OrchestrationService {
        -PersistenceService persistence_service
        -TextDetectionService text_detection_service
        -TextRecognitionService text_recognition_service
        -LayoutAnalysisService layout_analysis_service
        -TableRecognitionService table_recognition_service
        -FormulaRecognitionService formula_recognition_service
        +__init__(...)
        +process_full_result(image_bytes: bytes) FullOCRResponse
        +process_text_detection(image_bytes: bytes) TextDetectionResponse
        +process_text_recognition(image_bytes: bytes) TextRecognitionResponse
        -_decode_base64_image(image_base64: str) bytes
    }
```

#### 函数/方法详解
-   **`__init__(self, ...)`**
    -   **输入参数**: 所有处理服务和持久化服务的实例（通过依赖注入传入）。
    -   **输出**: None.
    -   **实现步骤**:
        1.  将传入的服务实例赋值给类成员变量，例如 `self.persistence_service = persistence_service`。

-   **`_decode_base64_image(self, image_base64: str) -> bytes`**
    -   **输入参数**: `image_base64` (str): 原始的 base64 编码字符串（可能包含 `data:image/...;base64,` 前缀）。
    -   **输出**: `bytes` - 纯净的图像二进制数据。
    -   **实现步骤**:
        1.  检查字符串是否包含 `,`。如果包含，则只取逗号后面的部分。
        2.  使用 `base64.b64decode()` 解码字符串。
        3.  返回解码后的字节。
        4.  在 `try...except` 块中捕获 `Exception`，如果解码失败则引发 `ClientErr`。

-   **`process_full_result(self, image_base64: str) -> schemas.FullOCRResponse`**
    -   **输入参数**: `image_base64` (str): Base64 编码的图像。
    -   **输出**: `schemas.FullOCRResponse` - 一个 Pydantic 模型实例，包含完整的 OCR 结果。
    -   **实现步骤**:
        1.  **生成ID**: `request_id = uuid.uuid4()`。
        2.  **解码图像**: `image_bytes = self._decode_base64_image(image_base64)`。
        3.  **调用处理层**:
            -   `layout_blocks_raw = self.layout_analysis_service.analyze(image_bytes)`
            -   `text_bboxes = self.text_detection_service.detect(image_bytes)`
            -   `text_lines_raw = self.text_recognition_service.recognize(image_bytes, text_bboxes)`
        4.  **组装结果**:
            -   初始化 `final_layout_items = []`。
            -   遍历 `layout_blocks_raw`。
            -   对于每个 `block`：
                -   创建一个 `LayoutAnalysisItem` 的基础实例。
                -   如果 `block.type == 'table'`，调用 `self.table_recognition_service.recognize(image_bytes, block.bbox)` 并将结果填充到 `LayoutAnalysisItem` 的 `table_content` 字段。
                -   如果 `block.type == 'formula'`，调用 `self.formula_recognition_service.recognize(image_bytes, block.bbox)` 并将结果填充到 `formula_content` 字段。
                -   将完整的 `LayoutAnalysisItem` 添加到 `final_layout_items`。
        5.  **创建响应模型**: `final_result = schemas.FullOCRResponse(request_id=request_id, layout=final_layout_items, text_lines=text_lines_raw)`。
        6.  **持久化**: `self.persistence_service.save_result(request_id, final_result)`。
        7.  **返回**: `return final_result`。
        8.  **其他 `process_*` 方法**: 将遵循类似的简化流程，只调用所需的处理服务，组装对应的响应模型，然后保存并返回。

### `app/api/endpoints/ocr.py`

a. **文件用途说明**
定义所有处理 OCR 任务的 `POST` API 端点。它负责接收请求、调用编排服务并返回结果。

b. **文件内容概览 (类与函数)**
-   `router` (instance): 一个 `fastapi.APIRouter` 实例。
-   `full_result`, `text_detection` 等: 每个端点对应一个异步函数。

#### 函数/方法详解
-   **`async def full_result(request: schemas.OCRRequest, service: OrchestrationService = Depends(...)) -> schemas.FullOCRResponse:`**
    -   **输入参数**:
        -   `request` (`schemas.OCRRequest`): FastAPI 会自动解析请求体并用 Pydantic 模型进行验证。
        -   `service` (`OrchestrationService`): 使用 FastAPI 的依赖注入系统获取 `OrchestrationService` 的实例。
    -   **输出**: `schemas.FullOCRResponse` - Pydantic 模型会被 FastAPI 自动序列化为 JSON 响应。
    -   **实现步骤**:
        1.  调用编排服务的方法：`result = service.process_full_result(image_base64=request.image_base64)`。
        2.  返回结果：`return result`。
    -   **注意**: 所有其他 `POST` 端点将遵循完全相同的结构，只是调用 `OrchestrationService` 上不同的 `process_*` 方法并期望不同的响应模型。

### `app/api/endpoints/results.py`

a. **文件用途说明**
定义用于根据 `request_id` 查询历史 OCR 结果的 `GET` 端点。

b. **文件内容概览 (类与函数)**
-   `router` (instance): 一个 `fastapi.APIRouter` 实例。
-   `get_ocr_result`: 获取结果的异步函数。

#### 函数/方法详解
-   **`async def get_ocr_result(request_id: UUID, service: PersistenceService = Depends(...)) -> Any:`**
    -   **输入参数**:
        -   `request_id` (UUID): 从 URL 路径中提取的请求ID。
        -   `service` (`PersistenceService`): 通过依赖注入获取 `PersistenceService` 实例。
    -   **输出**: `Any` (实际上是 `dict` 或 `JSONResponse`) - 返回从文件读取的 JSON 内容。
    -   **实现步骤**:
        1.  调用持久化服务的方法：`result_data = service.get_result(request_id)`。
        2.  FastAPI 会自动处理 `HTTPException`（如果 `get_result` 内部抛出）。
        3.  返回结果：`return result_data`。

### `app/api/endpoints/images.py`

a. **文件用途说明**
定义用于根据 `request_id` 获取原始上传图片的 `GET` 端点。

b. **文件内容概览 (类与函数)**
-   `router` (instance): 一个 `fastapi.APIRouter` 实例。
-   `get_original_image`: 获取原始图片的异步函数。

#### 函数/方法详解
-   **`async def get_original_image(request_id: UUID, service: ImageService = Depends(...)) -> FileResponse:`**
    -   **输入参数**:
        -   `request_id` (UUID): 从 URL 路径中提取的请求ID。
        -   `service` (`ImageService`): 通过依赖注入获取 `ImageService` 实例。
    -   **输出**: `FileResponse` - 返回图片文件的二进制响应。
    -   **实现步骤**:
        1.  调用图片服务的方法：`image_path = service.get_image_path(request_id)`。
        2.  检查文件是否存在，如果不存在抛出 `HTTPException(404)`。
        3.  返回 `FileResponse`：`return FileResponse(image_path, media_type="image/*")`。

### `app/api/endpoints/visualizations.py`

a. **文件用途说明**
定义用于根据 `request_id` 和 `view_type` 获取可视化结果图的 `GET` 端点。

b. **文件内容概览 (类与函数)**
-   `router` (instance): 一个 `fastapi.APIRouter` 实例。
-   `get_visualization_image`: 获取可视化结果图的异步函数。

#### 函数/方法详解
-   **`async def get_visualization_image(request_id: UUID, view_type: str, service: VisualizationService = Depends(...)) -> FileResponse:`**
    -   **输入参数**:
        -   `request_id` (UUID): 从 URL 路径中提取的请求ID。
        -   `view_type` (str): 从查询参数中获取的视图类型。
        -   `service` (`VisualizationService`): 通过依赖注入获取 `VisualizationService` 实例。
    -   **输出**: `FileResponse` - 返回可视化结果图的二进制响应。
    -   **实现步骤**:
        1.  调用可视化服务的方法：`visualization_path = service.get_or_create_visualization(request_id, view_type)`。
        2.  检查文件是否存在，如果不存在或生成失败抛出 `HTTPException(404)`。
        3.  返回 `FileResponse`：`return FileResponse(visualization_path, media_type="image/png")`。

### `app/services/image_service.py`

a. **文件用途说明**
封装原始图片的存储和获取逻辑。负责将上传的图片保存到磁盘，并提供根据 `request_id` 获取图片的功能。

b. **文件内容概览 (类与函数)**
-   `ImageService` (class): 管理图片文件的存储和获取。

#### 函数/方法详解
-   **`__init__(self, images_dir: str)`**
    -   **输入参数**: `images_dir` (str) - 从配置中传入的图片存储目录路径。
    -   **实现步骤**: 创建目录并确保其存在。

-   **`save_image(self, request_id: UUID, image_bytes: bytes) -> bool`**
    -   **输入参数**: `request_id` (UUID), `image_bytes` (bytes) - 图片的二进制数据。
    -   **输出**: `bool` - 保存是否成功。
    -   **实现步骤**: 将图片二进制数据保存为 `{request_id}.jpg` 文件。

-   **`get_image_path(self, request_id: UUID) -> Path`**
    -   **输入参数**: `request_id` (UUID) - 请求ID。
    -   **输出**: `Path` - 图片文件的路径。
    -   **实现步骤**: 返回对应的图片文件路径，如果文件不存在则抛出异常。

### `app/services/visualization_service.py`

a. **文件用途说明**
封装可视化结果图的生成和获取逻辑。根据OCR结果和视图类型，生成相应的可视化图片。

b. **文件内容概览 (类与函数)**
-   `VisualizationService` (class): 管理可视化结果图的生成和获取。

#### 函数/方法详解
-   **`__init__(self, visualizations_dir: str, persistence_service: PersistenceService, image_service: ImageService)`**
    -   **输入参数**: 可视化目录路径、持久化服务、图片服务实例。
    -   **实现步骤**: 初始化服务依赖和目录。

-   **`get_or_create_visualization(self, request_id: UUID, view_type: str) -> Path`**
    -   **输入参数**: `request_id` (UUID), `view_type` (str) - 请求ID和视图类型。
    -   **输出**: `Path` - 可视化结果图的路径。
    -   **实现步骤**: 
        1. 检查是否已存在对应的可视化图片文件。
        2. 如果不存在，根据OCR结果和视图类型生成新的可视化图片。
        3. 返回图片文件路径。

-   **`_generate_visualization(self, request_id: UUID, view_type: str, ocr_result: dict, original_image_path: Path) -> Path`**
    -   **输入参数**: 请求ID、视图类型、OCR结果、原图路径。
    -   **输出**: `Path` - 生成的可视化图片路径。
    -   **实现步骤**: 根据不同的view_type生成相应的可视化效果，具体包括：
        - **文字定位**: 在原图上绘制所有识别出的文本行边框
        - **文字识别**: 在白底图上绘制识别出的文字
        - **版面分析**: 在原图上绘制不同颜色的透明蒙版，标注版面分析类型
        - **表格识别**: 在白底图上绘制表格边框和单元格内文字
        - **公式识别**: 在白底图上显示识别出的公式结果
        - **最终组合结果**: 在白底图上绘制文字、公式、表格，添加版面分析蒙版，并将原图中的图片区域裁剪并贴到相应位置
        
        实现上将使用utils/visualization.py中的绘制函数，如draw_rect_text和draw_quad等。

### `app/main.py`

a. **文件用途说明**
应用的入口点。它负责创建 FastAPI 应用实例，挂载所有 API 路由器，并配置静态文件服务。

b. **文件内容概览 (类与函数)**
-   `app` (instance): `FastAPI()` 的主应用实例。
-   （隐式）Uvicorn 将运行此文件中的 `app` 对象。

#### 函数/方法详解
该文件内容主要是声明式的配置代码。
-   **实现步骤**:
    1.  导入 `FastAPI`, `ocr` 路由器, `results` 路由器, `config`。
    2.  创建 FastAPI 实例: `app = FastAPI()`。
    3.  包含 API 路由器:
        -   `app.include_router(ocr.router, prefix=settings.API_V1_STR, tags=["OCR Processing"])`
        -   `app.include_router(results.router, prefix=settings.API_V1_STR, tags=["Results"])`
        -   `app.include_router(images.router, prefix=settings.API_V1_STR, tags=["Images"])`
        -   `app.include_router(visualizations.router, prefix=settings.API_V1_STR, tags=["Visualizations"])`
    4.  挂载静态文件服务: `app.mount("/", StaticFiles(directory="static", html=True), name="static")`。这将服务于 `static` 目录下的 `index.html` 和其他前端资源。
    5.  可以添加一个根路径的健康检查端点:
        ```python
        @app.get("/health")
        async def health_check():
            return {"status": "ok"}
        ```

### `app/utils/visualization.py`

a. **文件用途说明**
提供通用的可视化工具函数，用于在图像上绘制文本框、标签、多边形等元素，支持透明效果和自适应文本大小。这些函数被visualization_service.py调用，用于生成不同类型的可视化结果。

b. **文件内容概览 (类与函数)**
- 颜色处理：`create_transparent_color`, `get_category_color`, `is_transparent`
- 绘制函数：`draw_label`, `draw_rect_text`, `draw_quad`
- 文本处理：`wrap_text_by_chars`, `fit_text_to_box`
- 字体处理：`get_font`
- 图像处理：`new_overlay`, `calculate_scale_factors`, `scale_bbox`

#### 函数/方法详解
- **`get_font(font_path: str=None, font_size: int=12) -> ImageFont.FreeTypeFont`**
  - **输入参数**: `font_path` (可选字体文件路径), `font_size` (字体大小)
  - **输出**: `ImageFont.FreeTypeFont` - 字体对象
  - **功能**: 获取支持中文的字体对象，优先使用指定字体，失败则使用默认字体

- **`create_transparent_color(color: Union[str, Tuple[int, int, int]], alpha: float = DEFAULT_ALPHA) -> Tuple[int, int, int, int]`**
  - **输入参数**: `color` (颜色名称或RGB元组), `alpha` (透明度，0-1)
  - **输出**: `Tuple[int, int, int, int]` - RGBA颜色元组
  - **功能**: 创建带透明度的颜色

- **`draw_rect_text(img: Image.Image, bbox, bg_color=None, line_color=None, line_width=1, font=None, min_font_size=8, max_font_size=72, text=None, text_color=(0, 0, 0), label=None, label_color=(255, 0, 0))`**
  - **输入参数**: 图像对象、边界框、背景颜色、边框颜色、边框宽度、字体、文本、标签等
  - **功能**: 在图像上绘制带文本的矩形框，自适应调整文本大小以适应边界框

- **`draw_quad(img: Image.Image, quad_points: List[Tuple], outline_color, fill_color, line_width: int = 1) -> None`**
  - **输入参数**: 图像对象、四边形顶点坐标、边框颜色、填充颜色、线宽
  - **功能**: 在图像上绘制四边形，支持透明效果

### `app/utils/image_locator.py`

a. **文件用途说明**
提供图像坐标处理的工具函数，包括多边形顶点排序、图像旋转计算等功能，用于处理OCR结果中的坐标信息，特别是在进行图像变换时保持坐标的正确性。

b. **文件内容概览 (类与函数)**
- 多边形处理：`sort_polygon_points`
- 图像旋转计算：`rotate_size`, `rotate_point`

#### 函数/方法详解
- **`sort_polygon_points(points: List[List[int]]) -> List[List[int]]`**
  - **输入参数**: `points` - 表示多边形顶点坐标的列表
  - **输出**: 按顺时针顺序排列的顶点坐标列表
  - **功能**: 对多边形顶点进行顺时针排序，通过计算几何中心点，然后按相对于中心点的角度排序

- **`rotate_size(width, height, angle)`**
  - **输入参数**: `width` (原图宽度), `height` (原图高度), `angle` (旋转角度)
  - **输出**: `Tuple[int, int]` - 旋转后的图像宽高
  - **功能**: 计算图像旋转后的尺寸

- **`rotate_point(x, y, angle, center_x, center_y)`**
  - **输入参数**: `x`, `y` (点坐标), `angle` (旋转角度), `center_x`, `center_y` (旋转中心)
  - **输出**: `Tuple[float, float]` - 旋转后的点坐标
  - **功能**: 计算点在图像旋转后的新坐标