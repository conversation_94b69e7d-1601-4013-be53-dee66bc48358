#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
可中断的OSS下载器
提供支持超时和中断的OSS文件下载功能
"""

import os
import time
import logging
import threading
from typing import Optional, Callable
from app.utils.oss_downloader import oss_download_file

logger = logging.getLogger(__name__)

class InterruptibleOSSDownloader:
    """可中断的OSS下载器"""
    
    def __init__(self):
        self._interrupted = False
        self._download_thread = None
        self._exception = None
        self._completed = False
    
    def download_with_timeout(self, 
                            object_name: str, 
                            local_file_path: str, 
                            timeout: float = 300.0,
                            interrupt_check: Optional[Callable[[], bool]] = None) -> bool:
        """带超时和中断检查的下载
        
        Args:
            object_name: OSS对象名称
            local_file_path: 本地文件路径
            timeout: 超时时间（秒）
            interrupt_check: 中断检查函数，返回True表示需要中断
            
        Returns:
            bool: 下载是否成功
            
        Raises:
            TimeoutError: 下载超时
            KeyboardInterrupt: 下载被中断
            Exception: 其他下载错误
        """
        self._interrupted = False
        self._exception = None
        self._completed = False
        
        # 启动下载线程
        self._download_thread = threading.Thread(
            target=self._download_worker,
            args=(object_name, local_file_path),
            daemon=True
        )
        self._download_thread.start()
        
        # 监控下载进度
        start_time = time.time()
        check_interval = 1.0  # 每秒检查一次
        
        while self._download_thread.is_alive():
            # 检查超时
            if time.time() - start_time > timeout:
                logger.error(f"OSS下载超时（超过{timeout}秒）")
                self._interrupted = True
                self._download_thread.join(timeout=5.0)  # 等待5秒让线程退出
                raise TimeoutError(f"OSS下载超时（超过{timeout}秒）")
            
            # 检查外部中断
            if interrupt_check and interrupt_check():
                logger.warning("OSS下载被外部中断")
                self._interrupted = True
                self._download_thread.join(timeout=5.0)
                raise KeyboardInterrupt("OSS下载被中断")
            
            time.sleep(check_interval)
        
        # 等待线程完成
        self._download_thread.join()
        
        # 检查下载结果
        if self._exception:
            raise self._exception
        
        if not self._completed:
            raise RuntimeError("OSS下载未完成，原因未知")
        
        return True
    
    def _download_worker(self, object_name: str, local_file_path: str):
        """下载工作线程"""
        try:
            logger.info(f"开始OSS下载: {object_name} -> {local_file_path}")
            
            # 调用原始的OSS下载函数
            oss_download_file(object_name, local_file_path)
            
            if not self._interrupted:
                self._completed = True
                logger.info(f"OSS下载完成: {local_file_path}")
            else:
                logger.warning(f"OSS下载被中断: {local_file_path}")
                # 清理部分下载的文件
                if os.path.exists(local_file_path):
                    try:
                        os.remove(local_file_path)
                        logger.info(f"已清理部分下载的文件: {local_file_path}")
                    except Exception as e:
                        logger.warning(f"清理部分下载文件失败: {e}")
                        
        except Exception as e:
            logger.error(f"OSS下载失败: {str(e)}")
            self._exception = e
            
            # 清理部分下载的文件
            if os.path.exists(local_file_path):
                try:
                    os.remove(local_file_path)
                    logger.info(f"已清理失败的下载文件: {local_file_path}")
                except Exception as cleanup_e:
                    logger.warning(f"清理失败下载文件时出错: {cleanup_e}")

def download_with_interrupt_support(object_name: str, 
                                   local_file_path: str, 
                                   timeout: float = 300.0,
                                   interrupt_check: Optional[Callable[[], bool]] = None) -> bool:
    """支持中断的OSS下载函数
    
    Args:
        object_name: OSS对象名称
        local_file_path: 本地文件路径
        timeout: 超时时间（秒），默认5分钟
        interrupt_check: 中断检查函数，返回True表示需要中断
        
    Returns:
        bool: 下载是否成功
        
    Raises:
        TimeoutError: 下载超时
        KeyboardInterrupt: 下载被中断
        Exception: 其他下载错误
    """
    downloader = InterruptibleOSSDownloader()
    return downloader.download_with_timeout(
        object_name, 
        local_file_path, 
        timeout, 
        interrupt_check
    )
