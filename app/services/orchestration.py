import os
import uuid
import base64
import logging
import numpy as np
import cv2
from typing import List, Optional, Dict, Any

from app.core import errors
from app.utils import image_locator
from app.schemas import ocr_results
from app.utils.image_locator import to_flat_polygon
from app.services.image_service import ImageService
from app.services.persistence import PersistenceService
from app.processing.text_detection import TextDetectionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.layout_analysis import LayoutAnalysisService
from app.processing.table_recognition import TableRecognitionService
from app.processing.formula_recognition import FormulaRecognitionService
from app.utils.image_downloader import download_image_from_url, validate_image_data, ImageDownloadError

logger = logging.getLogger(__name__)


class OrchestrationService:
    """编排服务，协调各个处理服务的调用顺序和数据流"""
    
    def __init__(
        self,
        text_detection_service: TextDetectionService,
        text_recognition_service: TextRecognitionService,
        layout_analysis_service: LayoutAnalysisService,
        table_recognition_service: TableRecognitionService,
        formula_recognition_service: FormulaRecognitionService,
        persistence_service: PersistenceService,
        image_service: ImageService
    ):
        """初始化编排服务
        
        Args:
            text_detection_service: 文本检测服务
            text_recognition_service: 文本识别服务
            layout_analysis_service: 版面分析服务
            table_recognition_service: 表格识别服务
            formula_recognition_service: 公式识别服务
            persistence_service: 持久化服务
            image_service: 图片服务
        """
        self.text_detection_service = text_detection_service
        self.text_recognition_service = text_recognition_service
        self.layout_analysis_service = layout_analysis_service
        self.table_recognition_service = table_recognition_service
        self.formula_recognition_service = formula_recognition_service
        self.persistence_service = persistence_service
        self.image_service = image_service
    
    def _decode_base64_image(self, image_base64: str) -> bytes:
        """解码Base64编码的图像数据
        
        Args:
            image_base64: Base64编码的图像字符串
            
        Returns:
            bytes: 解码后的图像二进制数据
            
        Raises:
            ClientError: 如果解码失败
        """
        # 处理可能包含的Data URL前缀
        if "," in image_base64:
            image_base64 = image_base64.split(",", 1)[1]
        
        try:
            # 解码Base64数据
            return base64.b64decode(image_base64)
        except Exception as e:
            logger.error(f"Base64解码失败: {str(e)}")
            raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "bad image base64")

    def _get_image_bytes(self, image_base64: Optional[str] = None, image_url: Optional[str] = None) -> bytes:
        """统一获取图像字节数据，支持Base64和URL两种输入方式

        Args:
            image_base64: Base64编码的图像字符串（可选）
            image_url: 图像URL（可选）

        Returns:
            bytes: 图像的二进制数据

        Raises:
            ClientError: 当输入无效或获取失败时
        """
        # 优先使用image_url
        if image_url:
            try:
                logger.info(f"使用image_url获取图像: {image_url}")
                image_bytes = download_image_from_url(image_url)

                # 验证下载的图像数据
                if not validate_image_data(image_bytes):
                    logger.warning(f"下载的数据可能不是有效的图像格式: {image_url}")

                return image_bytes

            except ImageDownloadError as e:
                logger.error(f"从URL下载图像失败: {image_url}, 错误: {str(e)}")
                raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, f"下载图像失败: {str(e)}")
            except Exception as e:
                logger.error(f"处理image_url时发生未知错误: {image_url}, 错误: {str(e)}")
                raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, f"处理图像URL失败: {str(e)}")

        # 如果没有image_url，使用image_base64
        elif image_base64:
            logger.info("使用image_base64获取图像")
            return self._decode_base64_image(image_base64)

        # 两者都没有提供
        else:
            raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "必须提供image_url或image_base64之一")

    def _get_image(self, image_base64: Optional[str] = None, image_url: Optional[str] = None) -> np.ndarray:
        """统一获取图像数据，避免重复解码

        Args:
            image_base64: Base64编码的图像字符串（可选）
            image_url: 图像URL（可选）

        Returns:
            np.ndarray: BGR格式的图像数组 (H, W, C)

        Raises:
            ClientError: 当输入无效或解码失败时
        """
        # 获取原始字节数据
        image_bytes = self._get_image_bytes(image_base64, image_url)

        # 统一解码为numpy数组
        image = cv2.imdecode(np.frombuffer(image_bytes, np.uint8), cv2.IMREAD_UNCHANGED)
        if image is None:
            logger.error("图像解码失败")
            raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "图像解码失败")

        # 统一格式处理
        if len(image.shape) == 2:
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        image = image[..., :3]  # 移除alpha通道，确保为3通道BGR格式

        logger.info(f"图像解码成功，尺寸: {image.shape}")
        return image

    def process_text_detection(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理文本检测请求

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 文本检测结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理文本检测请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 调用文本检测服务
        bboxes, bboxes_scores = self.text_detection_service.detect(image)

        # 将结果转换为TextDetectionItem列表
        detection_items = []
        for bbox, score in zip(bboxes, bboxes_scores):
            detection_items.append(ocr_results.TextDetectionItem(
                bbox=to_flat_polygon(bbox),
                confidence=score
            ))

        # 创建响应对象，将结果包装在 "text_detection" 键下
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results={"text_detection": [item.dict(exclude_none=True) for item in detection_items]}
        )

        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"文本检测请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response
        
    def process_text_recognition(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理文本识别请求

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 文本识别结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理文本识别请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 调用文本检测服务获取边界框
        bboxes, bboxes_scores = self.text_detection_service.detect(image)

        # 创建一个从bbox到其置信度分数的映射，以便高效查找
        # 将bbox（list）转换为str，使其可以作为字典的键
        bbox_to_score_map = {" ".join(list(map(str, np.array(bbox).reshape(-1).tolist()))): score for bbox, score in zip(bboxes, bboxes_scores)}

        # 调用文本识别服务
        text_items_raw = self.text_recognition_service.recognize(image, bboxes)

        # 将原始结果转换为Pydantic模型
        text_items = []
        for item in text_items_raw:
            # 创建嵌套的文本内容对象
            text_content = ocr_results.TextContent(
                value=item["words"],  # 识别的文本内容
                confidence=item["confidence"]  # 识别的置信度
            )

            # 从映射中查找当前bbox对应的置信度分数
            # 将item["bbox"]（list）转换为str以匹配字典的键
            score = bbox_to_score_map.get(" ".join(list(map(str, item["bbox"]))))

            text_items.append(ocr_results.TextRecognitionItem(
                bbox=to_flat_polygon(item["bbox"]),
                confidence=score,
                text=text_content  # 嵌套的文本内容
            ))

        # 创建响应对象，将结果包装在 "text_recognition" 键下
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results={"text_recognition": [item.dict(exclude_none=True) for item in text_items]}
        )

        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"文本识别请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response
        
    def process_layout_analysis(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理版面分析请求

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 版面分析结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理版面分析请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 调用版面分析服务
        layout_items_raw = self.layout_analysis_service.analyze(image)

        # 将原始结果转换为Pydantic模型
        layout_items = []
        for item in layout_items_raw:
            layout_items.append(ocr_results.LayoutAnalysisItem(
                bbox=to_flat_polygon(item["bbox"]),
                type=item["type"],
                confidence=item["confidence"]
                # content 字段在独立版面分析中不填充，保持默认的 None
            ))

        # 创建响应对象，将结果包装在 "layout_analysis" 键下
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results={"layout_analysis": [item.dict(exclude_none=True) for item in layout_items]}
        )

        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"版面分析请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response
        
    def process_table_recognition(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理表格识别请求

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 表格识别结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理表格识别请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 先进行版面分析找出表格区域
        layout_items_raw = self.layout_analysis_service.analyze(image)

        # 找出所有表格区域
        layout_tables = []
        for item in layout_items_raw:
            if item["type"].lower() == "table":
                layout_tables.append(item)

        table_recognition_items = []
        if not layout_tables:
            logger.warning("未找到表格区域，直接返回空结果")
        else:
            logger.info(f"找到 {len(layout_tables)} 个表格区域")

            # 处理所有表格区域
            for i, layout_table in enumerate(layout_tables):
                logger.info(f"处理表格区域 {i+1}/{len(layout_tables)}: {layout_table['bbox']}")

                # 调用表格识别服务，传递表格边界框
                table_content_raw = self.table_recognition_service.recognize_table(image, layout_table["bbox"], None)

                # 将原始结果转换为Pydantic模型
                cells = []
                for cell in table_content_raw["cells"]:
                    # 创建嵌套的文本内容对象
                    text_content = ocr_results.TextContent(
                        value=cell.get("text", {}).get("value"),
                        confidence=cell.get("text", {}).get("confidence")
                    )

                    cells.append(ocr_results.TableCell(
                        bbox=to_flat_polygon(cell["bbox"]),
                        confidence=cell["confidence"],
                        row_start=cell["row_start"],
                        col_start=cell["col_start"],
                        row_span=cell["row_span"],
                        col_span=cell["col_span"],
                        text=text_content  # 嵌套的文本内容
                    ))

                # 创建表格识别项（去掉content包装，直接包含cells）
                table_item = ocr_results.TableRecognitionItem(
                    bbox=to_flat_polygon(layout_table["bbox"]),
                    confidence=layout_table["confidence"],
                    cells=cells  # 直接包含cells数组
                )
                table_recognition_items.append(table_item)

        # 创建响应对象，将结果包装在 "table_recognition" 键下
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results={"table_recognition": [item.dict(exclude_none=True) for item in table_recognition_items]}
        )

        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"表格识别请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response
        
    def process_formula_recognition(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理公式识别请求

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 公式识别结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理公式识别请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 先进行版面分析找出公式区域
        layout_items_raw = self.layout_analysis_service.analyze(image)

        # 找出所有公式区域，同时保存版面分析的置信度
        formula_items = []
        for item in layout_items_raw:
            if item["type"].lower() == "formula":
                formula_items.append(item)

        formula_recognition_items = []
        if not formula_items:
            logger.warning("未找到公式区域，直接返回空结果")
        else:
            logger.info(f"找到 {len(formula_items)} 个公式区域")

            # 提取边界框进行批量识别
            formula_bboxes = [item["bbox"] for item in formula_items]
            formula_results = self.formula_recognition_service.recognize(image, formula_bboxes)

            # 创建公式识别项列表
            for i, (formula_item, formula_result) in enumerate(zip(formula_items, formula_results)):
                logger.info(f"处理公式区域 {i+1}/{len(formula_items)}: {formula_item['bbox']}")

                # 创建嵌套的文本内容对象
                text_content = ocr_results.TextContent(
                    value=formula_result["latex_string"],  # 公式识别的LaTeX字符串
                    confidence=formula_result["confidence"]  # 公式识别的置信度
                )

                formula_recognition_item = ocr_results.FormulaRecognitionItem(
                    bbox=to_flat_polygon(formula_item["bbox"]),
                    confidence=formula_item["confidence"],  # 版面分析的置信度
                    text=text_content  # 嵌套的文本内容
                )
                formula_recognition_items.append(formula_recognition_item)

        # 创建响应对象，将结果包装在 "formula_recognition" 键下
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results={"formula_recognition": [item.dict(exclude_none=True) for item in formula_recognition_items]}
        )

        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"公式识别请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response
        
    def process_full_result(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """处理全量OCR请求，组合多个处理服务的结果

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 全量OCR结果
        """
        # 生成请求ID
        request_id = uuid.uuid4()
        logger.info(f"开始处理全量OCR请求 ID: {request_id} (持久化: {persist})")

        # 获取统一的图像数据（避免重复解码）
        image = self._get_image(image_base64=image_base64, image_url=image_url)

        # 根据persist参数决定是否保存原始图片
        if persist:
            image_bytes = self._get_image_bytes(image_base64=image_base64, image_url=image_url)
            self.image_service.save_image(request_id, image_bytes)

        # 1. 调用文本检测服务获取边界框
        bboxes, bboxes_scores = self.text_detection_service.detect(image)

        # 创建一个从bbox到其置信度分数的映射，以便高效查找
        # 将bbox（list）转换为str，使其可以作为字典的键
        bbox_to_score_map = {" ".join(list(map(str, np.array(bbox).reshape(-1).tolist()))): score for bbox, score in zip(bboxes, bboxes_scores)}

        # 2. 调用文本识别服务
        text_items_raw = self.text_recognition_service.recognize(image, bboxes)
        text_items = []
        for item in text_items_raw:
            # 创建嵌套的文本内容对象
            text_content = ocr_results.TextContent(
                value=item["words"],  # 识别的文本内容
                confidence=item["confidence"]  # 识别的置信度
            )

            # 从映射中查找当前bbox对应的置信度分数
            # 将item["bbox"]（list）转换为str以匹配字典的键
            score = bbox_to_score_map.get(" ".join(list(map(str, item["bbox"]))))
            text_items.append(ocr_results.TextRecognitionItem(
                bbox=to_flat_polygon(item["bbox"]),
                confidence=score,  # 使用查找到的置信度
                text=text_content  # 嵌套的文本内容
            ))

        # 3. 调用版面分析服务
        layout_items_raw = self.layout_analysis_service.analyze(image)

        # 4. 先收集所有公式区域进行批量处理
        formula_items = [item for item in layout_items_raw if item["type"].lower() == "formula"]
        formula_results_dict = {}

        if formula_items:
            formula_bboxes = [item["bbox"] for item in formula_items]
            formula_results = self.formula_recognition_service.recognize(image, formula_bboxes)

            # 将结果与对应的layout_item关联
            for item, result in zip(formula_items, formula_results):
                formula_results_dict[id(item)] = result

        # 5. 对每个版面区域进行深度处理
        layout_items_with_content = []
        for item in layout_items_raw:
            layout_item = ocr_results.LayoutAnalysisItem(
                bbox=to_flat_polygon(item["bbox"]),
                type=item["type"],
                confidence=item["confidence"]
            )

            # 根据区域类型调用不同的处理服务
            item_type_lower = item["type"].lower()  # 添加: 转为小写用于比较

            if item_type_lower == "table":  # 修改: 使用小写比较
                # 处理表格
                table_content_raw = self.table_recognition_service.recognize_table(image, item["bbox"], text_items_raw)
                cells = []
                for cell in table_content_raw["cells"]:
                    # 创建嵌套的文本内容对象
                    text_content = ocr_results.TextContent(
                        value=cell.get("text", {}).get("value"),
                        confidence=cell.get("text", {}).get("confidence")
                    )

                    cells.append(ocr_results.TableCell(
                        bbox=to_flat_polygon(cell["bbox"]),
                        confidence=cell["confidence"],  # 表格识别的置信度（默认值）
                        row_start=cell["row_start"],
                        col_start=cell["col_start"],
                        row_span=cell["row_span"],
                        col_span=cell["col_span"],
                        text=text_content  # 嵌套的文本内容
                    ))

                # 直接设置cells数组，不使用content包装
                layout_item.cells = cells

                # HTML字段已从表格识别服务中移除

            elif item_type_lower == "formula":  # 修改: 使用小写比较
                # 使用批量处理的结果
                formula_content_raw = formula_results_dict.get(id(item))
                if formula_content_raw:
                    # 创建嵌套的文本内容对象
                    text_content = ocr_results.TextContent(
                        value=formula_content_raw["latex_string"],  # 公式识别的LaTeX字符串
                        confidence=formula_content_raw["confidence"]  # 公式识别的置信度
                    )
                    layout_item.text = text_content
                else:
                    logger.warning(f"Formula recognition result not found for item: {item['bbox']}")

            elif item_type_lower in ["text", "title", "paragraph"]:  # 修改: 支持更多文本类型
                # 文本区域，从text_items中提取对应区域的文本
                text_value = ""
                confidence_sum = 0.0
                text_count = 0

                for text_item in text_items:
                    # 检查文本是否在当前版面区域内
                    if self._is_bbox_inside(text_item.bbox, item["bbox"]):
                        if text_value:
                            text_value += " "
                        text_value += text_item.text.value  # 使用新的嵌套结构
                        confidence_sum += text_item.text.confidence  # 使用文本识别的置信度
                        text_count += 1

                # 计算平均置信度
                avg_confidence = confidence_sum / text_count if text_count > 0 else 0.0

                # 创建嵌套的文本内容对象
                if text_value:  # 只有当有文本内容时才设置
                    text_content = ocr_results.TextContent(
                        value=text_value,
                        confidence=avg_confidence
                    )
                    layout_item.text = text_content

            layout_items_with_content.append(layout_item)

        # 6. 组装最终结果
        results = {
            "text_recognition": [item.dict(exclude_none=True) for item in text_items],
            "layout_analysis": [item.dict(exclude_none=True) for item in layout_items_with_content]
        }

        # 创建响应对象
        response = ocr_results.FullOCRResponse(
            request_id=request_id,
            results=results
        )
        
        # 根据persist参数决定是否持久化结果
        if persist:
            self.persistence_service.save_result(request_id, response)

        logger.info(f"全量OCR请求 ID: {request_id} 处理完成 (持久化: {persist})")
        return response

    def process_full_ocr(self, image_base64: Optional[str] = None, image_url: Optional[str] = None, persist: bool = True) -> ocr_results.FullOCRResponse:
        """process_full_result的别名，为了保持API兼容性

        Args:
            image_base64: Base64编码的图像（可选）
            image_url: 图像URL（可选）
            persist: 是否进行持久化操作（保存图片和结果）

        Returns:
            FullOCRResponse: 全量OCR结果
        """
        return self.process_full_result(image_base64=image_base64, image_url=image_url, persist=persist)

    def _is_bbox_inside(self, inner_bbox: List[int], outer_bbox: List[int]) -> bool:
        inter_poly = image_locator.inter_polygon(inner_bbox, outer_bbox)
        inner_area = image_locator.polygon_area(inner_bbox)
        if not inter_poly or not inner_area:
            return False
        inter_area = inter_poly.area / inner_area
        return inter_area > 0.65
