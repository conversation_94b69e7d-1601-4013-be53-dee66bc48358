from pathlib import Path
from uuid import UUID
import logging

from app.core import errors

logger = logging.getLogger(__name__)


class ImageService:
    """图片存储和获取服务"""
    
    def __init__(self, images_dir: str):
        """初始化图片服务
        
        Args:
            images_dir: 图片存储目录路径
        """
        self.images_dir = Path(images_dir)
        # 确保目录存在
        self.images_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"图片存储目录: {self.images_dir.absolute()}")
    
    def save_image(self, request_id: UUID, image_bytes: bytes) -> bool:
        """保存图片到磁盘
        
        Args:
            request_id: 请求ID
            image_bytes: 图片的二进制数据
            
        Returns:
            bool: 保存是否成功
        """
        # 使用.jpg作为默认扩展名
        file_path = self.images_dir / f"{request_id}.jpg"
        file_path.write_bytes(image_bytes)
        logger.info(f"图片已保存: {file_path}")
        return True
    
    def get_image_path(self, request_id: UUID) -> Path:
        """获取图片文件路径
        
        Args:
            request_id: 请求ID
            
        Returns:
            Path: 图片文件路径
        """
        file_path = self.images_dir / f"{request_id}.jpg"
        if not file_path.exists():
            logger.warning(f"图片文件不存在: {file_path}")
            raise errors.ClientErr(errors.ErrCode.CLIENT_NOT_FOUND, f"图片不存在")
        
        return file_path
    