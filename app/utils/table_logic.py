import json
import copy

from app.utils.image_locator import to_polygon, sort_polygon_points

_MAX_TOLERANCE = 30
_MIN_TOLERANCE = 10

def merge_boundaries(coords: list[float], tolerance: int = _MAX_TOLERANCE) -> list[float]:
    """
    合并排序列表中接近的坐标值。

    Args:
        coords (list[float]): 包含所有边界坐标的列表。
        tolerance (int): 用于判断坐标是否“接近”的容差值。

    Returns:
        list[float]: 合并后，代表唯一网格线的坐标列表。
    """
    if not coords:
        return []

    sorted_coords = sorted(coords)
    
    merged = [sorted_coords[0]]
    last = sorted_coords[0]
    for i in range(1, len(sorted_coords)):
        # 如果当前坐标与最后一个已合并的坐标的差值大于容差，则视为新的网格线
        if sorted_coords[i] - last > tolerance:
            merged.append(sorted_coords[i])
        last = sorted_coords[i]

    return merged

def find_closest_index(value: float, sorted_list: list[float]) -> int:
    """
    在排序好的边界列表中，找到与给定坐标值最接近的边界线的索引。
    """
    min_diff = float('inf')
    closest_idx = -1
    for i, boundary in enumerate(sorted_list):
        diff = abs(value - boundary)
        if diff < min_diff:
            min_diff = diff
            closest_idx = i
            
    return closest_idx

def process_table_structure(bbox_list: list[list[float]], image_size: tuple[int, int] = None) -> list[dict]:
    """
    接收表格所有单元格的坐标点，返回每个单元格的结构化信息。
    """
    if not bbox_list:
        return []
    tolerance = 5
    if image_size:
        t = min(image_size) * 0.015
        t = min(_MAX_TOLERANCE, t)
        tolerance = max(_MIN_TOLERANCE, t)

    # 步骤 1: 预处理单元格数据
    cells_data = []
    all_x_boundaries = []
    all_y_boundaries = []

    for bbox in bbox_list:
        polygon = to_polygon(bbox) 
        polygon = sort_polygon_points(polygon) # 将多边形顶点按照顺时针排序，得到上左、上右、下右、下左的坐标点顺序
        x_min, y_min = polygon[0] # 以上左坐标点为单元格最小点 
        x_max, y_max = polygon[2] # 以下右坐标点为单元格最大点

        cells_data.append({
            'bbox': bbox,
            'x_min': x_min,
            'y_min': y_min,
            'x_max': x_max,
            'y_max': y_max
        })
        
        all_x_boundaries.extend([x_min, x_max])
        all_y_boundaries.extend([y_min, y_max])

    # 步骤 2: 推断并合并网格线
    x_boundaries = merge_boundaries(all_x_boundaries, tolerance=tolerance)
    y_boundaries = merge_boundaries(all_y_boundaries, tolerance=tolerance)

    # 步骤 3: 映射单元格到网格并计算行列信息
    final_output = []
    for cell in cells_data:
        row_start = find_closest_index(cell['y_min'], y_boundaries)
        col_start = find_closest_index(cell['x_min'], x_boundaries)
        
        row_end = find_closest_index(cell['y_max'], y_boundaries)
        col_end = find_closest_index(cell['x_max'], x_boundaries)

        row_span = max(1, row_end - row_start)
        col_span = max(1, col_end - col_start)

        final_output.append({
            "bbox": cell['bbox'],
            "row_start": row_start,
            "col_start": col_start,
            "row_span": row_span,
            "col_span": col_span
        })

    return final_output

def sort_cells(cells):
    """排序函数，按行列排序或者根据左上角坐标 (y, x) 进行排序"""
    if not cells:
        return (0, 0)
    has_row_start = ("row_start" in cells[0])
    def compare(cell):
        if has_row_start:
            return (cell["row_start"], cell["col_start"])
        bbox = cell["bbox"]
        polygon = to_polygon(bbox)
        x, y = polygon[0]
        return (y, x)

    cells.sort(key=compare)

def get_table_bbox(cells):
    if not cells:
        return []
    min_x, min_y, max_x, max_y = float("inf"), float("inf"), float("-inf"), float("-inf")
    for cell in cells:
        bbox = to_polygon(cell["bbox"])
        for point in bbox:
            min_x = min(min_x, point[0])
            max_x = max(max_x, point[0])
            min_y = min(min_y, point[1])
            max_y = max(max_y, point[1])
    return list(map(int, (min_x, min_y, max_x, max_y)))

if __name__ == '__main__':
    in_cells = [
        [466.1, 4, 466, 39.3, 696, 39, 696, 4],
        [696, 4, 696, 39, 927, 39.5, 927, 4],
        [6, 74, 6, 109, 236, 108, 236, 74],
        [466, 39, 466, 74, 696, 74, 696, 39],
        [466, 74, 466, 108, 696, 108.7, 696, 74],
        [236, 74, 236, 108, 466, 108, 466, 74],
        [696, 39, 696, 108, 927, 108, 927, 39],
        [236, 39, 236, 74, 466, 74.4, 466, 39],
        [6, 39, 6, 74, 236.6, 74, 236, 39],
        [6, 4, 6, 39, 466, 39, 466, 4.9]
    ]
    output_result = process_table_structure(in_cells, image_size=(935, 115))
    # 按照从上到下、从左到右的顺序对单元格进行排序
    sort_cells(output_result)
    print(json.dumps(output_result, indent=2, ensure_ascii=False))