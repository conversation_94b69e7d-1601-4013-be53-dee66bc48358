import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Spin, Alert, Image } from 'antd';
import { ocrApi } from '../api/ocrApi';

interface OriginalImageViewerProps {
  requestId: string;
}

export const OriginalImageViewer: React.FC<OriginalImageViewerProps> = ({ requestId }) => {
  const { data: imageUrl, isPending, isError, error } = useQuery({
    queryKey: ['originalImage', requestId],
    queryFn: () => ocrApi.getOriginalImage(requestId),
    enabled: !!requestId,
  });

  if (isPending) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载原图中...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert
        message="加载失败"
        description={error?.message || '无法加载原图，请重试'}
        type="error"
        showIcon
      />
    );
  }

  if (!imageUrl) {
    return (
      <Alert
        message="暂无图片"
        description="图片正在处理中，请稍后"
        type="info"
        showIcon
      />
    );
  }

  return (
    <div style={{ textAlign: 'center' }}>
      <Image
        src={imageUrl}
        alt="Original Upload"
        style={{ maxWidth: '100%', maxHeight: '400px' }}
        preview={{
          mask: '点击查看大图',
        }}
      />
    </div>
  );
}; 