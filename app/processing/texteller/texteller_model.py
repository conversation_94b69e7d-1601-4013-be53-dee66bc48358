from pathlib import Path

from transformers import RobertaTokenizerFast, VisionEncoderDecoderConfig, VisionEncoderDecoderModel

from .constants import (
    FIXED_IMG_SIZE,
    IMG_CHANNELS,
    MAX_TOKEN_SIZE,
    VOCAB_SIZE,
)
from .types import TexTellerModel
from .device_utils import cuda_available


class TexTeller(VisionEncoderDecoderModel):
    def __init__(self, repo_name: str = "OleehyO/TexTeller"):
        config = VisionEncoderDecoderConfig.from_pretrained(repo_name)
        config.encoder.image_size = FIXED_IMG_SIZE
        config.encoder.num_channels = IMG_CHANNELS
        config.decoder.vocab_size = VOCAB_SIZE
        config.decoder.max_position_embeddings = MAX_TOKEN_SIZE

        super().__init__(config=config)

    @classmethod
    def from_pretrained(cls, model_dir: str = None, use_onnx=False, repo_name: str = "OleehyO/TexTeller") -> TexTellerModel:
        if model_dir is None or model_dir == repo_name:
            if not use_onnx:
                return VisionEncoderDecoderModel.from_pretrained(repo_name)
            else:
                from optimum.onnxruntime import ORTModelForVision2Seq

                return ORTModelForVision2Seq.from_pretrained(
                    repo_name,
                    provider="CUDAExecutionProvider"
                    if cuda_available()
                    else "CPUExecutionProvider",
                )
        model_dir = Path(model_dir).resolve()
        return VisionEncoderDecoderModel.from_pretrained(str(model_dir))

    @classmethod
    def get_tokenizer(cls, tokenizer_dir: str = None, repo_name: str = "OleehyO/TexTeller") -> RobertaTokenizerFast:
        if tokenizer_dir is None or tokenizer_dir == repo_name:
            return RobertaTokenizerFast.from_pretrained(repo_name)
        tokenizer_dir = Path(tokenizer_dir).resolve()
        return RobertaTokenizerFast.from_pretrained(str(tokenizer_dir))
