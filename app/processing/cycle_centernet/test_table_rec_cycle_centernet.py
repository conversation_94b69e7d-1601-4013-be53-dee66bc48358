#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
from pathlib import Path

from .table_rec_cycle_centernet import CycleCenterNetTable
from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_cycle_centernet_table_recognition():
    """测试CycleCenterNet表格识别功能"""
    
    # 配置
    config = {
        "USE_CUDA": False,  # 根据实际情况设置
    }
    
    # 创建文本识别服务（这里使用模拟服务）
    class MockTextRecognitionService(TextRecognitionService):
        def __init__(self):
            pass
            
        def recognize(self, image_bytes, text_bboxes):
            # 模拟文本识别结果
            results = []
            for i, bbox in enumerate(text_bboxes):
                results.append({
                    "bbox": bbox,
                    "words": f"文本{i+1}",
                    "confidence": 0.95
                })
            return results
    
    # 创建文本检测服务（这里使用模拟服务）
    class MockTextDetectionService(TextDetectionService):
        def __init__(self):
            pass
            
        def detect(self, image_bytes):
            # 模拟文本检测结果，返回一些边界框
            return [
                [10, 10, 100, 30],
                [120, 10, 200, 30],
                [10, 50, 100, 70],
                [120, 50, 200, 70]
            ]
    
    # 创建服务实例
    text_recognition = MockTextRecognitionService()
    text_detection = MockTextDetectionService()
    
    table_recognition = CycleCenterNetTable(
        config=config,
        text_recognition=text_recognition,
        text_detection=text_detection
    )
    
    # 测试图像路径
    assets_dir = Path(__file__).parent.parent / "assets"
    test_image_path = assets_dir / "test03.png"
    
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        return
    
    # 读取测试图像
    with open(test_image_path, 'rb') as f:
        image_bytes = f.read()
    
    logger.info(f"开始测试表格识别，图像: {test_image_path}")
    
    try:
        # 执行表格识别
        result = table_recognition.recognize_table(image_bytes)
        
        # 打印结果
        logger.info("表格识别结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 验证结果格式
        assert "confidence" in result
        assert "cells" in result
        assert isinstance(result["cells"], list)
        
        for i, cell in enumerate(result["cells"]):
            assert "bbox" in cell
            assert "row_start" in cell
            assert "col_start" in cell
            assert "row_span" in cell
            assert "col_span" in cell
            assert "text" in cell
            assert "confidence" in cell
            
            # 验证text字段是一个包含value和confidence的字典
            assert isinstance(cell["text"], dict)
            assert "value" in cell["text"]
            assert "confidence" in cell["text"]
            
            logger.info(f"单元格 {i}: 行={cell['row_start']}, 列={cell['col_start']}, "
                       f"行跨度={cell['row_span']}, 列跨度={cell['col_span']}, "
                       f"文本='{cell['text']['value']}'")
        
        logger.info("测试通过！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

def test_with_table_bbox():
    """测试带表格边界框的表格识别"""
    
    # 配置
    config = {
        "USE_CUDA": False,
    }
    
    # 创建模拟服务
    class MockTextRecognitionService(TextRecognitionService):
        def __init__(self):
            pass
            
        def recognize(self, image_bytes, text_bboxes):
            results = []
            for i, bbox in enumerate(text_bboxes):
                results.append({
                    "bbox": bbox,
                    "words": f"单元格文本{i+1}",
                    "confidence": 0.92
                })
            return results
    
    text_recognition = MockTextRecognitionService()
    
    table_recognition = CycleCenterNetTable(
        config=config,
        text_recognition=text_recognition,
        text_detection=None  # 不使用文本检测
    )
    
    # 测试图像路径
    assets_dir = Path(__file__).parent.parent / "assets"
    test_image_path = assets_dir / "test03.png"
    
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        return
    
    # 读取测试图像
    with open(test_image_path, 'rb') as f:
        image_bytes = f.read()
    
    # 定义表格边界框（示例）
    table_bbox = [50, 50, 800, 400]
    
    logger.info(f"开始测试带边界框的表格识别，边界框: {table_bbox}")
    
    try:
        # 执行表格识别
        result = table_recognition.recognize_table(image_bytes, table_bbox)
        
        # 打印结果
        logger.info("带边界框的表格识别结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("带边界框测试通过！")
        
    except Exception as e:
        logger.error(f"带边界框测试失败: {e}")
        raise

if __name__ == "__main__":
    logger.info("开始测试CycleCenterNet表格识别")
    
    # 测试基本功能
    test_cycle_centernet_table_recognition()
    
    # 测试带边界框功能
    test_with_table_bbox()
    
    logger.info("所有测试完成！")
