from app.utils.visualization import (
    get_font, draw_polygon_text
)
from app.utils.table_logic import get_table_bbox

def visual_cells(cells, img, show_text=True):
    font = get_font(font_size=10)
    for i, cell in enumerate(cells):
        bbox = cell["bbox"]
        info = None
        if show_text:
            info = f"{i+1}: CELL=({cell['row_start']+1}, {cell['col_start']+1}), SPAN=({cell['row_span']}, {cell['col_span']})"
            cell_text = cell.get("text", {}).get("value", "").strip()
            if cell_text:
                draw_polygon_text(img, bbox, line_width=0, line_color=None, text=cell_text, font=font, text_color=(0, 0, 0, 128), max_font_size=24)
        draw_polygon_text(img, bbox, line_width=1, line_color="black", text=info, font=font, text_color="red", max_font_size=36)

def visual_table_box(table, img):
    bbox= table.get("bbox")
    if not bbox:
        bbox = get_table_bbox(table.get("cells", []))
    if not bbox:
        return
    draw_polygon_text(img, bbox, line_width=2, line_color="black")

def visual_table(table, img, show_text=True):
    visual_table_box(table, img)
    cells = table.get("cells", [])
    visual_cells(cells, img, show_text)

if __name__ == "__main__":
    from PIL import Image
    from pathlib import Path

    table = {
        "bbox": [
          4,
          4,
          929,
          109
        ],
        "type": "Table",
        "cells": [
            {
                "bbox":[
                466.1,
                4,
                466,
                39.3,
                696,
                39,
                696,
                4
                ],
                "row_start": 0,
                "col_start": 2,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                696,
                4,
                696,
                39,
                927,
                39.5,
                927,
                4
                ],
                "row_start": 0,
                "col_start": 3,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                6,
                74,
                6,
                109,
                236,
                108,
                236,
                74
                ],
                "row_start": 2,
                "col_start": 0,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                466,
                39,
                466,
                74,
                696,
                74,
                696,
                39
                ],
                "row_start": 1,
                "col_start": 2,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                466,
                74,
                466,
                108,
                696,
                108.7,
                696,
                74
                ],
                "row_start": 2,
                "col_start": 2,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                236,
                74,
                236,
                108,
                466,
                108,
                466,
                74
                ],
                "row_start": 2,
                "col_start": 1,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                696,
                39,
                696,
                108,
                927,
                108,
                927,
                39
                ],
                "row_start": 1,
                "col_start": 3,
                "row_span": 2,
                "col_span": 1
            },
            {
                "bbox": [
                236,
                39,
                236,
                74,
                466,
                74.4,
                466,
                39
                ],
                "row_start": 1,
                "col_start": 1,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                6,
                39,
                6,
                74,
                236.6,
                74,
                236,
                39
                ],
                "row_start": 1,
                "col_start": 0,
                "row_span": 1,
                "col_span": 1
            },
            {
                "bbox": [
                6,
                4,
                6,
                39,
                466,
                39,
                466,
                4.9
                ],
                "row_start": 0,
                "col_start": 0,
                "row_span": 1,
                "col_span": 2,
                "text": "我是单元格内的文字"
            }
        ]
    }

    table_img = Image.new('RGBA', (934, 114), color="white")
    visual_table(table, table_img, show_text=True)
    output = str(Path(__file__).parent.parent.parent/"output/images/out_table_img.png")
    table_img.save(output)
