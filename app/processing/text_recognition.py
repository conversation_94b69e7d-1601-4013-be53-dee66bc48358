import logging
import cv2
import yaml
import time
import numpy as np
from typing import List, Dict, Any
from .text_classifier import TextClassifier
from .text_recognizer import TextRecognizer
from ..core.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[35m{time.asctime() + "  " + __name__}\033[0m')


class TextRecognitionService:
    """文本识别服务，负责识别定位后的文本内容"""

    def __init__(self):
        """
        Initializes the OCRRect pipeline.
        """

        with open(settings.TEXT_REC_CONFIG, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        self.classifier = TextClassifier(config['TextClassifier'])
        self.recognizer = TextRecognizer(config['TextRecognizer'])

        # Default parameters from the original OCRPipeline
        pipeline_params = config.get('Pipeline', {})
        self.repeat_rotate = pipeline_params.get('repeat_rotate', True)
        self.pipeline_threshold = pipeline_params.get('pipeline_threshold', 0.8)
        self.global_rotate_threshold = pipeline_params.get('global_rotate_threshold', 0.85)
        self.rotate_conf_threshold = pipeline_params.get('rotate_conf_threshold', 0.9)
        self.rec_conf_threshold = pipeline_params.get('rec_conf_threshold', 0.75)
    
    def recognize(self, image: np.ndarray, text_bboxes: List[List[int]]) -> List[Dict[str, Any]]:
        """识别图像中指定区域的文本

        The process is performed in batches for efficiency:
        1. The classifier processes the boxes to sort them and get cropped images and orientations.
        2. The recognizer processes the cropped images with their orientation data.
        3. The final results are compiled into a list of dictionaries.

        Args:
            image: BGR格式的图像数组 (H, W, C)
            text_bboxes: 文本边界框列表，每个边界框为 [x_min, y_min, x_max, y_max]

        Returns:
            List[Dict[str, Any]]: 文本识别结果列表，每项包含边界框、文本内容和置信度
        """

        start_time = time.time()

        if not text_bboxes:
            return []

        boxes = text_bboxes

        # 直接使用传入的numpy数组，无需重复解码

        # 1. Classify boxes to get angles, scores, sorted boxes, and cropped images.
        angles, scores, dt_boxes, img_crop_list, img_ori_list, _ = self.classifier.classify(
            ori_im=image,
            dt_boxes=boxes
        )

        # 2. Recognize text in the batch of cropped images.
        rec_results, _, _ = self.recognizer.recognize(
            img_list=img_crop_list,
            img_ori_list=img_ori_list,
            rotate_angles=angles,
            rotate_scores=scores,
            repeat_rotate=self.repeat_rotate,
            global_rotate_threshold=self.global_rotate_threshold,
            rotate_conf_threshold=self.rotate_conf_threshold,
            rec_conf_threshold=self.rec_conf_threshold
        )

         # 3. Combine sorted detection boxes with recognition results.
        # Note: rec_results are already in the original input order due to batch_inference restoration
        final_results = []
        for i, box in enumerate(dt_boxes):  # Use original input boxes, not classifier-sorted dt_boxes
            rec_result = rec_results[i]
            
            # Apply the same confidence adjustment and filtering logic as original OCRPipeline
            # Short text confidence boost (from ocr_pipeline.py line 182-183)
            if len(rec_result.get("text", "")) <= 3:
                rec_result['confidence'] = rec_result.get('confidence', 0.0) + 0.15
            
            # Filter by pipeline threshold (from ocr_pipeline.py line 184-185)
            if rec_result.get('confidence', 0.0) < self.pipeline_threshold:
                continue

            final_results.append({
                "words": rec_result.get("text", ""),
                "confidence": float(min(1.0, rec_result.get("confidence", 0.0))),
                "bbox": np.array(box).reshape(-1).tolist(),
                "rotated": rec_result.get("rotate_try", False),
                "rotate_angle": rec_result.get("rotate_angle", 0)
            })

        logger.info(f"  本次文本识别调用耗时为:   {(time.time() - start_time) * 1000} ms")

        return final_results