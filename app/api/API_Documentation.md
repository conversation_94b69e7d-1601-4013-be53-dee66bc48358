# AgileOCR API 

---

## API 端点详细文档

### 基础配置
- **Base URL**: `https://modelzooapi-test-inner.zhhainiao.com`
- **API 版本**: `/api/v1`
- **Content-Type**: `application/json`

### 通用响应格式

所有API响应都遵循统一格式：

```json
{
  "request_id": "uuid-string",
  "status": "ok",
  "error_message": null,
  "results": {
    // 具体结果内容
  }
}
```

### 1. 健康检查 API

#### 1.1 基础健康检查
- **端点**: `GET /health`
- **描述**: 快速健康状态检查
- **响应**:
```json
{
  "status": "ok"
}
```

#### 1.2 详细健康检查
- **端点**: `GET /api/v1/health`
- **描述**: 详细的健康状态信息
- **响应**:
```json
{
  "status": "healthy",
  "service": "agileocr-backend",
  "timestamp": "2025-01-20T10:30:00.000Z",
  "version": "1.0.0"
}
```

#### 1.3 就绪检查
- **端点**: `GET /api/v1/ready`
- **描述**: 服务就绪状态检查
- **响应**:
```json
{
  "status": "ready",
  "service": "agileocr-backend",
  "timestamp": "2025-01-20T10:30:00.000Z"
}
```

#### 1.4 存活检查
- **端点**: `GET /api/v1/live`
- **描述**: 服务存活状态检查
- **响应**:
```json
{
  "status": "alive",
  "service": "agileocr-backend",
  "timestamp": "2025-01-20T10:30:00.000Z"
}
```

### 2. 核心 OCR API (无持久化)

#### 通用请求格式
```json
{
  "image_base64": "base64编码的图片数据",
  "filename": "example.jpg"
}
```

#### 2.1 文本检测
- **端点**: `POST /api/v1/ocr/text_detection`
- **描述**: 检测图像中的文本区域，返回边界框和置信度
- **请求示例**:
```json
{
  "image_base64": "/9j/4AAQSkZJRgABAQAAAQ...",
  "filename": "document.jpg"
}
```
- **响应示例**:
```json
{
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_detection": [
      {
        "bbox": [100.5, 200.3, 300.2, 250.1],
        "confidence": 0.95
      }
    ]
  }
}
```

#### 2.2 文本识别
- **端点**: `POST /api/v1/ocr/text_recognition`
- **描述**: 检测并识别图像中的文本内容
- **响应示例**:
```json
{
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_recognition": [
      {
        "bbox": [100.5, 200.3, 300.2, 250.1],
        "confidence": 0.95,
        "text": {
          "value": "识别的文本内容",
          "confidence": 0.92
        }
      }
    ]
  }
}
```

#### 2.3 表格识别
- **端点**: `POST /api/v1/ocr/table_recognition`
- **描述**: 识别图像中的表格结构和内容
- **响应示例**:
```json
{
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "ok",
  "error_message": null,
  "results": {
    "table_recognition": [
      {
        "bbox": [50.0, 100.0, 500.0, 300.0],
        "confidence": 0.88,
        "cells": [
          {
            "bbox": [60.0, 110.0, 150.0, 130.0],
            "confidence": 0.92,
            "row_start": 0,
            "col_start": 0,
            "row_span": 1,
            "col_span": 1,
            "text": {
              "value": "表头1",
              "confidence": 0.95
            }
          }
        ]
      }
    ]
  }
}
```

#### 2.4 公式识别
- **端点**: `POST /api/v1/ocr/formula_recognition`
- **描述**: 识别图像中的数学公式并转换为LaTeX格式
- **响应示例**:
```json
{
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "ok",
  "error_message": null,
  "results": {
    "formula_recognition": [
      {
        "bbox": [200.0, 150.0, 400.0, 200.0],
        "confidence": 0.89,
        "text": {
          "value": "\\frac{x^2 + y^2}{z}",
          "confidence": 0.91
        }
      }
    ]
  }
}
```

#### 2.5 全量处理
- **端点**: `POST /api/v1/ocr/full_result`
- **描述**: 执行完整的文档分析，包括版面分析、文本识别、表格识别和公式识别
- **响应示例**:
```json
{
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "ok",
  "error_message": null,
  "results": {
    "layout_analysis": [
      {
        "bbox": [50.0, 50.0, 550.0, 100.0],
        "type": "text",
        "confidence": 0.96,
        "text": {
          "value": "这是标题文本",
          "confidence": 0.94
        }
      },
      {
        "bbox": [50.0, 120.0, 550.0, 300.0],
        "type": "table",
        "confidence": 0.88,
        "cells": [
          {
            "bbox": [60.0, 130.0, 150.0, 150.0],
            "confidence": 0.92,
            "row_start": 0,
            "col_start": 0,
            "row_span": 1,
            "col_span": 1,
            "text": {
              "value": "单元格内容",
              "confidence": 0.95
            }
          }
        ]
      }
    ]
  }
}
```

### 3. 前端专用 API (带持久化与IP限制)

前端专用API与核心API功能相同，但具有以下特点：
- **IP访问限制**: 仅允许白名单IP访问
- **结果持久化**: 处理结果会被保存，可通过request_id后续查询
- **路径前缀**: `/api/v1/web/ocr/`

#### 3.1 前端文本检测
- **端点**: `POST /api/v1/web/ocr/text_detection`
- **描述**: 带持久化的文本检测，结果会被保存
- **请求/响应**: 与核心API相同，但结果会被持久化存储

#### 3.2 前端文本识别
- **端点**: `POST /api/v1/web/ocr/text_recognition`
- **描述**: 带持久化的文本识别

#### 3.3 前端表格识别
- **端点**: `POST /api/v1/web/ocr/table_recognition`
- **描述**: 带持久化的表格识别

#### 3.4 前端公式识别
- **端点**: `POST /api/v1/web/ocr/formula_recognition`
- **描述**: 带持久化的公式识别

#### 3.5 前端全量处理
- **端点**: `POST /api/v1/web/ocr/full_result`
- **描述**: 带持久化的完整文档分析

### 4. 结果管理 API

#### 4.1 获取处理结果
- **端点**: `GET /api/v1/web/results/{request_id}`
- **描述**: 根据请求ID获取之前保存的OCR处理结果
- **路径参数**:
  - `request_id`: UUID格式的请求标识符
- **响应**: 返回对应的OCR结果数据

#### 4.2 获取原始图片
- **端点**: `GET /api/v1/web/images/{request_id}`
- **描述**: 获取原始上传的图片文件
- **路径参数**:
  - `request_id`: UUID格式的请求标识符
- **响应**: 返回图片文件的二进制数据 (Content-Type: image/jpeg)

#### 4.3 获取可视化结果
- **端点**: `GET /api/v1/web/visualizations/{request_id}?view_type={type}`
- **描述**: 获取OCR结果的可视化图片，在原图上标注识别结果
- **路径参数**:
  - `request_id`: UUID格式的请求标识符
- **查询参数**:
  - `view_type`: 视图类型，可选值：
    - `text_detection`: 文本检测结果可视化
    - `text_recognition`: 文本识别结果可视化
    - `layout_analysis`: 版面分析结果可视化
    - `table_recognition`: 表格识别结果可视化
    - `formula_recognition`: 公式识别结果可视化
    - `full_result`: 全量分析结果可视化
- **响应**: 返回PNG格式的可视化图片

### 5. 错误处理

#### 5.1 客户端错误 (4xx)
```json
{
  "request_id": "",
  "status": "client_error_code",
  "error_message": "错误描述信息",
  "results": null
}
```

#### 5.2 服务器错误 (5xx)
```json
{
  "request_id": "",
  "status": "server_error_code",
  "error_message": "服务器内部错误",
  "results": null
}
```

#### 5.3 IP访问被拒绝 (403)
```json
{
  "request_id": "",
  "status": "client_forbidden",
  "error_message": "Access denied for IP: xxx.xxx.xxx.xxx",
  "results": null
}
```

### 6. 使用示例

#### 6.1 完整工作流程示例

```bash
# 1. 提交图片进行OCR处理（前端API，带持久化）
curl -X POST "http://your-domain.com/api/v1/web/ocr/full_result" \
  -H "Content-Type: application/json" \
  -d '{
    "image_base64": "/9j/4AAQSkZJRgABAQAAAQ...",
    "filename": "document.pdf"
  }'

# 响应获得 request_id: "123e4567-e89b-12d3-a456-426614174000"

# 2. 获取原始图片
curl "http://your-domain.com/api/v1/web/images/123e4567-e89b-12d3-a456-426614174000" \
  --output original_image.jpg

# 3. 获取可视化结果
curl "http://your-domain.com/api/v1/web/visualizations/123e4567-e89b-12d3-a456-426614174000?view_type=full_result" \
  --output visualization.png

# 4. 重新获取处理结果
curl "http://your-domain.com/api/v1/web/results/123e4567-e89b-12d3-a456-426614174000"
```

#### 6.2 快速处理示例（无持久化）

```bash
# 仅进行文本识别，不保存结果
curl -X POST "http://your-domain.com/api/v1/ocr/text_recognition" \
  -H "Content-Type: application/json" \
  -d '{
    "image_base64": "/9j/4AAQSkZJRgABAQAAAQ...",
    "filename": "simple_text.jpg"
  }'
```

### 7. IP访问限制说明

前端专用API (`/api/v1/web/*`) 受IP白名单限制，默认允许的IP范围：
- `************/24`
- `127.0.0.1` (localhost)
- `::1` (IPv6 localhost)
- `***********/16` (私有网络)
- `*********/16` (私有网络)

核心API (`/api/v1/ocr/*`) 和健康检查API不受IP限制。

### 8. 注意事项

1. **图片格式**: 支持JPEG、PNG等常见格式，需要Base64编码传输
2. **文件大小**: 建议单个图片不超过10MB
3. **并发限制**: 系统支持并发处理，但建议控制并发数量
4. **请求ID**: 所有响应都包含唯一的request_id，用于结果追踪
5. **缓存策略**: 持久化结果会被保存一定时间，具体保留期限请咨询管理员

### 9. SDK和示例代码

Python示例：
```python
import requests
import base64
import json

def ocr_text_recognition(image_path, api_base_url):
    # 读取并编码图片
    with open(image_path, 'rb') as f:
        image_data = base64.b64encode(f.read()).decode('utf-8')
    
    # 构建请求
    payload = {
        "image_base64": image_data,
        "filename": image_path.split('/')[-1]
    }
    
    # 发送请求
    response = requests.post(
        f"{api_base_url}/api/v1/ocr/text_recognition",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    return response.json()

# 使用示例
result = ocr_text_recognition("document.jpg", "http://your-domain.com")
print(json.dumps(result, indent=2, ensure_ascii=False))
``` 