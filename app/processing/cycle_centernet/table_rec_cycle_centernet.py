import logging
import numpy as np
import cv2
import time
from typing import List, Dict, Any

from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks

from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService
from app.processing.table_recognition import TableRecognitionService
from app.utils.table_logic import process_table_structure, sort_cells
from app.utils import image_locator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[33m{time.asctime() + "  " + __name__}\033[0m')

class CycleCenterNetTable(TableRecognitionService):
    """基于CycleCenterNet的表格识别服务，负责识别表格结构和内容"""
    
    def __init__(self, config: Dict, text_recognition: TextRecognitionService, text_detection: TextDetectionService = None):
        """初始化表格识别服务
        
        Args:
            config: 配置字典，包含模型路径和设备信息
            text_recognition: 文本识别服务实例
            text_detection: 文本检测服务实例（可选）
        """
        super().__init__(config, text_recognition, text_detection)
        
        # 初始化CycleCenterNet模型
        self.model = pipeline(
            Tasks.table_recognition, 
            model='iic/cv_dla34_table-structure-recognition_cycle-centernet'
        )
        
        logger.info("  CycleCenterNet表格识别服务初始化完成")
    
    def recognize_table(self, image: np.ndarray, table_bbox: List[int] = None, text_results=None) -> Dict[str, Any]:
        """识别图像中的表格结构和内容

        Args:
            image: BGR格式的图像数组 (H, W, C)
            table_bbox: 可选的表格边界框 [x_min, y_min, x_max, y_max]

        Returns:
            Dict[str, Any]: 表格识别结果，包含单元格信息
        """
        logger.debug(f"  开始识别表格，边界框: {table_bbox if table_bbox else '整图'}")
        start_time = time.time()

        # 直接使用传入的numpy数组，无需重复解码
        original_height, original_width = image.shape[:2]
        
        # 如果提供了表格边界框，裁剪图像
        offset_x, offset_y = 0, 0
        if table_bbox: # 暂时关闭裁剪再识别False and 
            x_min, y_min, x_max, y_max = table_bbox
            # 确保边界框在图像范围内
            x_min = max(0, x_min - 100)
            y_min = max(0, y_min - 100)
            x_max = min(original_width, x_max + 100)
            y_max = min(original_height, y_max + 100)
            
            # 保存偏移量，用于后续坐标映射
            offset_x, offset_y = x_min, y_min
            
            # 裁剪图像
            table_image = image[y_min:y_max, x_min:x_max]
            logger.info(f"  裁剪表格区域: [{x_min}, {y_min}, {x_max}, {y_max}]")
        else:
            table_image = image
        
        # 调用CycleCenterNet模型识别表格结构
        result = self.model(table_image)
        cell_polygons = result["polygons"].tolist()
        logger.info(f"  CycleCenterNet模型识别到 {len(cell_polygons)} 个单元格")
        
        # 如果有偏移，调整单元格边界框坐标
        if offset_x > 0 or offset_y > 0:
            adjusted_polygons = []
            for polygon in cell_polygons:
                adjusted_polygon = []
                for i in range(0, len(polygon), 2):
                    adjusted_polygon.extend([polygon[i] + offset_x, polygon[i+1] + offset_y])
                adjusted_polygons.append(adjusted_polygon)
            cell_polygons = adjusted_polygons
        
        # 使用table_logic处理表格结构
        structured_cells = process_table_structure(cell_polygons, image_size=(original_width,original_height))
        
        # 按照从上到下、从左到右的顺序对单元格进行排序
        sort_cells(structured_cells)
        
        # 创建表格区域的边界框，用于文字检测
        if table_bbox:
            # 如果提供了表格边界框，直接使用
            table_region = table_bbox
        else:
            # 如果没有提供表格边界框，使用所有单元格边界框的外接矩形
            all_x_coords = []
            all_y_coords = []
            for polygon in cell_polygons:
                for i in range(0, len(polygon), 2):
                    all_x_coords.append(polygon[i])
                    all_y_coords.append(polygon[i+1])
            
            x_min = min(all_x_coords)
            y_min = min(all_y_coords)
            x_max = max(all_x_coords)
            y_max = max(all_y_coords)
            table_region = [x_min, y_min, x_max, y_max]
        
        # 对表格区域进行文字检测和识别
        if not text_results:
            text_results = self._detect_and_recognize_text(image, table_region)
        
        # 将结果转换为标准格式
        result = self._convert_to_table_result(structured_cells, text_results)
        
        logger.info("  表格识别完成")
        end_time = time.time()
        logger.info(f"  本次表格识别调用耗时为:   {(end_time - start_time) * 1000} ms")
        return result
    

    def _detect_and_recognize_text(self, image: np.ndarray, table_region: List[float]) -> List[Dict[str, Any]]:
        """对表格区域进行文字检测和识别

        Args:
            image: 原始图像的numpy数组
            table_region: 表格区域边界框 [x_min, y_min, x_max, y_max]

        Returns:
            List[Dict[str, Any]]: 文本识别结果列表
        """
        text_results = []

        if self.text_detection is not None:
            # 如果提供了文字检测服务，使用它对表格区域进行文字检测
            logger.info("对表格区域进行文字检测")

            # 裁剪表格区域图像
            x_min, y_min, x_max, y_max = table_region
            table_region_image = image[int(y_min):int(y_max), int(x_min):int(x_max)]

            # 对表格区域进行文字检测（使用数组接口）
            text_bboxes = self.text_detection.detect(table_region_image)

            # 调整文字边界框坐标，加上表格区域的偏移量
            for i in range(len(text_bboxes)):
                # 需要将每个点的坐标都加上偏移量
                text_bboxes[i] = image_locator.move_polygon(text_bboxes[i], x_min, y_min)

            # 对检测到的文字进行识别（使用数组接口）
            text_results = self.text_recognition.recognize(image, text_bboxes)
            logger.info(f"检测到 {len(text_bboxes)} 个文字区域")
        else:
            # 如果没有提供文字检测服务，记录警告
            logger.warning("未提供文字检测服务，将无法进行文字识别")

        return text_results

    def _convert_to_table_result(self, structured_cells: List[Dict], text_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """将结构化单元格和文本识别结果转换为标准的表格识别结果格式

        Args:
            structured_cells: 结构化单元格列表，包含bbox和行列信息
            text_results: 文本识别结果列表

        Returns:
            Dict[str, Any]: 标准格式的表格识别结果
        """
        # 创建单元格列表
        cells = []

        for i, cell_info in enumerate(structured_cells):
            bbox = cell_info["bbox"]
            row_start = cell_info["row_start"]
            col_start = cell_info["col_start"]
            row_span = cell_info["row_span"]
            col_span = cell_info["col_span"]

            # 找到与当前单元格重叠的文本
            cell_text = ""
            cell_confidence = 0.0
            text_count = 0

            for text_item in text_results:
                text_bbox = text_item["bbox"]
                inter_poly = image_locator.inter_polygon(bbox, text_bbox)
                text_area = image_locator.polygon_area(text_bbox)
                text_in_cell_ratio = 0
                if inter_poly and text_area:
                    text_in_cell_ratio = inter_poly.area / text_area
                # 如果文本边界框至少70%在单元格内，认为文本属于这个单元格
                if text_in_cell_ratio >= 0.7:
                    if cell_text:
                        cell_text += " "  # 如果有多个文本块，用空格连接
                    cell_text += text_item["words"]
                    cell_confidence += text_item["confidence"]
                    text_count += 1

                    # 添加调试日志
                    logger.debug(f"单元格 #{i} 匹配到文本: '{text_item['words']}' (覆盖率={text_in_cell_ratio:.2f})")

            # 计算平均置信度
            if text_count > 0:
                cell_confidence /= text_count
            else:
                cell_confidence = 0.9  # 默认置信度

            # 如果没有找到文本，使用空字符串
            if not cell_text:
                cell_text = ""
                logger.debug(f"单元格 #{i} 未匹配到文本，位置: 行={row_start}, 列={col_start}")

            # 创建单元格对象
            cell = {
                "bbox": bbox,
                "row_start": row_start,
                "col_start": col_start,
                "row_span": row_span,
                "col_span": col_span,
                "text": {
                    "value": cell_text,
                    "confidence": float(cell_confidence)
                },
                "confidence": 0.9  # 表格结构识别的置信度（默认值）
            }
            cells.append(cell)

        # 创建结果字典
        sort_cells(cells)
        result = {
            "confidence": 0.9,
            "cells": cells
        }

        return result
