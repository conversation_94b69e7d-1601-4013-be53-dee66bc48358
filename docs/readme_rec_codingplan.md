# 开发计划: OCR_RECT 模块重构

本文档为 `OCR_RECT` 模块的重构任务提供了详细的、渐进式的编码步骤。计划的核心是确保每一步都可独立完成和验证，并严格遵循“忠实复用”原则。

---

## 1. 目录结构树

重构完成后，我们将得到一个独立的 `ocr_rect_dev` 模块，其结构如下：

```
ocr_rect_dev/
├── __init__.py         # 模块入口，暴露 OCRRect 类
├── ocr_rect.py         # 定义 OCRRect 流程编排类
├── classifier.py       # 定义 TextClassifier 类及图像裁剪辅助函数
├── recognizer.py       # 定义 TextRecognizer 类及图像旋转辅助函数
├── configs/
│   └── rec_config_template.yaml # 模块配置文件模板
└── requirements.txt    # 模块的全部依赖
```

---

## 2. 受影响的现有模块

本次任务是**创建全新的、独立的 `ocr_rect_dev` 模块**，该模块不依赖于原始项目的任何非模型、非配置的 `.py` 文件。因此，**本次重构不会修改或影响任何现有的模块**。我们将从现有代码中复制和重组逻辑，而不是直接修改它们。

---

## 3. 渐进式小步迭代开发步骤

我们将按照“由内而外”的顺序，先构建原子能力，再组装成完整流程。

### **步骤 1: 创建模块骨架**

-   **任务**: 创建 `ocr_rect_dev` 模块的目录结构和所有空的 Python 文件。
-   **操作**:
    1.  创建顶级目录 `ocr_rect_dev/`。
    2.  在 `ocr_rect_dev/` 内创建子目录 `configs/`。
    3.  在 `ocr_rect_dev/` 内创建空的 `__init__.py`, `ocr_rect.py`, `classifier.py`, `recognizer.py`, `requirements.txt`。
    4.  在 `ocr_rect_dev/configs/` 内创建空的 `rec_config_template.yaml`。
-   **验证**: 检查目录和文件结构是否与设计文档一致。

### **步骤 2: 实现原子能力 - `classifier.py` (代码迁移)**

-   **任务**: 通过直接的代码迁移，实现完整的 `TextClassifier` 类及其辅助函数。
-   **操作 (严格复制，避免重写)**:
    1.  **迁移 `crop_image` 函数**: 将 `modules/utils/utils.py` 中 `get_minarea_rect_crop` 函数的 **完整代码** 复制到 `classifier.py` 中，并重命名为 `crop_image`。
    2.  **迁移预处理与后处理逻辑**:
        -   在 `classifier.py` 内部，定义一个私有类 `_ClsPreProcess`，将其 **完整代码** 从 `modules/pre_process/cls_pre_proc.py` 的 `ClsPreProcess` 类复制过来。
        -   同样，定义一个私有类 `_ClsPostProcess`，将其 **完整代码** 从 `modules/post_process/cls_post_proc.py` 的 `ClsPostProcess` 类复制过来。
    3.  **实现 `TextClassifier` 类**: 
        -   **`__init__`**: **复制** `modules/predictors/classifier.py` 中 `TextClassifier.__init__` 的代码。修改其内容，使其不再继承，而是直接实例化内部的 `_ClsPreProcess` 和 `_ClsPostProcess` 类。
        -   **`classify`**: **完整复制** `modules/predictors/classifier.py` 中 `TextClassifier.classify` 方法的代码。
-   **验证**: `classifier.py` 成为一个功能完备的组件。可通过临时脚本验证其功能。

### **步骤 3: 实现原子能力 - `recognizer.py` (代码迁移)**

-   **任务**: 通过直接的代码迁移，实现完整的 `TextRecognizer` 类及其辅助函数。
-   **操作 (严格复制，避免重写)**:
    1.  **迁移 `rotate_image` 函数**: 从 `modules/pre_process/rec_pre_proc.py` 的 `RecPreProcess` 类中，**复制** 处理图像旋转和尺寸重塑的相关代码行，组合成一个独立的 `rotate_image` 函数，放置在 `recognizer.py` 中。
    2.  **迁移预处理与后处理逻辑**:
        -   在 `recognizer.py` 内部，定义私有类 `_RecPreProcess`，将其代码（除已提取的旋转逻辑外）从 `modules/pre_process/rec_pre_proc.py` 的 `RecPreProcess` 类 **复制** 过来。
        -   定义私有类 `_RecPostProcess` 和 `_CTCLabelDecode`，将它们的 **完整代码** 分别从 `modules/post_process/rec_post_proc.py` 中的 `RecPostProcess` 和 `CTCLabelDecode` 类 **复制** 过来。
    3.  **实现 `TextRecognizer` 类**:
        -   **`__init__`**: **复制** `modules/predictors/recognizer.py` 中 `TextRecognizer.__init__` 的代码，并修改以实例化内部的预/后处理类。
        -   **`recognize` / `batch_inference`**: **完整复制** `modules/predictors/recognizer.py` 中同名方法的代码。
-   **验证**: `recognizer.py` 成为另一个功能完备的组件。同样，可通过临时脚本验证其功能。

### **步骤 4: 实现流程编排器 - `ocr_rect.py`**

-   **任务**: 实现 `OCRRect` 类，将 `classifier` 和 `recognizer` 的能力编排成一个完整的流水线。
-   **操作**:
    1.  **实现 `__init__`**: 读取 YAML 配置文件，并根据配置分别实例化 `classifier.TextClassifier` 和 `recognizer.TextRecognizer`。
    2.  **实现 `run` 方法**: 复刻 `modules/pipelines/ocr_pipeline.py` 中 `OCRPipeline.process_image` 的核心流程，但调用的是我们新模块内的函数和方法：
        ```python
        # 伪代码
        def run(self, image, boxes):
          results = []
          for box in boxes:
            cropped = classifier.crop_image(image, box)
            direction, _ = self.classifier.classify(cropped)
            rotated = recognizer.rotate_image(cropped, direction)
            text, conf = self.recognizer.recognize(rotated)
            results.append({'bbox': box, 'words': text, ...})
          return results
        ```
-   **验证**: 此时，整个模块的核心逻辑已经完成。但要运行，还需要配置。

### **步骤 5: 填充配置与依赖**

-   **任务**: 完成最后的配置文件和依赖项声明。
-   **操作**:
    1.  **填充 `rec_config_template.yaml`**: 将 `README_REC_LLD.md` 中最终确定的配置内容复制到该文件中。
    2.  **填充 `requirements.txt`**: 分析原始代码依赖，至少包含 `onnxruntime`, `numpy`, `pyyaml`, `opencv-python`，并锁定版本。
-   **验证**: 模块现在是完全自包含且可配置的了。

### **步骤 6: 创建集成测试入口**

-   **任务**: 编写一个简单的入口脚本（例如 `test_run.py`，**位于 `ocr_rect` 模块之外**），用于演示和验证整个模块的功能。
-   **操作**:
    1.  加载一张测试图片和预设的边框坐标。
    2.  从 `ocr_rect` 模块导入 `OCRRect` 类。
    3.  实例化 `OCRRect`，传入上一步创建的配置文件路径。
    4.  调用 `run` 方法并打印结果。
-   **验证**: 成功运行 `test_run.py` 并获得预期的识别结果，标志着整个重构任务的成功完成。
