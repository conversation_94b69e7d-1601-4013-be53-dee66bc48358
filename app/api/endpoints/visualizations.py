from fastapi import APIRouter, Depends, Query
from fastapi.responses import FileResponse
from uuid import UUID
import logging

from app.services.visualization_service import VisualizationService
from app.services import injector
from app.services.persistence import PersistenceService
from app.services.image_service import ImageService
from app.core.config import settings
from app.core import errors

logger = logging.getLogger(__name__)

router = APIRouter()

_visualization_service = None
def get_visualization_service() -> VisualizationService:
    """依赖注入：获取可视化服务实例"""
    global _visualization_service
    if not _visualization_service:
        persistence_service = injector.get_persistence_service()
        image_service = injector.get_image_service()
        _visualization_service = VisualizationService(
            visualizations_dir=settings.VISUALIZATIONS_DIR,
            persistence_service=persistence_service,
            image_service=image_service
        )
    return _visualization_service

@router.get("/{request_id}")
async def get_visualization_image(
    request_id: UUID,
    view_type: str = Query(..., description="视图类型"),
    service: VisualizationService = Depends(get_visualization_service)
) -> FileResponse:
    """获取可视化结果图
    
    Args:
        request_id: 请求ID
        view_type: 视图类型（text_detection, text_recognition, layout_analysis, 
                   table_recognition, formula_recognition, full_result）
        service: 可视化服务实例（通过依赖注入）
        
    Returns:
        FileResponse: 可视化结果图的二进制响应
    """
    logger.info(f"请求获取可视化结果: {request_id}, view_type: {view_type}")
    
    # 验证view_type参数
    valid_view_types = [
        'text_detection', 'text_recognition', 'layout_analysis',
        'table_recognition', 'formula_recognition', 'full_result'
    ]
    if view_type not in valid_view_types:
        raise errors.ClientErr(errors.ErrCode.CLIENT_BAD_PARAMS, f"无效的视图类型{view_type}")
    
    visualization_path = service.get_or_create_visualization(request_id, view_type)
    logger.info(f"返回可视化结果图: {visualization_path}")
    
    return FileResponse(
        path=str(visualization_path),
        media_type="image/png",
        filename=f"{request_id}_{view_type}.png"
    )