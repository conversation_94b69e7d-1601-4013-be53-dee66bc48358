# 公式识别批量处理详细设计文档 (LLD)

## 项目结构与总体设计

本次改造的核心目标是将公式识别服务从单边框处理模式改为批量边框处理模式，以提高GPU利用率和整体处理效率。改造遵循最小化影响原则，仅修改必要的接口和调用逻辑，不涉及服务初始化和其他无关组件。

### 设计原则
- **简约至上**: 仅修改必要的接口，保持现有架构不变
- **批量优化**: 充分利用GPU批处理能力，提高计算效率
- **顺序一致**: 确保批量处理结果与输入边框顺序严格对应

## 目录结构树 (Directory Tree)

```text
app/
├── processing/
│   └── formula_recognition.py          # 【修改】公式识别服务核心逻辑
└── services/
    └── orchestration.py                # 【修改】编排服务调用逻辑
```

## 整体逻辑和交互时序图

### 改造前后对比

**改造前流程**:
```
版面分析 → 筛选公式边框 → 循环调用公式识别服务
```

**改造后流程**:
```
版面分析 → 筛选公式边框 → 批量调用公式识别服务
```

### 批量处理时序图

```mermaid
sequenceDiagram
    participant Orch as OrchestrationService
    participant Layout as LayoutAnalysisService
    participant Formula as FormulaRecognitionService
    participant GPU as GPU计算资源

    Orch->>Layout: analyze(image_bytes)
    Layout-->>Orch: 返回版面分析结果
    Note over Orch: 筛选出所有公式边框
    
    Orch->>Formula: recognize(image_bytes, formula_bboxes)
    Note over Formula: 批量处理开始
    
    loop 按batch_size分批处理
        Formula->>GPU: 批量推理 (batch_size个公式)
        GPU-->>Formula: 返回批量结果
    end
    
    Note over Formula: 合并所有批次结果
    Formula-->>Orch: 返回批量识别结果列表
    
    Note over Orch: 构造最终响应
```

## 数据实体结构深化

### 输入输出数据结构

```mermaid
erDiagram
    FormulaRecognitionInput {
        bytes image_bytes "图像二进制数据"
        List formula_bboxes "公式边框列表"
    }
    
    FormulaBbox {
        int x_min "左上角X坐标"
        int y_min "左上角Y坐标" 
        int x_max "右下角X坐标"
        int y_max "右下角Y坐标"
    }
    
    FormulaRecognitionOutput {
        List results "识别结果列表"
    }
    
    FormulaResult {
        string latex_string "LaTeX公式字符串"
        float confidence "置信度"
    }
    
    FormulaRecognitionInput ||--o{ FormulaBbox : contains
    FormulaRecognitionOutput ||--o{ FormulaResult : contains
```

### 批处理数据流

```mermaid
erDiagram
    BatchProcessing {
        List input_bboxes "输入边框列表"
        int batch_size "批处理大小"
        List batches "分批后的批次列表"
        List results "最终结果列表"
    }
    
    Batch {
        List bbox_subset "当前批次的边框子集"
        List cropped_images "裁剪后的图像列表"
        List batch_results "当前批次的结果"
    }
    
    BatchProcessing ||--o{ Batch : split_into
```

## 配置项

### 新增配置项
- `FORMULA_BATCH_SIZE`: 公式识别批处理大小，默认值为4

### 现有配置项（保持不变）
- `FORMULA_WEIGHTS`: 公式识别模型权重路径

## 涉及到的文件详解 (File-by-File Breakdown)

### app/processing/formula_recognition.py

#### 文件用途说明
公式识别服务的核心实现，负责将输入的图像和公式边框列表进行批量识别，返回对应的LaTeX公式字符串和置信度。

#### 文件内类图

```mermaid
classDiagram
    class FormulaRecognitionService {
        -device: torch.device
        -model: VisionEncoderDecoderModel
        -processor: TrOCRProcessor
        -batch_size: int
        +__init__()
        +recognize(image_bytes, formula_bboxes) List~Dict~
        -_process_batch(image, bbox_batch) List~Dict~
        -_crop_images(image, bboxes) List~Image~
    }
```

#### 函数/方法详解

##### __init__方法
- **用途**: 初始化公式识别服务，加载模型和处理器
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  ```
  1. 设置GPU设备
  2. 加载预训练模型到GPU
  3. 初始化TrOCR处理器
  4. 设置默认batch_size为4
  5. 记录初始化日志
  ```

##### recognize方法（核心改造方法）
- **用途**: 批量识别图像中多个指定区域的数学公式
- **输入参数**: 
  - `image_bytes: bytes` - 图像的二进制数据
  - `formula_bboxes: List[List[int]]` - 公式边界框列表，每个边框格式为[x_min, y_min, x_max, y_max]
- **输出**: `List[Dict[str, Any]]` - 公式识别结果列表，每个结果包含latex_string和confidence
- **实现步骤和要点**:
  ```
  1. 记录开始时间
  2. 将image_bytes转换为PIL Image对象
  3. 如果formula_bboxes为空，返回空列表
  4. 按batch_size将formula_bboxes分批
  5. 对每个批次调用_process_batch方法
  6. 合并所有批次的结果
  7. 确保结果顺序与输入边框顺序一致
  8. 记录处理耗时
  9. 返回结果列表
  ```

##### _process_batch方法（新增私有方法）
- **用途**: 处理单个批次的公式识别
- **输入参数**:
  - `image: PIL.Image` - 原始图像对象
  - `bbox_batch: List[List[int]]` - 当前批次的边框列表
- **输出**: `List[Dict[str, Any]]` - 当前批次的识别结果
- **实现步骤和要点**:
  ```
  1. 根据边框列表裁剪图像，得到cropped_images
  2. 使用processor处理图像列表，转换为tensor
  3. 将tensor移动到GPU设备
  4. 调用model.generate进行批量推理
  5. 使用processor.batch_decode解码结果
  6. 构造结果字典列表，包含latex_string和confidence
  7. 返回批次结果
  ```

##### _crop_images方法（新增私有方法）
- **用途**: 根据边框列表批量裁剪图像
- **输入参数**:
  - `image: PIL.Image` - 原始图像对象
  - `bboxes: List[List[int]]` - 边框列表
- **输出**: `List[PIL.Image]` - 裁剪后的图像列表
- **实现步骤和要点**:
  ```
  1. 遍历边框列表
  2. 对每个边框执行image.crop操作
  3. 将裁剪结果添加到列表
  4. 返回裁剪图像列表
  ```

### app/services/orchestration.py

#### 文件用途说明
编排服务，协调各个处理服务的调用。需要修改两个方法的公式识别调用逻辑，从循环调用改为批量调用。

#### 涉及修改的方法

##### process_formula_recognition方法
- **用途**: 处理专门的公式识别请求
- **修改要点**:
  ```
  原逻辑：
  for formula_bbox in formula_bboxes:
      result = self.formula_recognition_service.recognize(image_bytes, formula_bbox)
      
  新逻辑：
  results = self.formula_recognition_service.recognize(image_bytes, formula_bboxes)
  ```
- **实现步骤**:
  ```
  1. 保持版面分析逻辑不变
  2. 收集所有公式边框到formula_bboxes列表
  3. 一次性调用公式识别服务的批量接口
  4. 遍历批量结果，构造FormulaRecognitionItem对象
  5. 保持响应格式不变
  ```

##### process_full_result方法
- **用途**: 处理全量OCR请求中的公式识别部分
- **修改要点**:
  ```
  原逻辑：
  if item_type_lower == "formula":
      result = self.formula_recognition_service.recognize(image_bytes, item["bbox"])
      
  新逻辑：
  # 先收集所有公式边框
  formula_items = [item for item in layout_items_raw if item["type"].lower() == "formula"]
  if formula_items:
      formula_bboxes = [item["bbox"] for item in formula_items]
      formula_results = self.formula_recognition_service.recognize(image_bytes, formula_bboxes)
      # 将结果与对应的layout_item关联
  ```
- **实现步骤**:
  ```
  1. 在处理版面分析结果时，先收集所有公式类型的item
  2. 提取公式边框列表
  3. 批量调用公式识别服务
  4. 将批量结果与对应的layout_item进行关联
  5. 保持其他类型区域的处理逻辑不变
  ```

## 迭代演进依据

### 设计的可扩展性

1. **批处理策略可配置化**
   - 当前batch_size硬编码为4，未来可轻松改为配置项
   - 可支持动态batch_size调整策略

2. **错误处理机制预留**
   - 当前设计专注核心功能，未来可在_process_batch方法中添加异常处理
   - 可支持部分失败时的降级处理策略

3. **性能监控扩展**
   - 当前只记录总耗时，未来可添加批次级别的性能监控
   - 可支持GPU内存使用情况监控

4. **接口兼容性**
   - 保持现有的依赖注入和服务初始化逻辑
   - 未来如需支持多种批处理模式，可通过策略模式扩展

### 模块化优势

1. **单一职责**
   - FormulaRecognitionService专注于公式识别逻辑
   - OrchestrationService专注于服务编排
   - 各模块职责清晰，便于独立演进

2. **低耦合设计**
   - 批量处理逻辑封装在FormulaRecognitionService内部
   - 调用方只需要修改调用方式，无需关心内部实现

3. **测试友好**
   - 新增的私有方法便于单元测试
   - 批量处理逻辑可独立验证

---

**文档版本**: v1.0  
**创建日期**: 2025-06-30  
**设计原则**: KISS + YAGNI + 迭代演进
