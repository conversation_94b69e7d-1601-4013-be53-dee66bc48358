#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/6/5 10:45
# <AUTHOR> <EMAIL>
# @FileName: file_folder_path

import os
import requests
from tqdm import tqdm

root_dir = "/appdata/aipdf/"

agileocr_cache_dir = os.path.join(root_dir, "agileocr")
os.makedirs(agileocr_cache_dir, exist_ok=True)


def urldownload_progressbar(url, file_path):
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    progress_bar = tqdm(total=total_size, unit='B', unit_scale=True)
    with open(file_path, 'wb') as f:
        for chunk in response.iter_content(1024):
            if chunk:
                f.write(chunk)
                progress_bar.update(len(chunk))

    progress_bar.close()
