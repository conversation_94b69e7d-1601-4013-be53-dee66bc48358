// 顶层响应结构
export interface OCRResponse {
  request_id: string;
  status: 'ok' | 'error';
  error_message: string | null;
  results: Results | null;
}

// Results对象
export interface Results {
  text_detection?: { bbox: [number, number, number, number]; confidence: number; }[];
  text_recognition?: TextRecognitionItem[];
  layout_analysis?: LayoutAnalysisItem[];
  table_recognition?: TableRecognitionItem[];
  formula_recognition?: FormulaRecognitionItem[];
}

// 文本内容模型（嵌套结构）
export interface TextContent {
  value: string;
  confidence: number;
}

// 文本识别条目
export interface TextRecognitionItem {
  bbox: [number, number, number, number];
  confidence: number;  // 来自文本检测的置信度
  text: TextContent;   // 嵌套的文本内容
}

// 版面分析条目
export interface LayoutAnalysisItem {
  bbox: [number, number, number, number];
  type: 'title' | 'table' | 'formula' | 'image' | 'text' | 'unknown';
  confidence: number;
  text?: TextContent;    // 用于文本类型的区域
  cells?: TableCell[];   // 用于表格类型的区域
}

export interface TableCell {
  bbox: [number, number, number, number];
  confidence: number;  // 来自表格识别的置信度
  row_start: number;
  col_start: number;
  row_span: number;
  col_span: number;
  text: TextContent;   // 嵌套的文本内容
}

// 表格识别响应项
export interface TableRecognitionItem {
  bbox: [number, number, number, number];
  confidence: number;
  cells: TableCell[];  // 直接包含cells数组
}

// 公式识别响应项
export interface FormulaRecognitionItem {
  bbox: [number, number, number, number];
  confidence: number;  // 来自版面分析的置信度
  text: TextContent;   // 嵌套的文本内容
}

// 单独的API响应类型
export interface TableRecognitionResponse {
  request_id: string;
  status: 'ok' | 'error';
  error_message: string | null;
  results: {
    table_recognition: TableRecognitionItem[];
  };
}

export interface FormulaRecognitionResponse {
  request_id: string;
  status: 'ok' | 'error';
  error_message: string | null;
  results: {
    formula_recognition: FormulaRecognitionItem[];
  };
}

// 视图类型
export type ViewType = 'text_detection' | 'text_recognition' | 'layout_analysis' | 'table_recognition' | 'formula_recognition' | 'full_result'; 