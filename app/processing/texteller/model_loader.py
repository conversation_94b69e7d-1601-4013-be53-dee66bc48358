import logging
from pathlib import Path

import wget
from onnxruntime import InferenceSession
from transformers import RobertaTokenizerFast

from .constants import LATEX_DET_MODEL_URL, TEXT_DET_MODEL_URL, TEXT_REC_MODEL_URL
from .texteller_model import TexTeller
from .device_utils import cuda_available
from .path_utils import mkdir, resolve_path
from .types import TexTellerModel

logger = logging.getLogger(__name__)


def load_model(model_dir: str = None, use_onnx: bool = False, repo_name: str = "OleehyO/TexTeller") -> TexTellerModel:
    """
    Load the TexTeller model for LaTeX recognition.

    This function loads the main TexTeller model, which is responsible for
    converting images to LaTeX. It can load either the standard PyTorch model
    or the optimized ONNX version.

    Args:
        model_dir: Directory containing the model files. If None, uses the default model.
        use_onnx: Whether to load the ONNX version of the model for faster inference.
                  Requires the 'optimum' package and ONNX Runtime.
        repo_name: Hugging Face repository name for the model.

    Returns:
        Loaded TexTeller model instance

    Example:
        >>> from app.processing.texteller.model_loader import load_model
        >>>
        >>> model = load_model(use_onnx=True)
    """
    return TexTeller.from_pretrained(model_dir, use_onnx=use_onnx, repo_name=repo_name)


def load_tokenizer(tokenizer_dir: str = None, repo_name: str = "OleehyO/TexTeller") -> RobertaTokenizerFast:
    """
    Load the tokenizer for the TexTeller model.

    This function loads the tokenizer used by the TexTeller model for
    encoding and decoding LaTeX sequences.

    Args:
        tokenizer_dir: Directory containing the tokenizer files. If None, uses the default tokenizer.
        repo_name: Hugging Face repository name for the tokenizer.

    Returns:
        RobertaTokenizerFast instance

    Example:
        >>> from app.processing.texteller.model_loader import load_tokenizer
        >>>
        >>> tokenizer = load_tokenizer()
    """
    return TexTeller.get_tokenizer(tokenizer_dir, repo_name=repo_name)


def load_latexdet_model(cache_dir: str | Path = None) -> InferenceSession:
    """
    Load the LaTeX detection model.

    This function loads the model responsible for detecting LaTeX formulas in images.
    The model is implemented as an ONNX InferenceSession for optimal performance.

    Args:
        cache_dir: Directory to cache downloaded models. If None, uses default cache.

    Returns:
        ONNX InferenceSession for LaTeX detection

    Example:
        >>> from app.processing.texteller.model_loader import load_latexdet_model
        >>>
        >>> detector = load_latexdet_model()
    """
    fpath = _maybe_download(LATEX_DET_MODEL_URL, cache_dir)
    return InferenceSession(
        resolve_path(fpath),
        providers=["CUDAExecutionProvider" if cuda_available() else "CPUExecutionProvider"],
    )


def _maybe_download(url: str, dirpath: str | Path | None = None, force: bool = False) -> Path:
    """
    Download a file if it doesn't already exist.

    Args:
        url: URL to download from
        dirpath: Directory to save the file in. If None, uses the default cache directory.
        force: Whether to force download even if the file already exists

    Returns:
        Path to the downloaded file
    """
    if dirpath is None:
        # Use a default cache directory in the project
        dirpath = Path("~/.cache/texteller").expanduser().resolve()
    mkdir(dirpath)

    fname = Path(url).name
    fpath = Path(dirpath) / fname
    if not fpath.exists() or force:
        logger.info(f"Downloading {fname} from {url} to {fpath}")
        wget.download(url, resolve_path(fpath))

    return fpath
