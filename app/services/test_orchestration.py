import unittest
from unittest import mock
import uuid
import base64
from io import BytesIO

from app.services.orchestration import OrchestrationService
from app.processing.text_detection import TextDetectionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.layout_analysis import LayoutAnalysisService
from app.processing.table_recognition import TableRecognitionService
from app.processing.formula_recognition import FormulaRecognitionService
from app.services.persistence import PersistenceService
from app.services.image_service import ImageService

_mock_uuid = uuid.UUID("7ac4c946-3e08-46cb-bf85-493aee137710")

class TestOrchestrationService(unittest.TestCase):
    
    def test_decode_base64_image(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 测试用例
        test_cases = [
            # 基本Base64编码
            ("SGVsbG8gV29ybGQ=", b"Hello World"),
            # 带有Data URL前缀的Base64
            ("data:image/png;base64,SGVsbG8gV29ybGQ=", b"Hello World"),
            # 带有空格的Base64
            ("SGVs bG8g V29y bGQ=", b"Hello World"),
        ]
        
        for base64_input, expected_output in test_cases:
            result = service._decode_base64_image(base64_input)
            self.assertEqual(result, expected_output, f"解码 {base64_input} 应得 {expected_output}，实际得到 {result}")
    
    def test_process_text_detection(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_text_detection.detect.return_value = [[10, 20, 100, 50], [200, 300, 400, 350]]
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_text_detection("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(result.bboxes, [[10, 20, 100, 50], [200, 300, 400, 350]])
            
            # 验证依赖方法被正确调用
            mock_text_detection.detect.assert_called_once()
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_process_text_recognition(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_text_detection.detect.return_value = [[10, 20, 100, 50]]
        mock_text_recognition.recognize.return_value = [
            {"bbox": [10, 20, 100, 50], "words": "Hello World", "confidence": 0.95}
        ]
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_text_recognition("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.text_items), 1)
            self.assertEqual(result.text_items[0].words, "Hello World")
            self.assertEqual(result.text_items[0].confidence, 0.95)
            
            # 验证依赖方法被正确调用
            mock_text_detection.detect.assert_called_once()
            mock_text_recognition.recognize.assert_called_once()
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_process_layout_analysis(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [10, 20, 100, 50], "type": "text", "confidence": 0.95},
            {"bbox": [200, 300, 400, 350], "type": "table", "confidence": 0.90}
        ]
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_layout_analysis("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.layout_items), 2)
            self.assertEqual(result.layout_items[0].type, "text")
            self.assertEqual(result.layout_items[1].type, "table")
            
            # 验证依赖方法被正确调用
            mock_layout_analysis.analyze.assert_called_once()
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_process_table_recognition(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [10, 20, 100, 50], "type": "table", "confidence": 0.95},
            {"bbox": [200, 300, 400, 350], "type": "table", "confidence": 0.90}
        ]
        mock_table_recognition.recognize_table.return_value = {
            "confidence": 0.95,
            "cells": [
                {
                    "bbox": [10, 20, 50, 30],
                    "row_start": 0,
                    "col_start": 0,
                    "row_span": 1,
                    "col_span": 1,
                    "text": {
                        "value": "Header",
                        "confidence": 0.98
                    },
                    "confidence": 0.95
                }
            ]
        }
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_table_recognition("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.table_contents), 2)  # 应该有两个表格内容
            self.assertEqual(result.table_contents[0].confidence, 0.95)
            self.assertEqual(len(result.table_contents[0].cells), 1)
            self.assertEqual(result.table_contents[0].cells[0].text.value, "Header")
            
            # 验证依赖方法被正确调用
            mock_layout_analysis.analyze.assert_called_once()
            self.assertEqual(mock_table_recognition.recognize_table.call_count, 2)  # 应该调用两次
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_process_table_recognition_no_table(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为 - 没有找到表格
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [10, 20, 100, 50], "type": "text", "confidence": 0.95}
        ]
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_table_recognition("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.table_contents), 0)  # 应该没有表格内容
            
            # 验证没有调用表格识别服务
            mock_table_recognition.recognize_table.assert_not_called()
    
    def test_process_formula_recognition(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [10, 20, 100, 50], "type": "formula", "confidence": 0.95},
            {"bbox": [200, 300, 400, 350], "type": "formula", "confidence": 0.90}
        ]
        mock_formula_recognition.recognize.return_value = {
            "latex_string": "E=mc^2",
            "confidence": 0.98
        }
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_formula_recognition("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.formula_contents), 2)  # 应该有两个公式内容
            self.assertEqual(result.formula_contents[0].latex_string, "E=mc^2")
            self.assertEqual(result.formula_contents[0].confidence, 0.98)
            
            # 验证依赖方法被正确调用
            mock_layout_analysis.analyze.assert_called_once()
            self.assertEqual(mock_formula_recognition.recognize.call_count, 2)  # 应该调用两次
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_process_formula_recognition_no_formula(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为 - 没有找到公式
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [10, 20, 100, 50], "type": "text", "confidence": 0.95}
        ]
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_formula_recognition("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(len(result.formula_contents), 0)  # 应该没有公式内容
            
            # 验证没有调用公式识别服务
            mock_formula_recognition.recognize.assert_not_called()
    
    def test_process_full_result(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 配置mock的行为
        mock_text_detection.detect.return_value = [[10, 20, 100, 50]]
        mock_text_recognition.recognize.return_value = [
            {"bbox": [10, 20, 100, 50], "words": "Hello World", "confidence": 0.95}
        ]
        mock_layout_analysis.analyze.return_value = [
            {"bbox": [5, 15, 110, 60], "type": "text", "confidence": 0.95},
            {"bbox": [200, 300, 400, 350], "type": "table", "confidence": 0.90},
            {"bbox": [500, 600, 700, 650], "type": "formula", "confidence": 0.85}
        ]
        mock_table_recognition.recognize_table.return_value = {
            "confidence": 0.95,
            "cells": [
                {
                    "bbox": [210, 310, 250, 330],
                    "row_start": 0,
                    "col_start": 0,
                    "row_span": 1,
                    "col_span": 1,
                    "text": {
                        "value": "Header",
                        "confidence": 0.98
                    },
                    "confidence": 0.95
                }
            ]
        }
        mock_formula_recognition.recognize.return_value = {
            "latex_string": "E=mc^2",
            "confidence": 0.98
        }
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 使用mock.patch来模拟uuid.uuid4的行为
        with mock.patch('uuid.uuid4') as mock_uuid:
            mock_uuid.return_value = _mock_uuid
            
            # 调用被测试的方法
            result = service.process_full_result("SGVsbG8gV29ybGQ=")
            
            # 验证结果
            self.assertEqual(result.request_id, _mock_uuid)
            self.assertEqual(result.status, "ok")
            
            # 验证文本识别结果
            self.assertEqual(len(result.results["text_recognition"]), 1)
            
            # 验证版面分析结果
            self.assertEqual(len(result.results["layout_analysis"]), 3)
            
            # 验证依赖方法被正确调用
            mock_text_detection.detect.assert_called_once()
            mock_text_recognition.recognize.assert_called_once()
            mock_layout_analysis.analyze.assert_called_once()
            self.assertEqual(mock_table_recognition.recognize_table.call_count, 1)
            self.assertEqual(mock_formula_recognition.recognize.call_count, 1)
            mock_image.save_image.assert_called_once()
            mock_persistence.save_result.assert_called_once()
    
    def test_is_bbox_inside(self):
        # 创建所有依赖的mock
        mock_text_detection = mock.Mock(spec=TextDetectionService)
        mock_text_recognition = mock.Mock(spec=TextRecognitionService)
        mock_layout_analysis = mock.Mock(spec=LayoutAnalysisService)
        mock_table_recognition = mock.Mock(spec=TableRecognitionService)
        mock_formula_recognition = mock.Mock(spec=FormulaRecognitionService)
        mock_persistence = mock.Mock(spec=PersistenceService)
        mock_image = mock.Mock(spec=ImageService)
        
        # 创建服务实例
        service = OrchestrationService(
            mock_text_detection,
            mock_text_recognition,
            mock_layout_analysis,
            mock_table_recognition,
            mock_formula_recognition,
            mock_persistence,
            mock_image
        )
        
        # 测试用例
        test_cases = [
            # 内部边界框完全在外部边界框内
            ([20, 30, 40, 50], [10, 20, 50, 60], True),
            # 内部边界框中心点在外部边界框内，但边界框本身部分超出
            ([5, 30, 25, 50], [10, 20, 50, 60], True),
            # 内部边界框完全在外部边界框外
            ([60, 70, 80, 90], [10, 20, 50, 60], False),
            # 边界情况：内部边界框中心点刚好在外部边界框边界上
            ([10, 30, 30, 50], [10, 20, 50, 60], True),
        ]
        
        for inner_bbox, outer_bbox, expected in test_cases:
            result = service._is_bbox_inside(inner_bbox, outer_bbox)
            self.assertEqual(result, expected, f"内部边界框 {inner_bbox} 在外部边界框 {outer_bbox} 内应为 {expected}，实际为 {result}")


if __name__ == "__main__":
    unittest.main() 