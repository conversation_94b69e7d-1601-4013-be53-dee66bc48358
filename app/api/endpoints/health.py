# Time: 2025-07-04
# Author: AI Assistant
# FileName: health.py

from fastapi import APIRouter
from datetime import datetime
from typing import Dict, Any
import logging
from app.services.initialization_manager import get_initialization_manager

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/health")
async def health_check():
    """基础健康检查端点

    Returns:
        dict: 包含服务状态、名称、时间戳和版本信息
    """
    return {
        "status": "healthy",
        "service": "agileocr-backend",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@router.get("/health/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """详细健康检查端点，包含算法服务初始化状态

    Returns:
        Dict[str, Any]: 详细的健康状态信息，包括各个算法服务的状态
    """
    try:
        manager = get_initialization_manager()
        initialization_status = manager.get_initialization_status()

        # 计算整体状态
        overall_status = "healthy" if manager.is_ready() else "initializing"
        if not initialization_status["is_initialized"]:
            failed_services = [
                name for name, info in initialization_status["services"].items()
                if info["status"] == "failed"
            ]
            if failed_services:
                overall_status = "unhealthy"

        return {
            "status": overall_status,
            "service": "agileocr-backend",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "message": "AgileOCR service detailed status",
            "initialization": initialization_status,
            "ready": manager.is_ready()
        }

    except Exception as e:
        logger.error(f"详细健康检查失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "service": "agileocr-backend",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "message": f"Health check failed: {str(e)}",
            "initialization": None,
            "ready": False
        }

@router.get("/health/ready")
async def readiness_check() -> Dict[str, Any]:
    """就绪检查端点，用于K8s等容器编排系统

    Returns:
        Dict[str, Any]: 就绪状态信息
    """
    try:
        manager = get_initialization_manager()
        is_ready = manager.is_ready()

        return {
            "ready": is_ready,
            "service": "agileocr-backend",
            "timestamp": datetime.now().isoformat(),
            "message": "All services are ready" if is_ready else "Services are still initializing"
        }

    except Exception as e:
        logger.error(f"就绪检查失败: {str(e)}", exc_info=True)
        return {
            "ready": False,
            "service": "agileocr-backend",
            "timestamp": datetime.now().isoformat(),
            "message": f"Readiness check failed: {str(e)}"
        }

@router.get("/ready")
async def readiness_check():
    """就绪检查端点
    
    Returns:
        dict: 服务就绪状态
    """
    return {
        "status": "ready",
        "service": "agileocr-backend",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/live")
async def liveness_check():
    """存活检查端点
    
    Returns:
        dict: 服务存活状态
    """
    return {
        "status": "alive",
        "service": "agileocr-backend",
        "timestamp": datetime.now().isoformat()
    }
