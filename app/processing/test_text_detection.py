import unittest
import os
import logging
import numpy as np
from pathlib import Path

from app.processing.text_detection import TextDetectionService
from app.core.config import assets_dir, settings

# 设置日志级别
logging.basicConfig(level=logging.INFO)

class TestTextDetectionService(unittest.TestCase):
    
    def setUp(self):
        """初始化测试环境"""
        self.service = TextDetectionService()
        self.test_image_path = str(assets_dir / "test01.png")
        self.assertTrue(os.path.exists(self.test_image_path), f"测试图像不存在: {self.test_image_path}")
        
        # 读取测试图像
        with open(self.test_image_path, "rb") as f:
            self.image_bytes = f.read()
    
    def test_detect(self):
        """测试文本检测功能"""
        # 调用检测方法
        boxes = self.service.detect(self.image_bytes)
        
        # 验证结果基本结构
        self.assertIsInstance(boxes, list)
        self.assertTrue(len(boxes) > 0, "应检测到至少一个文本区域")
        
        # 验证边界框格式
        first_box = boxes[0]
        self.assertIsInstance(first_box, list)
        self.assertEqual(len(first_box), 4, "边界框应包含4个坐标值 [x_min, y_min, x_max, y_max]")
        
        # 验证坐标类型
        for coord in first_box:
            self.assertIsInstance(coord, float)
    
    def test_preprocess(self):
        """测试预处理函数"""
        # 解码图像
        image = np.frombuffer(self.image_bytes, np.uint8)
        import cv2
        image = cv2.imdecode(image, cv2.IMREAD_UNCHANGED)
        
        # 调用预处理函数
        preprocessed_image, ratio = self.service._preprocess(image)
        
        # 验证预处理输出
        self.assertIsInstance(preprocessed_image, np.ndarray)
        self.assertEqual(len(preprocessed_image.shape), 4)  # NCHW格式
        self.assertEqual(preprocessed_image.shape[0], 1)    # 批次大小为1
        self.assertEqual(preprocessed_image.shape[1], 3)    # 3通道
        
        # 验证图像尺寸是否符合模型要求（可被32整除）
        self.assertEqual(preprocessed_image.shape[2] % 32, 0, "预处理后的高度应该是32的倍数")
        self.assertEqual(preprocessed_image.shape[3] % 32, 0, "预处理后的宽度应该是32的倍数")
        
        # 验证数据类型和缩放比例
        self.assertEqual(preprocessed_image.dtype, np.float32)
        self.assertIsInstance(ratio, float)
        self.assertTrue(0 < ratio < 1, "缩放比例应该在0到1之间")
    
    def test_postprocess(self):
        """测试后处理函数"""
        # 创建一个简单的模拟概率图
        h, w = 64, 64
        prob_map = np.zeros((h, w), dtype=np.float32)
        prob_map[20:40, 20:40] = 0.8  # 中间区域设置高概率
        
        # 将概率图包装成模型输出格式
        model_outputs = [np.expand_dims(np.expand_dims(prob_map, 0), 0)]
        
        # 调用后处理函数
        boxes = self.service._postprocess(model_outputs, (h, w))
        
        # 验证后处理输出
        self.assertIsInstance(boxes, list)
        
        # 根据测试结果，应该检测到至少一个边界框
        self.assertTrue(len(boxes) > 0, "应检测到至少一个边界框")
        
        if boxes:
            # 验证边界框格式
            first_box = boxes[0]
            self.assertIsInstance(first_box, list)
            self.assertEqual(len(first_box), 4, "每个边界框应包含4个点")
            
            # 验证每个点的格式
            for point in first_box:
                self.assertIsInstance(point, list)
                self.assertEqual(len(point), 2, "每个点应包含x和y坐标")
                self.assertIsInstance(point[0], float)
                self.assertIsInstance(point[1], float)
    
    def test_boxes_from_bitmap(self):
        """测试从位图提取边界框的函数"""
        # 创建一个简单的二值图像和概率图
        h, w = 64, 64
        bitmap = np.zeros((h, w), dtype=np.uint8)
        bitmap[20:40, 20:40] = 1  # 中间区域设置为1
        
        prob_map = np.zeros((h, w), dtype=np.float32)
        prob_map[20:40, 20:40] = 0.8  # 中间区域设置高概率
        
        # 调用函数
        boxes, scores = self.service._boxes_from_bitmap(prob_map, bitmap, w, h)
        
        # 验证输出
        self.assertIsInstance(boxes, list)
        self.assertIsInstance(scores, list)
        self.assertEqual(len(boxes), len(scores))
        
        # 根据测试结果，应该检测到一个边界框
        self.assertEqual(len(boxes), 1, "应检测到一个边界框")
        
        if boxes:
            # 验证边界框格式
            first_box = boxes[0]
            self.assertIsInstance(first_box, list)
            self.assertEqual(len(first_box), 4, "边界框应包含4个点")
            
            # 验证每个点的格式
            for point in first_box:
                self.assertIsInstance(point, list)
                self.assertEqual(len(point), 2, "每个点应包含x和y坐标")
                self.assertIsInstance(point[0], float)
                self.assertIsInstance(point[1], float)
            
            # 验证分数
            self.assertIsInstance(scores[0], float)
            self.assertTrue(0 <= scores[0] <= 1, "分数应在0到1之间")

if __name__ == "__main__":
    unittest.main() 