#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
OCR算法服务初始化管理器

该模块负责管理所有OCR相关算法服务的初始化过程，包括：
- 文本检测服务
- 文本识别服务
- 版面分析服务
- 表格识别服务
- 公式识别服务
- 图像处理服务
- 持久化服务

特点：
1. 异步初始化：支持并发初始化多个服务，提高启动速度
2. 超时控制：防止初始化过程无限等待
3. 中断支持：支持Ctrl+C中断初始化过程
4. 状态管理：实时跟踪每个服务的初始化状态
5. 错误处理：详细的错误信息和恢复机制
6. 单例模式：确保全局只有一个初始化管理器实例
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum

from app.core.config import settings
from app.services.persistence import PersistenceService
from app.services.image_service import ImageService
from app.processing.text_detection import TextDetectionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.layout_analysis import LayoutAnalysisService
from app.processing.cycle_centernet.table_rec_cycle_centernet import CycleCenterNetTable
from app.processing.formula_recognition import FormulaRecognitionService
from app.utils.interruptible_oss_downloader import download_with_interrupt_support
from app.utils.file_folder_path import agileocr_cache_dir

DOMAIN_URL = "aidatadev/appdata/shared_models/agileocr/v202507042122/"

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """服务状态枚举"""
    NOT_STARTED = "not_started"
    INITIALIZING = "initializing"
    READY = "ready"
    FAILED = "failed"

@dataclass
class ServiceInfo:
    """服务信息"""
    name: str
    status: ServiceStatus
    instance: Optional[Any] = None
    error: Optional[str] = None
    init_time: Optional[float] = None

class InitializationManager:
    """算法服务初始化管理器

    负责异步并发初始化所有OCR算法服务，确保每个服务只被实例化一次
    """
    
    def __init__(self):
        self.services: Dict[str, ServiceInfo] = {
            "model_resources": ServiceInfo("model_resources", ServiceStatus.NOT_STARTED),
            "persistence_service": ServiceInfo("persistence_service", ServiceStatus.NOT_STARTED),
            "image_service": ServiceInfo("image_service", ServiceStatus.NOT_STARTED),
            "text_detection_service": ServiceInfo("text_detection_service", ServiceStatus.NOT_STARTED),
            "text_recognition_service": ServiceInfo("text_recognition_service", ServiceStatus.NOT_STARTED),
            "layout_analysis_service": ServiceInfo("layout_analysis_service", ServiceStatus.NOT_STARTED),
            "formula_recognition_service": ServiceInfo("formula_recognition_service", ServiceStatus.NOT_STARTED),
            "table_recognition_service": ServiceInfo("table_recognition_service", ServiceStatus.NOT_STARTED),
        }
        self._initialization_lock = asyncio.Lock()
        self._is_initialized = False
        # 共享线程池，避免重复创建
        self._thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="OCR-Init")
        # 中断标志
        self._interrupted = False

    async def initialize_all_services(self, timeout: float = 300.0) -> bool:
        """异步初始化所有服务

        Args:
            timeout: 初始化超时时间（秒），默认5分钟

        Returns:
            bool: 是否所有服务都初始化成功
        """
        async with self._initialization_lock:
            if self._is_initialized:
                logger.info("服务已经初始化完成，跳过重复初始化")
                return True

            logger.info("开始异步初始化所有OCR算法服务...")
            logger.info(f"初始化超时时间: {timeout}秒，可使用Ctrl+C中断")
            start_time = time.time()

            try:
                # 使用asyncio.wait_for添加超时和中断支持
                return await asyncio.wait_for(
                    self._do_initialization(),
                    timeout=timeout
                )

            except asyncio.TimeoutError:
                logger.error(f"初始化超时（超过{timeout}秒），正在清理...")
                self._interrupted = True
                await self._cleanup_failed_initialization()
                return False

            except KeyboardInterrupt:
                logger.warning("收到中断信号，正在停止初始化...")
                self._interrupted = True
                await self._cleanup_failed_initialization()
                raise

            except Exception as e:
                logger.error(f"服务初始化过程中发生异常: {str(e)}", exc_info=True)
                self._interrupted = True
                await self._cleanup_failed_initialization()
                return False

    async def _do_initialization(self) -> bool:
        """执行实际的初始化过程"""
        start_time = time.time()

        # 第一阶段：初始化模型资源（必须先完成）
        if self._interrupted:
            raise KeyboardInterrupt("初始化被中断")
        await self._init_model_resources()

        # 第二阶段：并发初始化基础服务（无依赖关系）
        if self._interrupted:
            raise KeyboardInterrupt("初始化被中断")

        basic_services = [
            self._init_persistence_service(),
            self._init_image_service(),
            self._init_text_detection_service(),
            self._init_text_recognition_service(),
            self._init_layout_analysis_service(),
            self._init_formula_recognition_service(),
        ]

        # 不使用return_exceptions=True，让KeyboardInterrupt正常传播
        await asyncio.gather(*basic_services)

        # 第三阶段：初始化有依赖关系的服务
        if self._interrupted:
            raise KeyboardInterrupt("初始化被中断")
        await self._init_table_recognition_service()

        # 检查初始化结果
        success = self._check_initialization_results()

        total_time = time.time() - start_time
        if success:
            logger.info(f"所有OCR算法服务初始化完成，总耗时: {total_time:.2f}秒")
            self._is_initialized = True
        else:
            logger.error(f"部分服务初始化失败，总耗时: {total_time:.2f}秒")

        return success

    async def _cleanup_failed_initialization(self):
        """清理失败的初始化"""
        logger.info("正在清理未完成的初始化...")

        # 标记所有正在初始化的服务为失败
        for service_info in self.services.values():
            if service_info.status == ServiceStatus.INITIALIZING:
                service_info.status = ServiceStatus.FAILED
                service_info.error = "初始化被中断或超时"

        # 强制关闭线程池
        if hasattr(self, '_thread_pool') and self._thread_pool:
            logger.info("强制关闭线程池...")
            self._thread_pool.shutdown(wait=False)  # 不等待，立即关闭

        logger.info("初始化清理完成")
    
    async def _init_model_resources(self):
        """初始化模型资源"""
        service_info = self.services["model_resources"]
        service_info.status = ServiceStatus.INITIALIZING

        try:
            # 检查是否被中断
            if self._interrupted:
                raise KeyboardInterrupt("模型资源初始化被中断")

            start_time = time.time()
            logger.info("开始初始化模型资源...")

            # 在共享线程池中执行同步的模型资源初始化，添加超时
            loop = asyncio.get_event_loop()
            await asyncio.wait_for(
                loop.run_in_executor(self._thread_pool, self._sync_init_model_resources),
                timeout=180.0  # 模型下载最多3分钟
            )

            service_info.init_time = time.time() - start_time
            service_info.status = ServiceStatus.READY
            logger.info(f"模型资源初始化完成，耗时: {service_info.init_time:.2f}秒")

        except asyncio.TimeoutError:
            service_info.status = ServiceStatus.FAILED
            service_info.error = "模型资源初始化超时（超过180秒）"
            logger.error("模型资源初始化超时")
            raise

        except KeyboardInterrupt:
            service_info.status = ServiceStatus.FAILED
            service_info.error = "模型资源初始化被用户中断"
            logger.warning("模型资源初始化被中断")
            raise

        except Exception as e:
            service_info.status = ServiceStatus.FAILED
            service_info.error = str(e)
            logger.error(f"模型资源初始化失败: {str(e)}")
            raise

    def _sync_init_model_resources(self):
        """同步初始化模型资源"""
        import os
        import tarfile
        import shutil

        dst_dir = os.path.join(agileocr_cache_dir, "agileocr_models")
        if os.path.exists(dst_dir):
            logger.info(f"模型目录 {dst_dir} 已存在，跳过下载和解压步骤")
            return

        model_tar_path = None
        try:
            # 检查中断标志
            if self._interrupted:
                raise KeyboardInterrupt("模型资源下载被中断")

            # 确保缓存目录存在
            os.makedirs(agileocr_cache_dir, exist_ok=True)

            # 下载模型文件
            model_tar_path = os.path.join(agileocr_cache_dir, "agileocr_models.tar.gz")
            logger.info(f"开始从OSS下载模型文件到 {model_tar_path}")

            # 使用可中断的下载器
            download_with_interrupt_support(
                os.path.join(DOMAIN_URL, "agileocr_models.tar.gz"),
                model_tar_path,
                timeout=180.0,  # 3分钟超时
                interrupt_check=lambda: self._interrupted
            )

            # 下载完成后再次检查中断标志
            if self._interrupted:
                raise KeyboardInterrupt("模型资源解压被中断")

            # 解压模型文件
            logger.info(f"开始解压模型文件到 {dst_dir}")
            with tarfile.open(model_tar_path, "r:gz") as tar:
                # 分批解压，定期检查中断标志
                members = tar.getmembers()
                total_members = len(members)

                for i, member in enumerate(members):
                    if self._interrupted:
                        raise KeyboardInterrupt("模型文件解压被中断")

                    tar.extract(member, path=agileocr_cache_dir)

                    # 每解压10个文件检查一次中断标志
                    if i % 10 == 0:
                        logger.info(f"解压进度: {i+1}/{total_members}")

            # 删除下载的压缩包
            if os.path.exists(model_tar_path):
                os.remove(model_tar_path)
            logger.info("模型资源初始化完成")

        except KeyboardInterrupt:
            logger.warning("模型资源初始化被中断，正在清理...")
            # 清理部分下载/解压的文件
            if model_tar_path and os.path.exists(model_tar_path):
                os.remove(model_tar_path)
            if os.path.exists(dst_dir):
                shutil.rmtree(dst_dir)
            raise

        except Exception as e:
            logger.error(f"模型资源初始化失败: {str(e)}")
            # 清理可能存在的部分文件
            if model_tar_path and os.path.exists(model_tar_path):
                os.remove(model_tar_path)
            if os.path.exists(dst_dir):
                shutil.rmtree(dst_dir)
            raise
    
    async def _init_persistence_service(self):
        """初始化持久化服务"""
        await self._init_service_async("persistence_service", 
                                     lambda: PersistenceService(results_dir=settings.RESULTS_DIR))
    
    async def _init_image_service(self):
        """初始化图片服务"""
        await self._init_service_async("image_service", 
                                     lambda: ImageService(images_dir=settings.IMAGES_DIR))
    
    async def _init_text_detection_service(self):
        """初始化文本检测服务"""
        await self._init_service_async("text_detection_service", 
                                     lambda: TextDetectionService())
    
    async def _init_text_recognition_service(self):
        """初始化文本识别服务"""
        await self._init_service_async("text_recognition_service", 
                                     lambda: TextRecognitionService())
    
    async def _init_layout_analysis_service(self):
        """初始化版面分析服务"""
        await self._init_service_async("layout_analysis_service", 
                                     lambda: LayoutAnalysisService())
    
    async def _init_formula_recognition_service(self):
        """初始化公式识别服务"""
        await self._init_service_async("formula_recognition_service", 
                                     lambda: FormulaRecognitionService())
    
    async def _init_table_recognition_service(self):
        """初始化表格识别服务（依赖其他服务）"""
        service_info = self.services["table_recognition_service"]
        service_info.status = ServiceStatus.INITIALIZING
        
        try:
            start_time = time.time()
            
            # 获取依赖的服务
            text_recognition = self.get_service("text_recognition_service")
            text_detection = self.get_service("text_detection_service")
            
            if not text_recognition or not text_detection:
                raise RuntimeError("表格识别服务依赖的文本检测和识别服务未初始化")
            
            # 在共享线程池中初始化表格识别服务
            loop = asyncio.get_event_loop()
            config = {
                "UNITABLE_ENCODER_WEIGHTS": settings.UNITABLE_ENCODER_WEIGHTS,
                "UNITABLE_DECODER_WEIGHTS": settings.UNITABLE_DECODER_WEIGHTS,
                "UNITABLE_VOCAB_PATH": settings.UNITABLE_VOCAB_PATH,
                "USE_CUDA": settings.USE_CUDA
            }
            service_info.instance = await loop.run_in_executor(
                self._thread_pool,
                lambda: CycleCenterNetTable(config, text_recognition, text_detection)
            )
            
            service_info.init_time = time.time() - start_time
            service_info.status = ServiceStatus.READY
            logger.info(f"表格识别服务初始化完成，耗时: {service_info.init_time:.2f}秒")
            
        except Exception as e:
            service_info.status = ServiceStatus.FAILED
            service_info.error = str(e)
            logger.error(f"表格识别服务初始化失败: {str(e)}")
    
    async def _init_service_async(self, service_name: str, factory_func):
        """异步初始化单个服务"""
        service_info = self.services[service_name]
        service_info.status = ServiceStatus.INITIALIZING

        try:
            # 检查是否被中断
            if self._interrupted:
                raise KeyboardInterrupt(f"{service_name}初始化被中断")

            start_time = time.time()
            logger.info(f"开始初始化{service_name}...")

            # 在共享线程池中执行同步的服务初始化，添加超时
            loop = asyncio.get_event_loop()
            service_info.instance = await asyncio.wait_for(
                loop.run_in_executor(self._thread_pool, factory_func),
                timeout=120.0  # 单个服务最多2分钟
            )

            service_info.init_time = time.time() - start_time
            service_info.status = ServiceStatus.READY
            logger.info(f"{service_name}初始化完成，耗时: {service_info.init_time:.2f}秒")

        except asyncio.TimeoutError:
            service_info.status = ServiceStatus.FAILED
            service_info.error = f"{service_name}初始化超时（超过120秒）"
            logger.error(f"{service_name}初始化超时")
            raise

        except KeyboardInterrupt:
            service_info.status = ServiceStatus.FAILED
            service_info.error = f"{service_name}初始化被用户中断"
            logger.warning(f"{service_name}初始化被中断")
            raise

        except Exception as e:
            service_info.status = ServiceStatus.FAILED
            service_info.error = str(e)
            logger.error(f"{service_name}初始化失败: {str(e)}")
            raise  # 重新抛出异常，让调用者处理
    
    def _check_initialization_results(self) -> bool:
        """检查初始化结果"""
        failed_services = []
        for name, info in self.services.items():
            if info.status == ServiceStatus.FAILED:
                failed_services.append(f"{name}: {info.error}")
        
        if failed_services:
            logger.error(f"以下服务初始化失败: {failed_services}")
            return False
        
        return True
    
    def get_service(self, service_name: str) -> Optional[Any]:
        """获取已初始化的服务实例"""
        service_info = self.services.get(service_name)
        if service_info and service_info.status == ServiceStatus.READY:
            return service_info.instance
        return None
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """获取初始化状态"""
        status = {
            "is_initialized": self._is_initialized,
            "services": {}
        }
        
        for name, info in self.services.items():
            status["services"][name] = {
                "status": info.status.value,
                "init_time": info.init_time,
                "error": info.error
            }
        
        return status
    
    def is_ready(self) -> bool:
        """检查所有服务是否就绪"""
        return self._is_initialized and all(
            info.status == ServiceStatus.READY
            for info in self.services.values()
        )



    def set_interrupted(self):
        """手动设置中断标志（用于外部中断）"""
        logger.warning("收到外部中断信号")
        self._interrupted = True

    def cleanup(self):
        """清理资源"""
        if hasattr(self, '_thread_pool') and self._thread_pool:
            logger.info("正在关闭初始化线程池...")
            self._thread_pool.shutdown(wait=True)
            logger.info("初始化线程池已关闭")

# 全局初始化管理器实例
_initialization_manager = None

def get_initialization_manager() -> InitializationManager:
    """获取全局初始化管理器实例"""
    global _initialization_manager
    if _initialization_manager is None:
        _initialization_manager = InitializationManager()
    return _initialization_manager
