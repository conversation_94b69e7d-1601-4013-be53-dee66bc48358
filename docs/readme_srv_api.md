# OCR服务API文档

## 概述

本文档描述了OCR服务的RESTful API接口。该服务提供文本检测、文本识别、版面分析、表格识别、公式识别等功能，支持图像的光学字符识别处理。

**基础信息：**
- 基础URL: `http://localhost:6000`
- API版本: v1
- API前缀: `/api/v1`
- 内容类型: `application/json`
- 字符编码: UTF-8

## API端点列表

### 公共OCR处理接口（无持久化，高性能）
- `POST /api/v1/ocr/text_detection` - 文本检测
- `POST /api/v1/ocr/text_recognition` - 文本识别
- `POST /api/v1/ocr/layout_analysis` - 版面分析
- `POST /api/v1/ocr/table_recognition` - 表格识别
- `POST /api/v1/ocr/formula_recognition` - 公式识别
- `POST /api/v1/ocr/full_result` - 全量OCR处理

---

## API类型说明

### 公共API

#### 公共API（`/api/v1/ocr/*`）
- **特点**: 无持久化操作，高性能，直接返回结果
- **适用场景**: 外部系统调用，批量处理，不需要后续查询结果的场景
- **性能优势**: 不进行文件存储操作，响应更快，资源消耗更少
- **限制**: 无法通过request_id查询历史结果，无法获取原始图片和可视化结果

### IP限制配置

在`app/core/config.py`中可以配置以下参数：

```python
# 前端专用API的IP限制配置
WEB_API_ALLOWED_IPS: List[str] = ["*************", "127.0.0.1", "localhost", "::1"]  # 允许访问的IP列表，珠海办公网，本机
WEB_API_IP_RESTRICTION_ENABLED: bool = True 
```

---

## 详细API说明

### 1. 文本检测

**URL路径:** `POST /api/v1/ocr/text_detection`

**用途说明:** 对上传的图像进行文本区域检测，返回文本区域的边界框坐标和置信度。

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_detection": [
      {
        "bbox": [10, 8, 810, 8, 810, 52, 10, 52],
        "confidence": 0.99
      }
    ]
  }
}
```

---

### 2. 文本识别

**URL路径:** `POST /api/v1/ocr/text_recognition`

**用途说明:** 对上传的图像进行文本检测和识别，返回检测到的文本内容、位置和置信度。

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_recognition": [
      {
        "bbox": [10, 8, 810, 8, 810, 52, 10, 52],
        "confidence": 0.98,
        "text": {
            "value": "Project Phoenix Q2 Financial Report",
            "confidence": 0.88
        }
      }
    ]
  }
}
```

---

### 3. 版面分析

**URL路径:** `POST /api/v1/ocr/layout_analysis`

**用途说明:** 对上传的图像进行版面结构分析，识别文档中的标题、段落、表格、公式、图像等区域类型。
当前版面分析输出的类型有 `title`, `text`, `table`, `formula`, `figure`, `unknown`

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "layout_analysis": [
      {
        "bbox": [10, 8, 810, 8, 810, 52, 10, 52],
        "type": "title",
        "confidence": 0.95
      },
      {
        "bbox": [10, 100, 810, 100, 810, 350, 10, 350],
        "type": "table",
        "confidence": 0.92
      }
    ]
  }
}
```

---

### 4. 表格识别

**URL路径:** `POST /api/v1/ocr/table_recognition`

**用途说明:** 对上传的图像进行表格结构识别和内容提取，返回表格的单元格信息、位置、跨行跨列信息等。

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8", "status": "ok", "error_message": null,
  "results": {
    "table_recognition": [
      {
        "bbox": [10, 100, 810, 100, 810, 350, 10, 350],
        "confidence": 0.92,
        "cells": [
          {
            "bbox": [12, 122, 200, 122, 200, 145, 12, 145],
            "confidence": 0.99,
            "row_start": 0, "col_start": 0, "row_span": 1, "col_span": 1,
            "text": {
                "value": "Metric",
                "confidence": 0.95
            }
          },
          {
            "bbox": [202, 122, 400, 122, 400, 145, 202, 145],
            "confidence": 0.99,
            "row_start": 0, "col_start": 1, "row_span": 1, "col_span": 1,
            "text": {
                "value": "Value",
                "confidence": 0.95
            }
          }
        ]
      }
    ]
  }
}
```

---

### 5. 公式识别

**URL路径:** `POST /api/v1/ocr/formula_recognition`

**用途说明:** 对上传的图像进行数学公式识别，返回公式的LaTeX表示和位置信息。

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "formula_recognition": [
      {
        "bbox": [10, 360, 810, 360, 810, 480, 10, 480],
        "confidence": 0.93,
        "text": {
          "value": "E = mc^2",
          "confidence": 0.90
        }
      }
    ]
  }
}
```

---

### 6. 全量OCR处理

**URL路径:** `POST /api/v1/ocr/full_result`

**用途说明:** 对上传的图像进行完整的OCR处理，包括文本识别、版面分析、表格识别、公式识别等所有功能的组合。

**输入参数:**
```json
{
  "image_base64": "string",  // 必需，Base64编码的图像数据
  "filename": "string"       // 必需，图像文件名
}
```

**输出示例:**
```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_recognition": [
      {
        "bbox": [10, 8, 810, 8, 810, 52, 10, 52],
        "confidence": 0.98,
        "text": {
            "value": "Project Phoenix Q2 Financial Report",
            "confidence": 0.88
        }
      }
    ],
    "layout_analysis": [
      {
        "bbox": [10, 8, 810, 8, 810, 52, 10, 52],
        "type": "title",
        "confidence": 0.95,
        "text": {
          "value": "im title1",
          "confidence": 0.98
        }
      },
      {
        "bbox": [10, 60, 400, 60, 400, 85, 10, 85],
        "type": "text",
        "confidence": 0.95,
        "text": {
          "value": "im paragraph1",
          "confidence": 0.98
        }
      },
      {
        "bbox": [10, 100, 810, 100, 810, 350, 10, 350],
        "type": "table",
        "confidence": 0.92,
        "cells": [
            {
                "bbox": [12, 122, 200, 122, 200, 145, 12, 145],
                "confidence": 0.99,
                "row_start": 0, "col_start": 0, "row_span": 1, "col_span": 1,
                "text": {
                    "value": "Metric",
                    "confidence": 0.95
                }
            },
            {
              "bbox": [202, 122, 400, 122, 400, 145, 202, 145],
              "confidence": 0.93,
              "row_start": 0, "col_start": 1, "row_span": 1, "col_span": 1,
              "text": {
                  "value": "Value",
                  "confidence": 0.96
              }
            }
        ]
      },
      {
        "bbox": [10, 360, 810, 360, 810, 480, 10, 480],
        "type": "formula",
        "confidence": 0.88,
        "text": {
          "value": "E = mc^2",
          "confidence": 0.93
        }
      },
      {
        "bbox": [850, 8, 1000, 8, 1000, 158, 850, 158],
        "type": "image",
        "confidence": 0.99
      }
    ]
  }
}
```

---

## 错误处理

### 错误响应格式
```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "error_code",
  "error_message": "错误描述信息",
  "results": null
}
```

### 错误代码说明
- `ok` - 请求成功
- `cli/img` - 图像数据错误或缺失
- `cli/not_found` - 请求的资源不存在
- `cli/bad_params` - 请求参数错误
- `cli/forbidden` - 访问被拒绝
- `srv/internal` - 服务器内部错误

### HTTP状态码
- `200` - 请求成功
- `400` - 客户端请求错误
- `403` - 访问被拒绝
- `404` - 资源不存在
- `500` - 服务器内部错误

---

## 数据格式说明

### 坐标格式
所有边界框(bbox)使用8个数值表示四个顶点坐标：`[x1, y1, x2, y2, x3, y3, x4, y4]`
- (x1, y1): 左上角
- (x2, y2): 右上角
- (x3, y3): 右下角
- (x4, y4): 左下角

### 置信度
所有置信度值为0-1之间的浮点数，值越高表示识别结果越可靠。

### 表格单元格
- `row_start`, `col_start`: 单元格起始行列索引（从0开始）
- `row_span`, `col_span`: 单元格跨行跨列数量

### 文本内容
文本内容使用嵌套结构，包含：
- `value`: 识别的文本内容
- `confidence`: 文本识别的置信度

---

## 使用示例

### 1. 基础文本识别
```bash
curl -X POST "http://localhost:6000/api/v1/ocr/text_recognition" \
  -H "Content-Type: application/json" \
  -d '{
    "image_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    "filename": "test.jpg"
  }'
```

