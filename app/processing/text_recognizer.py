#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/06/23 16:05
# <AUTHOR> Cascade
# @FileName: recognizer.py

import os
import cv2
import time
import math
import yaml
import torch
import logging
import numpy as np
import onnxruntime as ort
from typing import Dict, List, Optional, Tuple, Union
from app.core.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[35m{time.asctime() + "  " + __name__}\033[0m')

# =================================================================================
# 1. Migrated utility classes and functions from modules/utils/ctc_utils.py
# =================================================================================

class _CTCDecodeInfo:
    """Internal CTC decode info class for delayed computation."""
    def __init__(self, char_positions, preds_idx, preds_prob=None, ignored_tokens=None, character_map=None):
        self.char_positions = char_positions
        self.preds_idx = preds_idx.tolist() if isinstance(preds_idx, np.ndarray) else preds_idx
        self._raw_preds_idx = preds_idx
        self._preds_prob = preds_prob
        self._ignored_tokens = ignored_tokens
        self._character_map = character_map
        self._char_blocks = None

    def get_char_blocks(self):
        if self._char_blocks is None:
            self._char_blocks = _analyze_ctc_sequence(
                self._raw_preds_idx, self._preds_prob, self._ignored_tokens, self._character_map
            )
        return self._char_blocks or []

def _analyze_ctc_sequence(preds_idx, preds_prob=None, ignored_tokens=None, character_map=None):
    if ignored_tokens is None:
        ignored_tokens = [0]
    seq_len = len(preds_idx)
    char_blocks = []
    i = 0
    while i < seq_len:
        if preds_idx[i] in ignored_tokens:
            i += 1
            continue
        current_char_idx = preds_idx[i]
        char = ''
        if character_map and 0 <= current_char_idx < len(character_map):
            char = character_map[current_char_idx]
        start_pos = i
        i += 1
        while i < seq_len and preds_idx[i] == current_char_idx:
            i += 1
        end_pos = i - 1
        confidence = float(np.mean(preds_prob[start_pos:end_pos + 1])) if preds_prob is not None else 0.0
        char_blocks.append({
            "char": char, "char_idx": current_char_idx, "start_pos": start_pos,
            "end_pos": end_pos, "width": end_pos - start_pos + 1, "confidence": confidence
        })
    return char_blocks

# =================================================================================
# 2. Migrated utility functions
# =================================================================================

def rotate_image(img: np.ndarray, angle: str) -> np.ndarray:
    """Rotates an image based on the angle string ('90', '180', '270')."""
    if angle == '180':
        img = cv2.rotate(img, cv2.ROTATE_180)
    elif angle == '90':
        img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    elif angle == '270':
        img = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
    return img

# =================================================================================
# 3. Migrated Pre-process and Post-process logic
# =================================================================================

class _RecPreProcess:
    """Internal pre-processing class for CTC-based recognition model."""
    def __init__(self, image_shape: List[int] = [3, 32, 320], mean: List[float] = [0.5, 0.5, 0.5], std: List[float] = [0.5, 0.5, 0.5], **kwargs):
        self.rec_image_shape = image_shape
        self.mean = np.array(mean, dtype=np.float32)
        self.std = np.array(std, dtype=np.float32)

    def _resize_norm_img(self, img: np.ndarray, max_wh_ratio: float) -> np.ndarray:
        imgC, imgH, imgW = self.rec_image_shape
        assert imgC == img.shape[2]
        imgW = int((imgH * max_wh_ratio))
        h, w = img.shape[:2]
        ratio = w / float(h)
        resized_w = min(int(math.ceil(imgH * ratio)), imgW)
        resized_image = cv2.resize(img, (resized_w, imgH))
        resized_image = resized_image.astype("float32")
        resized_image = resized_image.transpose((2, 0, 1))
        resized_image = (resized_image / 255.0 - self.mean[:, None, None]) / self.std[:, None, None]
        padding_im = np.zeros((imgC, imgH, imgW), dtype=np.float32)
        padding_im[:, :, 0:resized_w] = resized_image
        return padding_im

    def __call__(self, img_list: List[np.ndarray]) -> np.ndarray:
        img_list = sorted(img_list, key=lambda img: img.shape[1] / img.shape[0])
        max_wh_ratio = float(img_list[-1].shape[1]) / float(img_list[-1].shape[0])
        norm_img_list = [self._resize_norm_img(img, max_wh_ratio) for img in img_list]
        return np.array(norm_img_list)

class _CTCLabelDecode:
    """Internal CTC label decoder."""
    def __init__(self, character_dict: str, use_space_char: bool = False, **kwargs):
        raw_character = self._parse_character_dict(character_dict)
        self.character = self._add_special_char(list(raw_character))
        if use_space_char and ' ' not in self.character:
            self.character.append(' ')

    def _add_special_char(self, dict_character: List[str]) -> List[str]:
        return ["blank"] + dict_character

    def _parse_character_dict(self, character_dict: str) -> str:
        if os.path.exists(character_dict):
            with open(character_dict, 'r', encoding='utf-8') as f:
                return ''.join(line.strip('\n').strip('\r\n') for line in f.readlines())
        return character_dict

    def get_ignored_tokens(self):
        """获取需要忽略的标记"""
        return [0]  # CTC中的blank标记

    def decode(self, preds_idx: np.ndarray, preds_prob: np.ndarray = None, is_remove_duplicate: bool = True) -> Tuple[str, float, List[float], _CTCDecodeInfo]:
        # This method is a faithful reimplementation of the original logic in modules/utils/rec_post_proc.py
        ignored_tokens = self.get_ignored_tokens()
        selection = np.ones(len(preds_idx), dtype=bool)

        if is_remove_duplicate:
            selection[1:] = preds_idx[1:] != preds_idx[:-1]
        
        for ignored_token in ignored_tokens:
            selection &= preds_idx != ignored_token
        
        filtered_indices = preds_idx[selection]
        char_positions = np.where(selection)[0].tolist()

        if not filtered_indices.size:
            decode_info = _CTCDecodeInfo(
                char_positions=[],
                preds_idx=preds_idx,
                preds_prob=preds_prob,
                ignored_tokens=ignored_tokens,
                character_map=self.character
            )
            return "", 0.0, [], decode_info

        char_list = []
        char_probs = []
        filtered_probs = preds_prob[selection] if preds_prob is not None else None

        for i, char_idx in enumerate(filtered_indices):
            if 0 <= char_idx < len(self.character):
                char_list.append(self.character[char_idx])
                if filtered_probs is not None:
                    char_probs.append(filtered_probs[i])
        
        text = "".join(char_list)
        
        # Safely calculate confidence using a standard Python list
        confidence = sum(char_probs) / len(char_probs) if char_probs else 0.0

        decode_info = _CTCDecodeInfo(
            char_positions=char_positions,
            preds_idx=preds_idx,
            preds_prob=preds_prob,
            ignored_tokens=ignored_tokens,
            character_map=self.character
        )
        
        return text, confidence, char_probs, decode_info

    def __call__(self, preds: np.ndarray) -> List[Dict]:
        # This method is a faithful reimplementation of the original logic in modules/utils/rec_post_proc.py
        if preds.ndim != 3:
            raise ValueError(f"Expected 3D input, but got shape: {preds.shape}")
        
        preds_prob = np.max(preds, axis=2)
        preds_idx = np.argmax(preds, axis=2)
        batch_size = preds.shape[0]
        
        results = []
        for idx in range(batch_size):
            text, confidence, char_probs, decode_info = self.decode(
                preds_idx[idx], preds_prob[idx], is_remove_duplicate=True)
            
            # Explicitly cast to float, as done in the original code, to prevent type errors.
            results.append({
                "text": text,
                "confidence": float(confidence),
                "char_probs": char_probs,
                "decode_info": decode_info
            })
        return results

# =================================================================================
# 4. TextRecognizer implementation
# =================================================================================

class TextRecognizer:
    """
    Text recognizer, supporting ONNX model inference.
    This is a self-contained implementation by migrating code from:
    - modules/predictors/base.py
    - modules/predictors/recognizer.py
    """
    def __init__(self, config: dict):
        """
        Initializes the TextRecognizer.

        Args:
            config (dict): Configuration dictionary for the recognizer.
        """
        self.config = config
        # self.model_path = self.config.get("model_path")
        self.model_path = settings.TEXT_REC_WEIGHTS
        self.model_type = self._determine_model_type(self.model_path)
        self.device_type = self.config.get("device_type", "cpu") if torch.cuda.is_available() else "cpu"
        self.device_id = self.config.get("device_id", 0)
        self.infer_batch_num = self.config.get("batch_size", 8)
        self.init_model()
        self._log_model_loaded()

    def _determine_model_type(self, model_path: str) -> str:
        if not model_path or not model_path.endswith(".onnx"):
            raise ValueError("Only ONNX models are supported in this module.")
        return "onnx"

    def _get_ort_providers(self) -> List[str]:
        providers = ['CPUExecutionProvider']
        if self.device_type in ['gpu', 'cuda']:
            providers.insert(0, ('CUDAExecutionProvider', {'device_id': self.device_id}))
        return providers

    def _create_onnx_session(self, model_path: str) -> ort.InferenceSession:
        sess_options = ort.SessionOptions()
        sess_options.intra_op_num_threads = os.cpu_count() or 1
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        return ort.InferenceSession(model_path, sess_options=sess_options, providers=self._get_ort_providers())

    def _log_model_loaded(self) -> None:
        device_info = f"Device: {self.device_type.upper()}"
        if self.device_type in ['gpu', 'cuda']: device_info += f" (ID: {self.device_id})"
        logger.info(f"  [TextRecognizer] Model loaded. {device_info}, Model Type: ONNX")
    
    def _correct_confidence(self, rec_results: List[Dict]) -> List[Dict]:
        """
        Correct the confidence of recognition results
        Args:
            rec_results: Recognition result list, each item contains text, confidence and rotation flag
        Returns:
            Corrected recognition result list
        """
        corrected_results = []
        for result in rec_results:
            if not result or 'text' not in result:
                # If format is wrong or text is empty, add original result directly
                corrected_results.append(result)
                continue
            
            text = result.get('text', '')
            confidence = result.get('confidence', 0.0)
            char_probs = result.get('char_probs', [])
            
            # If character confidence is not empty and length does not match text, do not correct
            if char_probs and len(char_probs) != len(text):
                corrected_results.append(result)
                continue
            
            # For text with 3 or more characters, perform confidence correction
            if len(text) >= 3 and char_probs:
                # Calculate average
                mean_conf = sum(char_probs) / len(char_probs)
                # Filter out confidence higher than (average - 0.3)
                filtered_confs = [conf for conf in char_probs if conf >= (mean_conf - 0.3)]
                if filtered_confs:  # If there are remaining confidences
                    new_avg_conf = sum(filtered_confs) / len(filtered_confs)
                    result['confidence'] = new_avg_conf
                    corrected_results.append(result)
                    continue
            elif char_probs:  # For short text, directly calculate average confidence
                confidence = sum(char_probs) / len(char_probs)
                result['confidence'] = confidence
            corrected_results.append(result)
        
        return corrected_results

    def _prepare_input_tensor(self, input_data):
        """根据模型精度准备输入张量"""
        if hasattr(self, 'model_precision') and self.model_precision == "fp16":
            return input_data.astype(np.float16)
        return input_data.astype(np.float32)

    def _ensure_fp32_output(self, output_data):
        """确保输出数据为FP32类型，无论模型是什么精度"""
        if output_data is None:
            return None
        
        # 如果是numpy数组并且不是float32类型，则转换为float32
        if isinstance(output_data, np.ndarray) and output_data.dtype != np.float32:
            return output_data.astype(np.float32)
        return output_data

    def init_model(self):
        if not self.model_path or not os.path.exists(self.model_path):
            raise ValueError("Model path is not specified or does not exist.")
        
        self.session = self._create_onnx_session(self.model_path)
        self.input_name = self.session.get_inputs()[0].name
        self.output_names = [output.name for output in self.session.get_outputs()]
        
        self.preprocess = _RecPreProcess(**self.config.get("pre_process", {}))
        
        # Robustly initialize post-processor to prevent argument errors
        post_process_config = self.config.get("post_process", {}).copy()
        post_process_config['character_dict'] = settings.TEXT_REC_VOCAB
        self.post_process = _CTCLabelDecode(**post_process_config)

    def batch_inference(self, img_list: List[np.ndarray]) -> List[Dict]:
        """
        Batch inference for image list with order preservation.
        Faithful reproduction of the original batch_inference method from modules/predictors/recognizer.py
        
        Args:
            img_list: List of input images
        Returns:
            List of recognition results
        """
        if not img_list:
            return []

        try:
            # Step 1: Sort images by aspect ratio for better batching efficiency
            aspect_ratios = []
            for idx, img in enumerate(img_list):
                h, w = img.shape[:2]
                aspect_ratio = w / h
                aspect_ratios.append((aspect_ratio, idx))

            # Sort by aspect ratio
            aspect_ratios.sort(key=lambda x: x[0])
            sorted_indices = [x[1] for x in aspect_ratios]
            sorted_img_list = [img_list[i] for i in sorted_indices]

            # Pre-allocate results list
            sorted_results = [None] * len(sorted_img_list)

            # Step 2: Process in batches
            for i in range(0, len(sorted_img_list), self.infer_batch_num):
                batch_imgs = sorted_img_list[i:i + self.infer_batch_num]

                try:
                    # Preprocess current batch
                    batch_norm_imgs = self.preprocess(batch_imgs)

                    # Prepare input tensor
                    processed_input = self._prepare_input_tensor(batch_norm_imgs)

                    # Execute batch inference
                    if self.session:
                        # processed_input = batch_norm_imgs.astype(np.float32)
                        inputs = {self.input_name: processed_input}
                        outputs = self.session.run(self.output_names, inputs)
                        preds = outputs[0]

                        # Ensure FP32 output
                        preds = self._ensure_fp32_output(preds)
                    else:
                        raise RuntimeError("Model session is not initialized.")

                    # Post-process
                    rec_result = self.post_process(preds)
                    for batch_idx in range(len(rec_result)):
                        sorted_results[i + batch_idx] = rec_result[batch_idx]

                except Exception as e:
                    # Set failed results for current batch
                    for idx in range(len(batch_imgs)):
                        result_idx = i + idx
                        if result_idx < len(sorted_results) and sorted_results[result_idx] is None:
                            sorted_results[result_idx] = {
                                "text": "",
                                "confidence": 0.0,
                                "char_probs": [],
                                "error": f"Inference failed: {str(e)}"
                            }

            # Step 3: Restore original order
            # sorted_indices[i] tells us which original index is at sorted position i
            # We need to put sorted_results[i] back to position sorted_indices[i]
            batch_results = [None] * len(img_list)
            for sorted_pos in range(len(sorted_indices)):
                original_idx = sorted_indices[sorted_pos]
                batch_results[original_idx] = sorted_results[sorted_pos]
            
            # Step 4: Apply confidence correction (faithful to original implementation)
            batch_results = self._correct_confidence(batch_results)
        
            return batch_results

        except Exception as e:
            # Return empty results with same length as input
            return [{"text": "", "confidence": 0.0, "error": f"Batch inference failed: {str(e)}"} for _ in img_list]

    def recognize(self, 
                  img_list: List[np.ndarray], 
                  img_ori_list: List[np.ndarray], 
                  rotate_angles: List[str], 
                  rotate_scores: List[float],
                  repeat_rotate: bool = True,
                  global_rotate_threshold: float = 0.85,
                  rotate_conf_threshold: float = 0.9,
                  rec_conf_threshold: float = 0.75
                  ) -> Tuple[List[Dict], Optional[List[float]], Optional[Dict]]:
        """Faithful reproduction of the original recognize method from modules/predictors/recognizer.py."""
        meta_info = {"first_round_count": 0, "second_round_count": 0}
        if not img_list:
            return [], None, meta_info

        # --- 1. First Round: Global Orientation Judgment and Initial Inference ---
        # Calculate global orientation rate to avoid rotating unnecessarily
        positive_ori_rate = rotate_angles.count("0") / len(rotate_angles) if rotate_angles else 1.0
        if 5 <= len(img_list) < 10: positive_ori_rate *= 2
        if len(img_list) < 5:
            positive_ori_rate = 1 if positive_ori_rate > 0 else positive_ori_rate * 3

        # Prepare images for the first batch inference
        first_round_imgs = []
        rotate_try_list = [False] * len(img_list)
        temp_img_ori_list = list(img_ori_list) # Create a copy to avoid in-place modification

        for idx, (img, rotate_angle) in enumerate(zip(img_list, rotate_angles)):
            if rotate_angle == '180' and positive_ori_rate < global_rotate_threshold:
                first_round_imgs.append(rotate_image(img, '180'))
                rotate_try_list[idx] = True
                temp_img_ori_list[idx] = (temp_img_ori_list[idx] + 180) % 360
            else:
                first_round_imgs.append(img.copy())
        
        rec_res = self.batch_inference(first_round_imgs)
        meta_info["first_round_count"] = len(first_round_imgs)

        # --- 2. Second Round: Retry for low-confidence results ---
        if repeat_rotate:
            second_round_imgs, second_round_indices = [], []
            for idx, result in enumerate(rec_res):
                conf = result.get('confidence', 0.0)
                # Trigger retry if classification or recognition confidence is low
                if rotate_scores[idx] < rotate_conf_threshold or conf < rec_conf_threshold:
                    original_img = first_round_imgs[idx]
                    # Always add 180-degree rotated image
                    second_round_imgs.append(rotate_image(original_img, '180'))
                    second_round_indices.append(idx)
                    # For short/narrow images, add 90/270 degree rotations
                    h, w = original_img.shape[:2]
                    if w / h <= 1.5:
                        second_round_imgs.append(rotate_image(original_img, '90'))
                        second_round_indices.append(idx)
                        second_round_imgs.append(rotate_image(original_img, '270'))
                        second_round_indices.append(idx)
                    rotate_try_list[idx] = True
        
            if second_round_imgs:
                second_rec_results = self.batch_inference(second_round_imgs)
                meta_info["second_round_count"] = len(second_round_imgs)
                # Group results by original index and find the best one
                grouped_results = {}
                for i, res in enumerate(second_rec_results):
                    original_idx = second_round_indices[i]
                    if original_idx not in grouped_results:
                        grouped_results[original_idx] = []
                    grouped_results[original_idx].append(res)

                for idx, candidates in grouped_results.items():
                    # Include the original result in the comparison
                    candidates.append(rec_res[idx])
                    best_res = max(candidates, key=lambda x: x.get('confidence', 0.0))
                    rec_res[idx] = best_res

        # --- 3. Final Result Processing and Confidence Correction ---
        final_results = []
        for idx, result in enumerate(rec_res):
            text = result.get('text', '')
            confidence = result.get('confidence', 0.0)
            # Confidence correction for short text, as in the original code
            if len(text) <= 2: confidence += 0.2
        
            final_results.append({
                "text": text,
                "confidence": min(1.0, confidence),
                "rotate_try": rotate_try_list[idx],
                "rotate_angle": temp_img_ori_list[idx], # Use the temporary list
                "decode_info": result.get('decode_info', {})
            })

        return final_results, None, meta_info
