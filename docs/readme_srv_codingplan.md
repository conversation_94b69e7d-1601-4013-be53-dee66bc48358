该计划遵循“**由内到外，由简到繁**”的原则：首先搭建最核心、无依赖的模块（配置、数据结构），然后构建可独立验证的服务层（持久化、处理层mock），再向上集成到API层，最后逐步扩展功能。每一步完成后，应用都应能成功启动并通过明确的方式进行验证。

### 最终目录结构

```
ocr_system/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── api/
│   │   ├── __init__.py
│   │   └── endpoints/
│   │       ├── __init__.py
│   │       ├── ocr.py
│   │       └── results.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── orchestration.py
│   │   └── persistence.py
│   ├── processing/
│   │   ├── __init__.py
│   │   ├── text_detection.py
│   │   ├── text_recognition.py
│   │   ├── layout_analysis.py
│   │   ├── table_recognition.py
│   │   └── formula_recognition.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   └── ocr_results.py
│   └── core/
│       ├── __init__.py
│       └── config.py
├── results/
│   └── .gitignore
├── static/
│   └── index.html
├── poetry.lock
└── pyproject.toml
```

---

### 渐进式小步迭代开发与集成步骤

**先决条件**: 已使用 `poetry init` 初始化项目，并已安装 `fastapi`, `uvicorn`, `pydantic`, `pydantic-settings`。

---

#### **第 1 步: 搭建最简应用骨架**

-   **任务**: 创建一个最基础的、可以运行的 FastAPI 应用，包含一个健康检查接口。
-   **操作**:
    1.  创建 `app/main.py` 文件。
    2.  在 `main.py` 中，导入 `FastAPI`，创建 `app` 实例，并添加一个 `/health` GET 端点，返回 `{"status": "ok"}`。
-   **验证**:
    1.  在项目根目录运行 `poetry run uvicorn app.main:app --reload`。
    2.  访问 `http://127.0.0.1:6000/health`，应能看到 `{ "status": "ok" }`。

---

#### **第 2 步: 建立配置管理**

-   **任务**: 创建全局配置模块，用于管理如文件路径等环境变量。
-   **操作**:
    1.  创建 `app/core/__init__.py` 和 `app/core/config.py` 文件。
    2.  在 `config.py` 中，使用 `pydantic_settings` 定义 `Settings` 类，包含 `RESULTS_DIR: str = "./results/"` 和 `API_V1_STR: str = "/api/v1"`。
    3.  创建 `Settings` 的全局实例 `settings`。
-   **验证**:
    1.  修改 `app/main.py`，在启动时打印 `settings.RESULTS_DIR` 的值。
    2.  重新运行应用，应在控制台看到 `./results/` 输出。应用保持可运行。

---

#### **第 3 步: 定义核心数据结构 (Schemas)**

-   **任务**: 创建 API 的输入和基础输出数据模型，为后续的 API 端点定义做好准备。
-   **操作**:
    1.  创建 `app/schemas/__init__.py` 和 `app/schemas/ocr_results.py` 文件。
    2.  在 `ocr_results.py` 中，定义两个基础 Pydantic 模型：
        -   `OCRRequest` (包含 `image_base64: str`, `filename: str`)。
        -   `BaseResponse` (包含 `request_id: UUID`)。
-   **验证**:
    1.  在 `app/main.py` 中尝试从 `app.schemas.ocr_results` 导入这两个模型。
    2.  重新运行应用，应能无错误启动。

---

#### **第 4 步: 实现结果持久化服务**

-   **任务**: 创建负责将结果写入磁盘和从磁盘读取结果的服务。
-   **操作**:
    1.  创建 `app/services/__init__.py` 和 `app/services/persistence.py` 文件。
    2.  在 `persistence.py` 中，创建 `PersistenceService` 类。
    3.  实现 `__init__` 方法，接收结果目录路径，并使用 `pathlib` 确保该目录存在。
    4.  实现 `save_result(request_id, result_data)` 方法，它将 Pydantic 模型转换为 JSON 并写入文件。
    5.  实现 `get_result(request_id)` 方法，它根据 ID 读取 JSON 文件并返回字典。如果文件不存在，则抛出 `HTTPException(404)`。
-   **验证**:
    1.  应用依然可以无错误启动。
    2.  （可选）可编写一个临时的独立脚本来实例化 `PersistenceService` 并测试其 `save` 和 `get` 方法。

---

#### **第 5 步: 创建结果查询API端点**

-   **任务**: 将持久化服务连接到 API 层，实现第一个端到端功能：查询结果。
-   **操作**:
    1.  创建 `app/api/__init__.py`, `app/api/endpoints/__init__.py`, 和 `app/api/endpoints/results.py` 文件。
    2.  在 `results.py` 中，创建 `APIRouter`，并定义 `GET /results/{request_id}` 端点。
    3.  该端点函数使用 `Depends` 来注入 `PersistenceService` 的实例，并调用其 `get_result` 方法。
    4.  在 `app/main.py` 中，使用 `app.include_router` 注册 `results.py` 中的路由器，并带上 `prefix=settings.API_V1_STR`。
-   **验证**:
    1.  在 `./results/` 目录下手动创建一个名为 `123e4567-e89b-12d3-a456-************.json` 的文件，内容为 `{"message": "hello"}`。
    2.  运行应用，访问 `http://127.0.0.1:6000/api/v1/results/123e4567-e89b-12d3-a456-************`，应能看到 `{"message": "hello"}`。
    3.  访问一个不存在的UUID，应收到 `404 Not Found` 响应。

---

#### **第 6 步: 创建所有处理服务的模拟(Mock)实现**

-   **任务**: 创建处理层的所有服务文件及其类和方法。这些方法暂时只返回符合预期的、写死的（hardcoded）数据。
-   **操作**:
    1.  创建 `app/processing/__init__.py`。
    2.  逐一创建 `text_detection.py`, `text_recognition.py`, `layout_analysis.py`, `table_recognition.py`, `formula_recognition.py` 文件。
    3.  在每个文件中，定义对应的服务类（如 `TextDetectionService`）。
    4.  在每个类中，定义核心方法（如 `detect`），让它返回一个符合其逻辑输出的、写死的模拟数据。例如, `detect` 返回 `[[10, 10, 100, 30]]`。
-   **验证**:
    1.  应用依然可以无错误启动。这证明了所有文件和类结构正确，没有语法错误。

---

#### **第 7 步: 实现编排服务 (Orchestration Service)**

-   **任务**: 创建核心编排服务，并实现第一个（最简单的）处理流程。
-   **操作**:
    1.  创建 `app/services/orchestration.py`。
    2.  创建 `OrchestrationService` 类，其 `__init__` 方法接收所有处理服务和持久化服务的实例（为依赖注入做准备）。
    3.  实现一个内部辅助方法 `_decode_base64_image`。
    4.  实现第一个业务流程方法 `process_text_detection(image_base64: str)`。此方法应：
        -   生成 `request_id`。
        -   调用 `_decode_base64_image`。
        -   调用 `text_detection_service.detect` 获取模拟结果。
        -   在 `app/schemas/ocr_results.py` 中定义 `TextDetectionResponse` 模型。
        -   将结果组装成 `TextDetectionResponse` Pydantic 模型。
        -   调用 `persistence_service.save_result` 保存结果。
        -   返回 `TextDetectionResponse` 模型实例。
-   **验证**:
    1.  应用依然可以无错误启动。服务逻辑已准备好，等待被API层调用。

---

#### **第 8 步: 创建第一个OCR处理API端点**

-   **任务**: 实现第一个 `POST` 端点，将整个“请求->编排->处理(Mock)->持久化->响应”的流程打通。
-   **操作**:
    1.  创建 `app/api/endpoints/ocr.py` 文件。
    2.  在 `ocr.py` 中，创建 `APIRouter`，并定义 `POST /ocr/text_detection` 端点。
    3.  该端点接收 `OCRRequest` 模型，使用 `Depends` 注入 `OrchestrationService`，并调用其 `process_text_detection` 方法。
    4.  在 `app/main.py` 中，注册 `ocr.py` 的路由器。
-   **验证**:
    1.  运行应用。使用 `curl` 或 API 测试工具向 `http://127.0.0.1:6000/api/v1/ocr/text_detection` 发送一个包含有效JSON（`image_base64`字段可以是任意字符串）的POST请求。
    2.  应收到一个包含 `request_id` 和模拟 bbox 数据的 `200 OK` JSON 响应。
    3.  检查 `./results/` 目录，应出现一个新的以该 `request_id` 命名的 `.json` 文件。
    4.  使用第5步验证过的 `GET` 接口查询此 `request_id`，应能获取刚刚保存的内容。

---

#### **第 9 步: 逐步扩展其他处理流程**

-   **任务**: 逐一实现剩余的 OCR 处理流程及其对应的 API 端点。
-   **操作**:
    1.  **文本识别**:
        -   在 `schemas/ocr_results.py` 中添加 `TextRecognitionItem` 和 `TextRecognitionResponse` 模型。
        -   在 `OrchestrationService` 中添加 `process_text_recognition` 方法。
        -   在 `api/endpoints/ocr.py` 中添加 `POST /ocr/text_recognition` 端点。
    2.  **版面分析**:
        -   在 `schemas/ocr_results.py` 中添加 `LayoutAnalysisItem` 和 `LayoutAnalysisResponse` 等模型。
        -   在 `OrchestrationService` 中添加 `process_layout_analysis` 方法。
        -   在 `api/endpoints/ocr.py` 中添加 `POST /ocr/layout_analysis` 端点。
    3.  **重复此模式**，完成 `table_recognition` 和 `formula_recognition` 的逻辑和端点。
-   **验证**:
    -   每完成一个小功能（如文本识别），都重复**第 8 步**的验证流程，测试新的端点，检查其响应和持久化文件是否正确。

---

#### **第 10 步: 实现最终组合流程 (full_result)**

-   **任务**: 实现调用多个处理服务并将结果组合的 `full_result` 流程。
-   **操作**:
    1.  在 `schemas/ocr_results.py` 中确保 `FullOCRResponse` 及其所有嵌套模型都已定义。
    2.  在 `OrchestrationService` 中实现 `process_full_result` 方法。该方法将按顺序调用多个（模拟的）处理服务，并根据 HLD 中描述的逻辑（如遍历版面分析结果来调用表格/公式识别）来组装最终的 `FullOCRResponse` 对象。
    3.  在 `api/endpoints/ocr.py` 中添加 `POST /ocr/full_result` 端点。
-   **验证**:
    -   运行应用，调用 `/api/v1/ocr/full_result` 端点。
    -   检查返回的 JSON 响应结构是否完整且正确。
    -   检查生成的持久化文件内容是否为完整的组合结果。

---

#### **第 11 步: 实现图片存储和获取服务**

-   **任务**: 创建图片存储服务，用于保存上传的原始图片并提供获取功能。
-   **操作**:
    1.  创建 `images/` 目录和对应的 `.gitignore` 文件。
    2.  创建 `app/services/image_service.py`，实现 `ImageService` 类。
    3.  实现 `save_image` 和 `get_image_path` 方法。
    4.  修改 `OrchestrationService`，在处理图片时同时保存原始图片。
    5.  创建 `app/api/endpoints/images.py`，实现 `GET /images/{request_id}` 端点。
    6.  在 `app/main.py` 中注册新的路由器。
-   **验证**:
    1.  调用任何OCR处理端点后，检查 `images/` 目录下是否有对应的图片文件。
    2.  访问 `http://127.0.0.1:6000/api/v1/images/{request_id}` 应能获取原始图片。

---

#### **第 12 步: 实现可视化结果生成服务**

-   **任务**: 创建可视化服务，根据OCR结果和视图类型生成可视化图片。
-   **操作**:
    1.  创建 `visualizations/` 目录和对应的 `.gitignore` 文件。
    2.  创建 `app/services/visualization_service.py`，实现 `VisualizationService` 类。
    3.  实现基础的可视化生成逻辑（可以先用简单的文字覆盖或框线标注）。
    4.  创建 `app/api/endpoints/visualizations.py`，实现 `GET /visualizations/{request_id}?view_type=xxx` 端点。
    5.  在 `app/main.py` 中注册新的路由器。
-   **验证**:
    1.  调用OCR处理端点后，访问 `http://127.0.0.1:6000/api/v1/visualizations/{request_id}?view_type=full_result` 应能获取可视化结果图。
    2.  测试不同的 `view_type` 参数，确保返回不同的可视化效果。

---

#### **第 13 步: 配置静态文件服务**

-   **任务**: 使 FastAPI 能够托管前端静态文件。
-   **操作**:
    1.  创建 `static/` 目录和 `static/index.html` 文件（内容可以很简单，如 `<h1>Hello OCR</h1>`）。
    2.  在 `app/main.py` 中，导入 `StaticFiles`。
    3.  使用 `app.mount("/", StaticFiles(directory="static", html=True), name="static")` 挂载静态文件目录。**注意**：此挂载应在所有API路由器注册之后，以避免路径冲突。
-   **验证**:
    1.  运行应用，访问 `http://127.0.0.1:6000/`，应能看到 `index.html` 的内容。
    2.  同时，所有 `/api/...` 和 `/health` 端点应仍然正常工作。