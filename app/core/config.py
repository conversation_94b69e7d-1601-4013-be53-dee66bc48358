import os
from typing import List, Union

import ipaddress
from pydantic_settings import BaseSettings
from pathlib import Path

proj_dir = Path(__file__).parent.parent.parent
assets_dir = proj_dir / "app/assets"
models_dir = Path(r"/appdata/aipdf/agileocr/agileocr_models")

_layout_weights = os.environ.get("LAYOUT_WEIGHTS", str(models_dir / "dla_model.onnx"))
_text_det_weights = os.environ.get("TEXT_DET_WEIGHTS", str(models_dir / "text_det_model.onnx"))
_text_cls_weights = os.environ.get("TEXT_CLS_WEIGHTS", str(models_dir / "text_cls_default_paddle.onnx"))
_text_rec_weights = os.environ.get("TEXT_REC_WEIGHTS", str(models_dir / "test_rec_bq_svtr_v2_7M.onnx"))
_text_cls_vocab = os.environ.get("TEXT_CLS_VOCAB", str(models_dir / "bq_ocr_keys_v2.txt"))
_formula_weights = os.environ.get("FORMULA_WEIGHTS", str(models_dir / "unimernet_base"))
_unitable_assets = os.environ.get("UNITABLE_ASSETS", assets_dir / "unitable")

# Texteller configuration
_texteller_repo_name = os.environ.get("TEXTELLER_REPO_NAME", "OleehyO/TexTeller")
_texteller_model_path = os.environ.get("TEXTELLER_MODEL_PATH", None)  # Use HuggingFace by default
_texteller_cache_dir = os.environ.get("TEXTELLER_CACHE_DIR", str(Path("~/.cache/texteller").expanduser()))

class Settings(BaseSettings):
    """应用配置类，管理全局配置变量"""
    RESULTS_DIR: str = str(proj_dir / "output/results/")
    IMAGES_DIR: str = str(proj_dir / "output/images/")
    VISUALIZATIONS_DIR: str = str(proj_dir / "output/visualizations/")
    
    API_V1_STR: str = "/api/v1"
    LAYOUT_CONFIG: str = str(assets_dir / "dla_config.yaml")
    LAYOUT_WEIGHTS: str = _layout_weights

    TEXT_DET_CONFIG: str = str(assets_dir / "det_config.yaml")
    TEXT_DET_WEIGHTS: str = _text_det_weights

    TEXT_REC_CONFIG: str = str(assets_dir / "rec_config.yaml")
    TEXT_REC_WEIGHTS: str = _text_rec_weights
    TEXT_REC_VOCAB: str = _text_cls_vocab
    TEXT_CLS_WEIGHTS: str = _text_cls_weights
    
    # Unitable配置
    UNITABLE_ENCODER_WEIGHTS: str = str(_unitable_assets / "encoder.pth")
    UNITABLE_DECODER_WEIGHTS: str = str(_unitable_assets / "decoder.pth")
    UNITABLE_VOCAB_PATH: str = str(_unitable_assets / "vocab.json")
    USE_CUDA: bool = True

    FORMULA_WEIGHTS: str = _formula_weights

    # Texteller 配置
    TEXTELLER_CONFIG: str = str(assets_dir / "texteller_config.yaml")
    TEXTELLER_REPO_NAME: str = _texteller_repo_name
    TEXTELLER_MODEL_PATH: str = _texteller_model_path
    TEXTELLER_USE_ONNX: bool = False
    TEXTELLER_MAX_TOKENS: int = 1024
    TEXTELLER_BATCH_SIZE: int = 4
    TEXTELLER_CACHE_DIR: str = _texteller_cache_dir
    TEXTELLER_KEEP_STYLE: bool = False

    # 前端专用API的IP限制配置
    # 支持单个IP地址(如"127.0.0.1")或CIDR格式网段(如"***********/24")
    WEB_API_ALLOWED_IPS: List[str] = ["************/24", "127.0.0.1", "localhost", "::1", "***********/16", "*********/16"]
    WEB_API_IP_RESTRICTION_ENABLED: bool = True 

settings = Settings()
