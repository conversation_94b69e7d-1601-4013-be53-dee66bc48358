FROM nvidia/cuda:12.4.1-cudnn-devel-ubuntu22.04

WORKDIR /build

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo 'Asia/Shanghai' > /etc/timezone

RUN chmod 777 /tmp \
    && apt-get update  \
    && apt-get install -y --no-install-recommends \
    tzdata wget cron git gcc g++ make cmake gdb libtool build-essential \
    libgl1 libglib2.0-dev pkg-config autoconf openssh-server \
    && mkdir /var/run/sshd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && rm -rf /var/lib/apt/lists/*

# 设置系统时区
ENV TZ=Asia/Shanghai
# 支持显示中文
ENV LANG=C.UTF-8
# 添加conda到环境变量中
ENV CONDAHOME=/opt/miniforge3
ENV PATH=$PATH:$CONDAHOME/bin
ENV CONDA_AUTO_UPDATE_CONDA=false

# 安装Miniforge3
RUN wget --no-check-certificate https://github.com/conda-forge/miniforge/releases/download/23.11.0-0/Miniforge3-Linux-x86_64.sh -O ~/miniforge3.sh \
    && chmod +x ~/miniforge3.sh \
    && bash ~/miniforge3.sh -b -p $CONDAHOME \
    && $CONDAHOME/bin/conda init bash \
    && echo 'export PATH=/usr/bin:$PATH' >> ~/.bashrc \
    && conda config --set show_channel_urls true \
    && conda config --add channels conda-forge \
    && conda config --add channels pytorch \
    && rm ~/miniforge3.sh

# 安装Python相关依赖
RUN conda install -y \
    gcc=12.2.0 \
    && conda clean -ya

# ------------------------------------------------------------------
# 1. 安装系统依赖和现代版本的 Node.js (解决 pnpm 报错)
# ------------------------------------------------------------------
RUN apt-get update && \
    apt-get install -y curl libmagickwand-dev && \
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm && \
    pip install poetry uvicorn && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# ------------------------------------------------------------------
# 2. 设置工作环境
# ------------------------------------------------------------------
RUN rm -rf /build

ENV WK_DIR=/mnt/ocr_service/agileocr
WORKDIR ${WK_DIR}

# ------------------------------------------------------------------
# 3. 使用 Poetry (唯一的项目经理) 安装你的 Python 环境
# ------------------------------------------------------------------
RUN poetry config virtualenvs.create false
COPY pyproject.toml poetry.lock* ./
RUN poetry install --no-interaction --no-ansi --no-root && poetry cache clear . --all -n

# ------------------------------------------------------------------
# 4. 设置启动命令
# ------------------------------------------------------------------
CMD ["sleep", "infinity"]