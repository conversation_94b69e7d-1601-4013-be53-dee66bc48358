# Unitable表格识别集成详细设计

## 项目结构与总体设计

本设计方案旨在将RapidTable中的Unitable表格识别模型集成到现有的agileocr应用中，仅使用其表格检测能力，而文字识别则继续使用agileocr现有的OCR系统。这种集成方式可以在不依赖ONNX转换的情况下，直接利用PyTorch实现高质量的表格识别功能。

## 目录结构树 (Directory Tree)

```
app/
├── assets/
│   ├── det_config.yaml           # 现有配置文件
│   ├── test01.png                # 测试图像
│   ├── unitable/
│   │   ├── encoder.pth           # Unitable编码器权重
│   │   ├── decoder.pth           # Unitable解码器权重
│   │   └── vocab.json            # Unitable词汇表文件
│   └── ... (其他资源文件)
│
├── processing/
│   ├── unitable/
│   │   ├── __init__.py
│   │   ├── table_structure/
│   │   │   ├── __init__.py
│   │   │   ├── unitable_modules.py      # 核心模型组件(Encoder, GPTFastDecoder等)
│   │   │   └── table_structure_unitable.py  # 表格结构识别实现
│   │   └── table_matcher/
│   │       ├── __init__.py
│   │       ├── matcher.py               # 表格与OCR结果匹配
│   │       └── utils.py                 # 匹配所需的辅助函数
│   │
│   ├── table_recognition.py             # 表格识别服务，集成Unitable
│   └── ... (现有文件)
│
├── core/
│   ├── __init__.py
│   └── config.py                 # 应用配置，包含Unitable相关配置
└── ... (其他现有目录和文件)
```

## 整体逻辑和交互时序图

Unitable表格识别的集成主要通过修改`processing/table_recognition.py`实现，该文件将作为连接现有系统与Unitable模型的桥梁。整体工作流程如下：

1. 客户端发送表格识别请求
2. API层接收请求并调用编排服务
3. 编排服务调用表格识别服务
4. 表格识别服务使用Unitable模型进行表格结构识别
5. 表格识别服务使用现有OCR系统识别文本
6. 使用TableMatch将表格结构与OCR结果匹配
7. 返回最终的表格识别结果

```mermaid
sequenceDiagram
    participant Client
    participant API as api/endpoints/ocr.py
    participant Orchestration as services/orchestration.py
    participant TableRecognition as processing/table_recognition.py
    participant Unitable as processing/unitable/table_structure/table_structure_unitable.py
    participant Encoder as processing/unitable/table_structure/unitable_modules.py
    participant Decoder as processing/unitable/table_structure/unitable_modules.py
    participant TableMatcher as processing/unitable/table_matcher/matcher.py
    participant TextRecognition as processing/text_recognition.py

    Client->>API: POST /api/v1/ocr/table_recognition
    API->>Orchestration: process_table_recognition(image_bytes)
    Orchestration->>TableRecognition: recognize_table(image_bytes)
    TableRecognition->>Unitable: __call__(image)
    Unitable->>Encoder: forward(image)
    Encoder-->>Unitable: memory
    Unitable->>Decoder: setup_caches()
    Unitable->>Decoder: forward(memory, context)
    Decoder-->>Unitable: tokens
    Unitable-->>TableRecognition: structure_str_list, bboxes
    TableRecognition->>TextRecognition: recognize_text(image_bytes, text_bboxes)
    TextRecognition-->>TableRecognition: text_results
    TableRecognition->>TableMatcher: __call__(structure_str_list, bboxes, text_bboxes, text_results)
    TableMatcher-->>TableRecognition: html_with_content
    TableRecognition-->>Orchestration: table_result
    Orchestration-->>API: response
    API-->>Client: JSON response
```

## 数据实体结构深化

集成Unitable不需要引入新的数据实体，但需要理解现有数据流和Unitable输出的结构：

```mermaid
classDiagram
    class TableRecognitionService {
        -TableStructureUnitable unitable
        -TableMatch matcher
        +__init__(config)
        +recognize_table(image_bytes: bytes) -> TableResult
    }
    
    class TableStructureUnitable {
        -Encoder encoder
        -GPTFastDecoder decoder
        -Tokenizer vocab
        +__init__(config)
        +__call__(image) -> Tuple[List[str], np.ndarray, float]
        -loop_decode(context, eos_id_tensor, memory) -> torch.Tensor
        -decode_tokens(context) -> Tuple[np.ndarray, List[str]]
    }
    
    class Encoder {
        +forward(x) -> torch.Tensor
    }
    
    class GPTFastDecoder {
        +setup_caches(max_batch_size, max_seq_length, dtype, device)
        +forward(memory, tgt) -> torch.Tensor
    }
    
    class TableMatch {
        +__init__(filter_ocr_result=True, use_master=False)
        +__call__(pred_structures, cell_bboxes, dt_boxes, rec_res) -> str
        -match_result(dt_boxes, cell_bboxes, min_iou) -> Dict
        -get_pred_html(pred_structures, matched_index, ocr_contents) -> Tuple[str, List]
        -decode_logic_points(pred_structures) -> List
        -_filter_ocr_result(cell_bboxes, dt_boxes, rec_res) -> Tuple[List, List]
    }
    
    TableRecognitionService --> TableStructureUnitable : uses
    TableRecognitionService --> TableMatch : uses
    TableStructureUnitable --> Encoder : contains
    TableStructureUnitable --> GPTFastDecoder : contains
```

## 配置项

在`app/core/config.py`中添加以下配置项，使用相对路径指向`assets`目录下的资源文件：

| 环境变量/参数 | 文件 (`app/core/config.py`) | 描述 | 默认值 |
| :--- | :--- | :--- | :--- |
| `UNITABLE_ENCODER_WEIGHTS` | `Settings.UNITABLE_ENCODER_WEIGHTS` | Unitable编码器权重文件路径 | `./assets/unitable/encoder.pth` |
| `UNITABLE_DECODER_WEIGHTS` | `Settings.UNITABLE_DECODER_WEIGHTS` | Unitable解码器权重文件路径 | `./assets/unitable/decoder.pth` |
| `UNITABLE_VOCAB_PATH` | `Settings.UNITABLE_VOCAB_PATH` | Unitable词汇表文件路径 | `./assets/unitable/vocab.json` |
| `USE_CUDA` | `Settings.USE_CUDA` | 是否使用CUDA加速 | `False` |

## 依赖管理

集成Unitable模型需要添加以下Python依赖项到项目的`pyproject.toml`文件中：

```toml
[tool.poetry.dependencies]
# 现有依赖...
torch = ">=2.0.0, <2.1.0"
torchvision = ">=0.15.0, <0.16.0"
tokenizers = ">=0.13.0, <0.14.0"
```

这些依赖项的作用如下：
1. **torch**: PyTorch核心库，用于运行Unitable模型
2. **torchvision**: PyTorch的计算机视觉库，提供图像预处理功能
3. **tokenizers**: Hugging Face的tokenizers库，用于处理词汇表和token解码

安装命令：
```bash
poetry add torch@^2.0.0 torchvision@^0.15.0 tokenizers@^0.13.0
```

## 资源文件管理

### 模型权重文件

Unitable模型需要两个主要的权重文件：
1. **encoder.pth**: 编码器权重，负责图像特征提取
2. **decoder.pth**: 解码器权重，负责生成表格结构

这些文件应从RapidTable项目中复制，并放置在`app/assets/unitable/`目录下。

### 词汇表文件

词汇表文件`vocab.json`包含Unitable模型使用的所有token，用于解码模型输出。此文件也应从RapidTable项目中复制，并放置在`app/assets/unitable/`目录下。

### 资源加载策略

为了确保应用能够在不同环境中正确找到资源文件，我们将实现以下加载策略：

1. 首先尝试从配置指定的绝对路径加载
2. 如果失败，则尝试相对于应用根目录的路径
3. 最后尝试相对于当前工作目录的路径

这种策略将在`TableStructureUnitable.__init__`方法中实现，确保模型能够在各种部署环境中正确加载资源文件。

## 涉及到的文件详解 (File-by-File Breakdown)

### app/processing/unitable/table_structure/unitable_modules.py
a. 文件用途说明
  
包含Unitable模型的核心组件，包括Encoder、GPTFastDecoder等，用于表格结构识别的编码解码过程。

b. 文件内类图
```mermaid
classDiagram
    class Encoder {
        -ImgLinearBackbone backbone
        -PositionEmbedding pos_embed
        -nn.TransformerEncoder encoder
        -nn.LayerNorm norm
        +__init__()
        +forward(x: torch.Tensor) -> torch.Tensor
    }
    
    class GPTFastDecoder {
        -nn.ModuleList layers
        -TokenEmbedding token_embed
        -PositionEmbedding pos_embed
        -nn.Linear generator
        +__init__()
        +forward(memory: torch.Tensor, tgt: torch.Tensor) -> torch.Tensor
        +setup_caches(max_batch_size: int, max_seq_length: int, dtype, device)
    }
    
    class ImgLinearBackbone {
        -nn.Conv2d conv1
        -nn.Conv2d conv2
        -nn.Linear linear
        +__init__()
        +forward(x: torch.Tensor) -> torch.Tensor
    }
    
    class KVCache {
        -torch.Tensor k_cache
        -torch.Tensor v_cache
        +__init__(max_batch_size: int, max_seq_length: int, n_heads: int, head_dim: int, dtype, device)
        +update(input_pos: torch.Tensor, k_val: torch.Tensor, v_val: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]
    }
    
    Encoder --> ImgLinearBackbone : uses
    GPTFastDecoder --> KVCache : uses
```

c. 函数/方法详解

#### Encoder.forward
- 用途: 执行编码器前向传播，提取图像特征
- 输入参数: 
  - x: torch.Tensor - 输入图像张量，形状为[batch_size, channels, height, width]
- 输出: 
  - torch.Tensor - 编码后的特征表示memory，形状为[batch_size, seq_len, d_model]
- 实现步骤:
  1. 通过backbone提取图像特征
  2. 添加位置编码
  3. 通过Transformer编码器处理
  4. 应用层归一化
  5. 返回编码后的特征表示

#### GPTFastDecoder.forward
- 用途: 执行解码器前向传播，生成下一个token
- 输入参数:
  - memory: torch.Tensor - 编码器输出的特征表示
  - tgt: torch.Tensor - 当前已生成的token序列
- 输出:
  - torch.Tensor - 下一个预测的token ID
- 实现步骤:
  1. 获取token嵌入
  2. 添加位置编码
  3. 生成注意力掩码
  4. 通过Transformer块处理
  5. 生成下一个token的概率分布
  6. 返回最可能的token ID

### app/processing/unitable/table_structure/table_structure_unitable.py
a. 文件用途说明

实现TableStructureUnitable类，负责加载Unitable模型并提供表格结构识别功能。

b. 文件内类图
```mermaid
classDiagram
    class TableStructureUnitable {
        -Tokenizer vocab
        -Encoder encoder
        -GPTFastDecoder decoder
        -transforms.Compose transform
        -int max_seq_len
        -str device
        -int img_size
        +__init__(config: Dict)
        +__call__(image: np.ndarray) -> Tuple[List[str], np.ndarray, float]
        -loop_decode(context: torch.Tensor, eos_id_tensor: torch.Tensor, memory: torch.Tensor) -> torch.Tensor
        -decode_tokens(context: torch.Tensor) -> Tuple[np.ndarray, List[str]]
    }
```

c. 函数/方法详解

#### __init__
- 用途: 初始化TableStructureUnitable实例
- 输入参数:
  - config: Dict - 包含模型路径和设备信息的配置字典
- 输出: 无
- 实现步骤:
  1. 从配置中获取模型路径和设备信息
  2. 加载词汇表
  3. 初始化编码器和解码器
  4. 设置图像预处理转换

#### __call__
- 用途: 执行表格结构识别
- 输入参数:
  - image: np.ndarray - 输入图像的numpy数组
- 输出:
  - Tuple[List[str], np.ndarray, float] - 包含表格HTML结构字符串列表、单元格边界框坐标数组和处理耗时
- 实现步骤:
  1. 记录开始时间
  2. 预处理图像
  3. 设置解码器缓存
  4. 初始化上下文token
  5. 通过编码器获取memory
  6. 执行自回归解码
  7. 解析token序列，提取HTML结构和边界框坐标
  8. 缩放边界框坐标以匹配原始图像尺寸
  9. 返回结果和处理耗时

#### loop_decode
- 用途: 执行自回归解码过程，生成表格结构的token序列
- 输入参数:
  - context: torch.Tensor - 初始上下文token
  - eos_id_tensor: torch.Tensor - 结束标记的ID
  - memory: torch.Tensor - 编码器输出的特征表示
- 输出:
  - torch.Tensor - 解码后的完整上下文token序列
- 实现步骤:
  1. 循环进行自回归解码
  2. 每次调用解码器生成下一个token
  3. 将新token添加到上下文中
  4. 当生成结束标记或达到最大长度时停止
  5. 返回完整的token序列

#### decode_tokens
- 用途: 解析模型生成的token序列，提取表格HTML结构和边界框坐标
- 输入参数:
  - context: torch.Tensor - 解码后的token序列
- 输出:
  - Tuple[np.ndarray, List[str]] - 单元格边界框坐标数组和表格HTML结构字符串列表
- 实现步骤:
  1. 将token序列解码为字符串
  2. 使用正则表达式提取HTML标签和边界框坐标
  3. 处理表格结构和单元格属性
  4. 返回边界框坐标和HTML结构

### app/processing/unitable/table_matcher/matcher.py
a. 文件用途说明

实现TableMatch类，负责将OCR识别结果与表格结构进行匹配，生成包含文本内容的HTML表格。

b. 文件内类图
```mermaid
classDiagram
    class TableMatch {
        -bool filter_ocr_result
        -bool use_master
        +__init__(filter_ocr_result=True, use_master=False)
        +__call__(pred_structures: List[str], cell_bboxes: np.ndarray, dt_boxes: List[List[float]], rec_res: List[Tuple]) -> str
        -match_result(dt_boxes: List[List[float]], cell_bboxes: np.ndarray, min_iou: float) -> Dict
        -get_pred_html(pred_structures: List[str], matched_index: Dict, ocr_contents: List[Tuple]) -> Tuple[str, List]
        -decode_logic_points(pred_structures: List[str]) -> List
        -_filter_ocr_result(cell_bboxes: np.ndarray, dt_boxes: List[List[float]], rec_res: List[Tuple]) -> Tuple[List, List]
    }
```

c. 函数/方法详解

#### __call__
- 用途: 将OCR结果与表格结构匹配，生成包含文本内容的HTML表格
- 输入参数:
  - pred_structures: List[str] - 表格的HTML结构字符串列表
  - cell_bboxes: np.ndarray - 单元格边界框坐标数组
  - dt_boxes: List[List[float]] - OCR检测框坐标列表
  - rec_res: List[Tuple] - OCR识别结果列表
- 输出:
  - str - 包含文本内容的HTML表格字符串
- 实现步骤:
  1. 如果需要过滤OCR结果，调用_filter_ocr_result
  2. 调用match_result方法匹配OCR结果和单元格
  3. 调用get_pred_html方法生成最终的HTML表格
  4. 返回HTML表格字符串

#### match_result
- 用途: 根据IoU和距离计算将OCR结果匹配到对应的表格单元格
- 输入参数:
  - dt_boxes: List[List[float]] - OCR检测框坐标列表
  - cell_bboxes: np.ndarray - 单元格边界框坐标数组
  - min_iou: float - 最小IoU阈值
- 输出:
  - Dict - 匹配结果字典，键为单元格索引，值为OCR结果索引列表
- 实现步骤:
  1. 遍历OCR检测框
  2. 计算每个检测框与所有单元格的距离和IoU
  3. 根据IoU和距离选择最佳匹配的单元格
  4. 将匹配结果存入字典
  5. 返回匹配结果字典

#### get_pred_html
- 用途: 根据匹配结果生成包含文本内容的HTML表格
- 输入参数:
  - pred_structures: List[str] - 表格的HTML结构字符串列表
  - matched_index: Dict - 匹配结果字典
  - ocr_contents: List[Tuple] - OCR识别结果列表
- 输出:
  - Tuple[str, List] - 包含文本内容的HTML表格字符串和HTML标签列表
- 实现步骤:
  1. 初始化结果HTML列表
  2. 遍历表格结构标签
  3. 对于每个单元格标签，检查是否有匹配的OCR结果
  4. 如果有匹配结果，将OCR文本内容插入到HTML中
  5. 处理特殊标签和格式
  6. 返回最终的HTML字符串和标签列表

### app/processing/unitable/table_matcher/utils.py
a. 文件用途说明

提供表格匹配所需的辅助函数，如IoU计算和距离计算。

b. 函数详解

#### compute_iou
- 用途: 计算两个矩形框的IoU
- 输入参数:
  - box1: List[float] - 第一个矩形框坐标 [x_min, y_min, x_max, y_max]
  - box2: List[float] - 第二个矩形框坐标 [x_min, y_min, x_max, y_max]
- 输出:
  - float - IoU值
- 实现步骤:
  1. 计算交集矩形的坐标
  2. 计算交集面积
  3. 计算两个矩形的面积
  4. 计算并返回IoU值

#### distance
- 用途: 计算两个矩形框的L1距离
- 输入参数:
  - box1: List[float] - 第一个矩形框坐标 [x_min, y_min, x_max, y_max]
  - box2: List[float] - 第二个矩形框坐标 [x_min, y_min, x_max, y_max]
- 输出:
  - float - L1距离值
- 实现步骤:
  1. 计算两个矩形框中心点坐标
  2. 计算中心点之间的L1距离
  3. 返回距离值

### app/processing/table_recognition.py
a. 文件用途说明

表格识别服务，集成Unitable模型进行表格结构识别，并与现有OCR系统结合。

b. 文件内类图
```mermaid
classDiagram
    class TableRecognitionService {
        -TableStructureUnitable unitable
        -TableMatch matcher
        -TextRecognitionService text_recognition
        +__init__(config: Dict, text_recognition: TextRecognitionService)
        +recognize_table(image_bytes: bytes) -> Dict
        -_prepare_image(image_bytes: bytes) -> np.ndarray
        -_convert_to_table_result(html: str, bboxes: np.ndarray) -> Dict
    }
```

c. 函数/方法详解

#### __init__
- 用途: 初始化表格识别服务
- 输入参数:
  - config: Dict - 配置字典，包含模型路径和设备信息
  - text_recognition: TextRecognitionService - 文本识别服务实例
- 输出: 无
- 实现步骤:
  1. 保存文本识别服务实例
  2. 创建Unitable配置字典
  3. 初始化TableStructureUnitable实例
  4. 初始化TableMatch实例

#### recognize_table
- 用途: 识别图像中的表格结构和内容
- 输入参数:
  - image_bytes: bytes - 图像的二进制数据
- 输出:
  - Dict - 表格识别结果，包含HTML表格和单元格信息
- 实现步骤:
  1. 将图像二进制数据转换为numpy数组
  2. 调用Unitable模型识别表格结构和单元格边界框
  3. 使用文本识别服务识别图像中的文本
  4. 使用TableMatch将表格结构与OCR结果匹配
  5. 将结果转换为标准格式
  6. 返回最终的表格识别结果

#### _prepare_image
- 用途: 将图像二进制数据转换为numpy数组
- 输入参数:
  - image_bytes: bytes - 图像的二进制数据
- 输出:
  - np.ndarray - 图像的numpy数组
- 实现步骤:
  1. 使用cv2.imdecode将二进制数据解码为numpy数组
  2. 返回图像数组

#### _convert_to_table_result
- 用途: 将HTML表格和边界框转换为标准的表格识别结果格式
- 输入参数:
  - html: str - HTML表格字符串
  - bboxes: np.ndarray - 单元格边界框坐标数组
- 输出:
  - Dict - 标准格式的表格识别结果
- 实现步骤:
  1. 解析HTML表格结构
  2. 提取单元格信息，包括位置、行列跨度和文本内容
  3. 创建符合schemas.ocr_results.py中定义的结构的结果字典
  4. 返回结果字典

### app/core/config.py (修改)
a. 文件用途说明

添加Unitable相关配置项，指向assets目录下的资源文件。

b. 修改内容
```python
class Settings(BaseSettings):
    # 现有配置项...
    
    # Unitable配置
    UNITABLE_ENCODER_WEIGHTS: str = str(assets_dir / "unitable/encoder.pth"
    UNITABLE_DECODER_WEIGHTS: str = str(assets_dir / "unitable/decoder.pth"
    UNITABLE_VOCAB_PATH: str = str(assets_dir / "unitable/vocab.json"
    USE_CUDA: bool = True
```

## 迭代演进依据

1. **模块化设计**: 将Unitable相关代码放在独立的`unitable`目录下，与现有代码保持清晰的边界，便于后续维护和升级。

2. **最小修改原则**: 仅修改`table_recognition.py`和`config.py`，不影响现有系统的其他部分，降低集成风险。

3. **接口一致性**: 确保`TableRecognitionService.recognize_table`方法的输入输出与现有系统保持一致，使编排服务无需做大量修改。

4. **可扩展性**: 如果未来需要支持更多表格识别模型，可以轻松添加新的实现，并通过配置选择使用哪种模型。

5. **性能考虑**: 直接使用PyTorch实现，避免了ONNX转换可能带来的性能损失和兼容性问题。

6. **依赖管理**: 需要在项目的依赖管理文件中添加PyTorch相关依赖，确保系统能正常运行Unitable模型。

7. **资源管理**: 将模型权重和词汇表文件统一放置在`assets/unitable/`目录下，便于管理和部署。 