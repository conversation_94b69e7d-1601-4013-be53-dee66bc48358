import logging
from typing import List, Dict, Any, Optional
from app.core.config import settings


import cv2
import yaml
import time
from PIL import Image
import numpy as np
import torch
import torchvision.transforms as T
import onnxruntime as ort

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[36m{time.asctime() + "  " + __name__}\033[0m')

IDX_CLS_DICT = {
    0: "Title",
    1: "Text",
    2: "Table",
    3: "Formula",
    4: "Figure",
    5: "UnKnown"
}

class LayoutAnalysisService:
    """版面分析服务，负责分析文档的布局结构"""
    def __init__(self):
        with open(settings.LAYOUT_CONFIG, 'r', encoding='utf-8') as f:
            config = yaml.load(f, Loader=yaml.Loader)

        self.imgsz = config['Process']['imgsz']
        self.conf_thresh = config['Process']['conf_thresh']

        # init onnx model.
        self.dla_model_path = settings.LAYOUT_WEIGHTS
        provider = 'CUDAExecutionProvider' if torch.cuda.is_available() else 'CPUExecutionProvider'
        logger.info(f"  使用{provider}加载版面分析模型")
        # self.sess = ort.InferenceSession(self.dla_model_path, providers=['CUDAExecutionProvider'])
        self.sess = ort.InferenceSession(self.dla_model_path, providers=[provider])

        # 优化：只保留ToTensor，缩放用OpenCV完成
        self.transforms = T.Compose([
            T.ToTensor(),
        ])
        self.img_w, self.img_h = 0, 0

    def _inference(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """进行推理"""
        im_data = self._preprocess(image)

        # 修复：传递缩放后的尺寸给模型，因为输入图像已经被缩放到固定尺寸
        output = self.sess.run(output_names=None, input_feed={"images": im_data, "orig_target_sizes": np.array([[self.imgsz, self.imgsz]])})

        labels, boxes, scores = output
        results = self._postprocess(labels[0], boxes[0], scores[0])

        return results

    def _preprocess(self, image: np.ndarray) -> np.ndarray:
        """预处理图像 - 优化版本，避免与输入大小相关的耗时"""
        # 记录原始图像尺寸
        self.img_h, self.img_w = image.shape[:2]

        # 直接用OpenCV缩放到固定尺寸（避免PIL转换开销）
        image_resized = cv2.resize(image, (self.imgsz, self.imgsz), interpolation=cv2.INTER_LINEAR)

        # BGR转RGB（只在固定尺寸上操作，耗时固定）
        image_rgb = cv2.cvtColor(image_resized, cv2.COLOR_BGR2RGB)

        # 转换为PIL（固定尺寸，耗时固定）
        image_pil = Image.fromarray(image_rgb)

        # 应用transforms（只有ToTensor，耗时固定）
        im_data = self.transforms(image_pil).unsqueeze(0)
        return im_data.numpy()

    def _rm_same_start(self, results_dict: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        results_dict.sort(key=lambda x: (x["bbox"][0], x["area"]), reverse=True)

        out_results = []
        len_results = len(results_dict)
        i = 0
        while i < len_results:
            bbox_f = results_dict[i]["bbox"]
            conf_max = results_dict[i]["confidence"]
            match_idx = i
            fx1, fy1, fx2, fy2 = bbox_f
            for j in range(i+1, len_results):
                bbox_p = results_dict[j]["bbox"]
                px1, py1, px2, py2 = bbox_p

                if px1 != fx1 or py1 != fy1:
                    break

                if px2 != fx2 or py2 != fy2:            # 表示同起点的大框包小框
                    continue

                if px2 == fx2 and py2 == fy2:           # 同起点，同大小的框
                    if results_dict[j]["type"] == "Formula":                # text_recognizer can not recognize formula elements.
                        match_idx = j
                    elif results_dict[j]["confidence"] > conf_max:
                        conf_max = results_dict[j]["confidence"]
                        match_idx = j
            else:
                j = len_results
            i = j
                
            out_results.append(results_dict[match_idx])
        return out_results
    
    def _postprocess(self, labels, boxes, scores) -> List[Dict[str, Any]]:
        mask = scores > self.conf_thresh
        lab = labels[mask]
        box = boxes[mask]
        scrs = scores[mask]

        # 计算从固定尺寸到原始尺寸的缩放比例
        ratio_w = self.img_w / self.imgsz
        ratio_h = self.img_h / self.imgsz

        results = []
        for idx, b in enumerate(box):
            x1, y1, x2, y2 = list(map(int, b))  # 恢复：确保坐标为整数
            cls_idx = lab[idx].item()
            category = IDX_CLS_DICT[cls_idx]

            # 将坐标从固定尺寸映射回原始尺寸
            x1 = max(0, round(x1 * ratio_w))
            y1 = max(0, round(y1 * ratio_h))
            x2 = min(round(x2 * ratio_w), self.img_w - 1)
            y2 = min(round(y2 * ratio_h), self.img_h - 1)
            conf = scrs[idx]
            results.append({
                "bbox": [x1, y1, x2, y2],
                "area": ((x2 - x1) * (y2 - y1)),
                "type": category,
                "confidence": conf,
            })

        results = self._rm_same_start(results)

        return results

    def analyze(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """分析图像的版面结构

        Args:
            image: BGR格式的图像数组 (H, W, C)

        Returns:
            List[Dict[str, Any]]: 版面分析结果列表，每项包含区域类型、边界框和置信度
        """

        start_time = time.time()

        results = self._inference(image)

        logger.info(f"  本次版面分析调用耗时为:   {(time.time() - start_time) * 1000} ms")

        return results
