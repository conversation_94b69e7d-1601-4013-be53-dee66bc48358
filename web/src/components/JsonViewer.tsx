import React from 'react';
import { Button, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';

interface JsonViewerProps {
  data: object;
}

export const JsonViewer: React.FC<JsonViewerProps> = ({ data }) => {
  const copyToClipboard = () => {
    // 创建一个临时文本区域来实现复制功能
    const textArea = document.createElement('textarea');
    textArea.value = JSON.stringify(data, null, 2);
    
    // 隐藏文本区域但保持它是可选择的
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    
    // 选择并复制内容
    textArea.focus();
    textArea.select();
    
    let success = false;
    try {
      // 使用传统的document.execCommand执行复制操作
      success = document.execCommand('copy');
      if (success) {
        message.success('JSON已复制到剪贴板');
      } else {
        message.error('复制失败，请手动选择并复制');
      }
    } catch (err) {
      message.error('复制失败，请手动选择并复制');
    }
    
    // 清理DOM
    document.body.removeChild(textArea);
  };

  return (
    <div style={{ position: 'relative' }}>
      <Button 
        icon={<CopyOutlined />}
        onClick={copyToClipboard}
        style={{ 
          position: 'absolute', 
          top: '8px', 
          left: '8px', 
          zIndex: 1 
        }}
      >
        复制
      </Button>
      
      <div 
        style={{ 
          maxHeight: '400px', 
          overflow: 'auto',
          backgroundColor: '#f5f5f5',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #d9d9d9'
        }}
      >
        <pre style={{ 
          margin: 0, 
          fontSize: '12px',
          lineHeight: '1.5',
          fontFamily: 'Monaco, Consolas, "Courier New", monospace'
        }}>
          <code>
            {JSON.stringify(data, null, 2)}
          </code>
        </pre>
      </div>
    </div>
  );
}; 