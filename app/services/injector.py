from app.services.persistence import PersistenceService
from app.services.orchestration import OrchestrationService
from app.services.image_service import ImageService
from app.services.initialization_manager import get_initialization_manager
from app.core.config import settings
from app.processing.text_detection import TextDetectionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.layout_analysis import LayoutAnalysisService
# from app.processing.unitable.table_rec_unitable import UnitableTableRecognition
from app.processing.cycle_centernet.table_rec_cycle_centernet import CycleCenterNetTable
from app.processing.formula_recognition import FormulaRecognitionService

import os
import tarfile
import logging
from app.utils.oss_downloader import oss_download_file
from app.utils.file_folder_path import agileocr_cache_dir
logger = logging.getLogger(__name__)

# 单实例缓存 - 现在通过InitializationManager管理
_orchestra = None

def init_model_resources():
    """初始化模型资源 - 已废弃，现在由InitializationManager管理

    为了保持向后兼容性而保留，实际功能已迁移到InitializationManager
    """
    logger.warning("init_model_resources()已废弃，请使用InitializationManager进行初始化")
    # 这里不做任何操作，因为初始化现在由InitializationManager统一管理

def get_persistence_service():
    """获取持久化服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("persistence_service")
    if service is None:
        raise RuntimeError("持久化服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_image_service():
    """获取图片服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("image_service")
    if service is None:
        raise RuntimeError("图片服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_text_detection_service():
    """获取文本检测服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("text_detection_service")
    if service is None:
        raise RuntimeError("文本检测服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_text_recognition_service():
    """获取文本识别服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("text_recognition_service")
    if service is None:
        raise RuntimeError("文本识别服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_layout_analysis_service():
    """获取版面分析服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("layout_analysis_service")
    if service is None:
        raise RuntimeError("版面分析服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_table_recognition_service():
    """获取表格识别服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("table_recognition_service")
    if service is None:
        raise RuntimeError("表格识别服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_formula_recognition_service():
    """获取公式识别服务单实例"""
    manager = get_initialization_manager()
    service = manager.get_service("formula_recognition_service")
    if service is None:
        raise RuntimeError("公式识别服务未初始化，请确保在应用启动时调用了初始化管理器")
    return service

def get_orchestration_service():
    """
    创建并返回一个配置完整的OrchestrationService单实例
    使用所有单实例服务
    """
    # 检查初始化管理器是否已完成初始化
    manager = get_initialization_manager()
    if not manager.is_ready():
        raise RuntimeError("算法服务未完全初始化，请确保在应用启动时调用了初始化管理器")

    global _orchestra
    if _orchestra is None:
        _orchestra = OrchestrationService(
            text_detection_service=get_text_detection_service(),
            text_recognition_service=get_text_recognition_service(),
            layout_analysis_service=get_layout_analysis_service(),
            table_recognition_service=get_table_recognition_service(),
            formula_recognition_service=get_formula_recognition_service(),
            persistence_service=get_persistence_service(),
            image_service=get_image_service()
        )
    return _orchestra

