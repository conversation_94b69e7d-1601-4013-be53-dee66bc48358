#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/06/23 15:45
# <AUTHOR> Cascade
# @FileName: classifier.py

import os
import time
import cv2
import torch
import logging
import numpy as np
import onnxruntime as ort
from typing import List, Dict, Optional, Tuple
from app.core.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[35m{time.asctime() + "  " + __name__}\033[0m')

# =================================================================================
# 1. Migrated utility functions from modules/utils/utils.py
# =================================================================================

def _get_rotate_crop_image(img: np.ndarray, points: np.ndarray) -> Tuple[np.ndarray, int]:
    """
    Helper function to get rotated and cropped image.
    Original: get_rotate_crop_image from utils.py
    """
    assert len(points) == 4, "The number of points must be 4."
    img_crop_width = int(
        max(
            np.linalg.norm(points[0] - points[1]),
            np.linalg.norm(points[2] - points[3])
        )
    )
    img_crop_height = int(
        max(
            np.linalg.norm(points[0] - points[3]),
            np.linalg.norm(points[1] - points[2])
        )
    )
    pts_std = np.float32([
        [0, 0],
        [img_crop_width, 0],
        [img_crop_width, img_crop_height],
        [0, img_crop_height],
    ])
    M = cv2.getPerspectiveTransform(points, pts_std)
    dst_img = cv2.warpPerspective(
        img,
        M,
        (img_crop_width, img_crop_height),
        borderMode=cv2.BORDER_REPLICATE,
        flags=cv2.INTER_CUBIC,
    )
    dst_img_height, dst_img_width = dst_img.shape[0:2]

    img_ori = 0
    if dst_img_height * 1.0 / dst_img_width >= 1.5:
        dst_img = np.rot90(dst_img)
        img_ori = 270
    return dst_img, img_ori

def crop_image(img: np.ndarray, points: np.ndarray) -> Tuple[np.ndarray, int]:
    """
    Crops an image based on the minimum area rectangle of the given points.
    Original: get_minarea_rect_crop from utils.py
    """
    bounding_box = cv2.minAreaRect(np.array(points).astype(np.int32))
    points = sorted(list(cv2.boxPoints(bounding_box)), key=lambda x: x[0])

    index_a, index_b, index_c, index_d = 0, 1, 2, 3
    if points[1][1] > points[0][1]:
        index_a = 0
        index_d = 1
    else:
        index_a = 1
        index_d = 0
    if points[3][1] > points[2][1]:
        index_b = 2
        index_c = 3
    else:
        index_b = 3
        index_c = 2

    box = [points[index_a], points[index_b], points[index_c], points[index_d]]
    crop_img, img_ori = _get_rotate_crop_image(img, np.array(box))
    return crop_img, img_ori

def sorted_boxes(dt_boxes: np.ndarray) -> List:
    """
    Sorts text boxes from top to bottom, left to right.
    Original: sorted_boxes from utils.py
    """
    num_boxes = dt_boxes.shape[0]
    sorted_boxes_list = sorted(dt_boxes, key=lambda x: (x[0][1], x[0][0]))
    _boxes = list(sorted_boxes_list)

    for i in range(num_boxes - 1):
        for j in range(i, -1, -1):
            if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and \
               (_boxes[j + 1][0][0] < _boxes[j][0][0]):
                tmp = _boxes[j]
                _boxes[j] = _boxes[j + 1]
                _boxes[j + 1] = tmp
            else:
                break
    return _boxes

# =================================================================================
# 2. Migrated Pre-process and Post-process logic
# =================================================================================

class _ClsPreProcess:
    """
    Pre-processing class for text classification model.
    Original: ClsPreProc from modules/utils/cls_pre_proc.py
    """
    def __init__(self, image_shape: List[int] = [80, 160], mean: List[float] = [0.485, 0.456, 0.406], std: List[float] = [0.229, 0.224, 0.225], **kwargs):
        # image_shape is in [H, W] format
        self.height = image_shape[0]
        self.width = image_shape[1]
        self.mean = np.array(mean, dtype=np.float32)
        self.std = np.array(std, dtype=np.float32)

    def to_rgb(self, img: np.ndarray) -> np.ndarray:
        if len(img.shape) == 3 and img.shape[2] == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        elif len(img.shape) == 2:
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
        return img

    def resize(self, img: np.ndarray) -> np.ndarray:
        return cv2.resize(img, (self.width, self.height))

    def normalize(self, img: np.ndarray) -> np.ndarray:
        img = img.astype(np.float32) / 255.0
        img = (img - self.mean) / self.std
        return img

    def to_chw(self, img: np.ndarray) -> np.ndarray:
        return np.transpose(img, (2, 0, 1))

    def __call__(self, img: np.ndarray) -> np.ndarray:
        img = self.to_rgb(img)
        img = self.resize(img)
        img = self.normalize(img)
        img = self.to_chw(img)
        img = np.expand_dims(img, axis=0)
        return img

class _ClsPostProcess:
    """
    Post-processing class for text classification model.
    Original: ClsPostProc from modules/utils/cls_post_proc.py
    """
    def __init__(self, label_map: Optional[Dict[int, str]] = None, **kwargs):
        self.label_map = label_map if label_map else {0: '0', 1: '180'}

    def __call__(self, pred: np.ndarray) -> Tuple[str, float]:
        # The model output `pred` for a single image has shape (1, num_classes), e.g., (1, 2).
        # We need to extract the 1D array of scores.
        scores = pred[0] if len(pred.shape) == 2 else pred

        pred_idx = np.argmax(scores)
        confidence = float(scores[pred_idx])
        angle = self.label_map.get(int(pred_idx), '0')
        
        return angle, confidence

# =================================================================================
# 3. TextClassifier implementation
# =================================================================================

class TextClassifier:
    """
    Text direction classifier, supporting ONNX model inference.
    This is a self-contained implementation by migrating code from:
    - modules/predictors/base.py
    - modules/predictors/classifier.py
    """
    def __init__(self, config: dict):
        """
        Initializes the TextClassifier.

        Args:
            config (dict): Configuration dictionary for the classifier.
        """
        self.config = config
        # self.model_path = self.config.get("model_path")
        self.model_path = settings.TEXT_CLS_WEIGHTS
        self.model_type = self._determine_model_type(self.model_path)
        self.device_type = self.config.get("device_type", "cpu") if torch.cuda.is_available() else 'cpu'
        self.device_id = self.config.get("device_id", 0)
        self.session = None
        self.input_name = None
        self.output_names = None
        self.preprocess = None
        self.post_process = None
        
        self.init_model()
        self._log_model_loaded()

    def _determine_model_type(self, model_path: str) -> str:
        if not model_path or not model_path.endswith(".onnx"):
            raise ValueError("Only ONNX models are supported in this module.")
        return "onnx"

    def _get_ort_providers(self) -> List[str]:
        providers = ['CPUExecutionProvider']
        if self.device_type in ['gpu', 'cuda']:
            provider_options = [{'device_id': self.device_id}]
            providers.insert(0, ('CUDAExecutionProvider', provider_options[0]))
        return providers

    def _create_onnx_session(self, model_path: str) -> ort.InferenceSession:
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        sess_options = ort.SessionOptions()
        sess_options.intra_op_num_threads = os.cpu_count() or 1
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        return ort.InferenceSession(
            model_path, 
            sess_options=sess_options,
            providers=self._get_ort_providers()
        )

    def _prepare_input_tensor(self, input_data: np.ndarray) -> np.ndarray:
        return input_data.astype(np.float32)

    def _ensure_fp32_output(self, output_data: np.ndarray) -> np.ndarray:
        if output_data is not None and output_data.dtype != np.float32:
            return output_data.astype(np.float32)
        return output_data

    def _log_model_loaded(self) -> None:
        device_info = f"Device: {self.device_type.upper()}"
        if self.device_type in ['gpu', 'cuda']:
            device_info += f" (ID: {self.device_id})"
        logger.info(f"  [TextClassifier] Model loaded. {device_info}, Model Type: ONNX")

    def init_model(self):
        """Initializes the classification model."""
        if not self.model_path or not os.path.exists(self.model_path):
            raise ValueError("Model path is not specified or does not exist in the config.")
        
        if self.model_type == "onnx":
            self.session = self._create_onnx_session(self.model_path)
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}. Only 'onnx' is supported.")
        
        preprocess_config = self.config.get("pre_process", {})
        self.preprocess = _ClsPreProcess(**preprocess_config)
        
        postprocess_config = self.config.get("post_process", {})
        if 'label_map' in self.config.get("Global", {}):
            postprocess_config['label_map'] = self.config['Global']['label_map']
        self.post_process = _ClsPostProcess(**postprocess_config)

    def classify(self, ori_im: np.ndarray, dt_boxes: List[List[int]], save_path: Optional[str] = None):
        """
        Performs text direction classification on a list of detected boxes.
        Original: TextClassifier.classify from classifier.py
        """
        time_track = {}
        tic = time.time()
        
        dt_boxes = sorted_boxes(np.array(dt_boxes))
        img_crop_list = []
        img_ori_list = []
        for box in dt_boxes:
            img_crop, img_ori = crop_image(ori_im, box)
            img_crop_list.append(img_crop)
            img_ori_list.append(img_ori)
        
        time_track["crop_and_sort"] = time.time() - tic

        if save_path and os.path.exists(save_path):
            for idx, img in enumerate(img_crop_list):
                cv2.imwrite(os.path.join(save_path, f"cls_crop_{idx}.jpg"), img)
        
        if not img_crop_list:
            return [], [], dt_boxes, [], [], time_track

        tic = time.time()
        batch_inputs = [self.preprocess(img) for img in img_crop_list]
        time_track["preprocess"] = time.time() - tic

        tic = time.time()
        infer_results = []
        if self.session:
            for input_data in batch_inputs:
                processed_input = self._prepare_input_tensor(input_data)
                outputs = self.session.run(self.output_names, {self.input_name: processed_input})
                pred_results = self._ensure_fp32_output(outputs[0])
                infer_results.append(pred_results)
        else:
            raise RuntimeError("Model session is not initialized.")
        time_track[f"inference_{self.device_type}"] = time.time() - tic

        tic = time.time()
        angles, scores = [], []
        for pred in infer_results:
            angle, score = self.post_process(pred)
            angles.append(angle)
            scores.append(score)
        time_track["postprocess"] = time.time() - tic

        return angles, scores, dt_boxes, img_crop_list, img_ori_list, time_track
