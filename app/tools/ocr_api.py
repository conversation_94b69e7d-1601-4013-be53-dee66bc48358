import base64
import json
import os
import requests
import sys
from pathlib import Path

project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))

from typing import Dict, Optional, Any, Tuple
from app.utils import ocr_visualization as ocr_vis


class OcrApiError(Exception):
    """OCR API调用错误"""
    def __init__(self, http_code: int, status_code: str, message: str, request_id: Optional[str] = None):
        self.http_code = http_code
        self.status_code = status_code
        self.message = message
        self.request_id = request_id
        super().__init__(self.__str__())
    
    def __str__(self) -> str:
        request_id_tip = ""
        if self.request_id:
            request_id_tip = f" (请求ID: {self.request_id})"
        return f"OCR API错误 [HTTP {self.http_code}] [{self.status_code}]: {self.message}{request_id_tip}"


def call_ocr_api(image_path: Optional[str] = None, api_name: str = "full_result", base_url: str = "http://**************:6060") -> Dict[str, Any]:
    """
    调用OCR服务API

    Args:
        image_path: 图片文件路径
        api_name: API名称，可选值: text_detection, text_recognition, layout_analysis,
                 table_recognition, formula_recognition, full_result
        base_url: API服务基础URL，默认为https://modelzooapi-test-inner.zhhainiao.com

    Returns:
        Dict[str, Any]: API响应结果

    Raises:
        ValueError: 当API名称无效或输入参数无效时
        FileNotFoundError: 当图片文件不存在时
        OcrApiError: 当API请求失败或返回错误状态时
    """
    
    # 验证API名称
    valid_apis = [
        "text_detection",
        "text_recognition",
        "layout_analysis",
        "table_recognition",
        "formula_recognition",
        "full_result"
    ]

    if api_name not in valid_apis:
        raise ValueError(f"不支持的API名称: {api_name}，可用选项: {', '.join(valid_apis)}")

    # 验证输入参数
    if not image_path:
        raise ValueError("必须提供image_path或image_url之一")

    image_url = ""
    if image_path.lower().startswith("http"):
        image_url = image_path
    # 准备请求数据
    request_data = {}

    # 优先使用image_url
    if image_url:
        request_data["image_url"] = image_url
        filename = os.path.basename(image_url.split('?')[0])  # 去掉查询参数
        request_data["filename"] = filename
    # 如果没有URL，使用本地文件路径
    else:
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
    
        with open(image_path, "rb") as img_file:
            image_data = img_file.read()
            image_base64 = base64.b64encode(image_data).decode("utf-8")
    
        request_data["image_base64"] = image_base64
        request_data["filename"] = os.path.basename(image_path)
    
    # 构建API URL
    api_url = f"{base_url}/api/v1/ocr/{api_name}"
    response = requests.post(
        api_url, 
        json=request_data, 
        headers={"Content-Type": "application/json"}
    )

    resp_json = {}
    try:
        resp_json = response.json()
    except:
        resp_json["status"] = "unk/json_err"
        resp_json["error_message"] = f"response text:\n{response.text}"
    
    status = resp_json.get("status", "unknown")
    if status != "ok" or response.status_code != 200:
        error_msg = resp_json.get("error_message", "未知错误")
        request_id = resp_json.get("request_id")
        raise OcrApiError(
            response.status_code,
            status,
            error_msg,
            request_id
        )
    return resp_json["results"]

def visual_result(api_name, image_path, result):
    origin_img = Image.open(image_path)
    ocr_img = ocr_vis.generate_ocr_visualization(api_name, origin_img, result)
    ocr_img_path = Path(image_path).stem + "_vis.jpg"
    ocr_img.save(ocr_img_path, format="JPEG")
    ocr_img.show()
    return ocr_img_path

def test_url_functionality():
    """测试URL功能的示例函数"""
    # 测试用的图像URL（这里使用一个示例URL，实际使用时需要替换为有效的图像URL）
    test_image_url = "https://img0.baidu.com/it/u=515120316,3012489254&fm=253&app=138&f=JPEG?w=800&h=1131"

    print("=== 测试URL功能 ===")
    result = call_ocr_api(image_path=test_image_url, api_name="text_detection")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("URL test successful!:")


if __name__ == "__main__":
    from PIL import Image

    # 命令行参数处理
    if len(sys.argv) >= 2:
        if sys.argv[1] == "--test-url":
            test_url_functionality()
            sys.exit(0)
        elif len(sys.argv) >= 3:
            # 传统的文件路径调用方式
            image_path = sys.argv[1]
            api_name = sys.argv[2]
            result = call_ocr_api(image_path=image_path, api_name=api_name)
            print(json.dumps(result, ensure_ascii=False, indent=2))
            ocr_img_path = visual_result(api_name, image_path, result)
            print(f"result image: {ocr_img_path}")
        else:
            print("用法: ")
            print("  python ocr_api.py <图片路径> <API名称>")
            print("  python ocr_api.py --test-url  # 测试URL功能")
            sys.exit(1)
    else:
        # 默认测试
        print("=== 默认测试（本地文件） ===")
        image_path = r"app/assets/test01.png"
        api_name = "full_result"
        result = call_ocr_api(image_path=image_path, api_name=api_name)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        ocr_img_path = visual_result("full_result", image_path, result)
        print(f"result image: {ocr_img_path}")
    