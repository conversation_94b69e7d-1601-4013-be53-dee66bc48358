### **产品需求文档 (PRD): OCR 全流程处理系统**

#### 1. 系统概述
本系统是一个Web服务，提供对图像的OCR全流程处理，包括文字定位、文字识别、版面分析、表格识别和公式识别，并通过后台接口调用和前端Web界面两种方式提供服务。

#### 2. 核心功能
系统后台处理流程包含五个核心功能节点，系统可根据请求按不同逻辑组合调用它们：
1.  **文字定位 (Text Detection)**
2.  **文字识别 (Text Recognition)**
3.  **版面分析 (Layout Analysis)**
4.  **表格识别 (Table Recognition)**
5.  **公式识别 (Formula Recognition)**

#### 3. 输入规范
- **数据来源:** 可通过程序化方式（如后台服务调用）或手动方式（通过Web界面）提供输入图像。
- **支持格式:** `JPG`, `PNG`, `BMP`。
- **限制:** 无文件大小限制。

#### 4. 处理逻辑与输出

##### 4.1. 逻辑概述
系统的核心能力是根据不同的结果请求，采用最优的节点组合路径进行处理。
- **对于Web界面发起的请求:** 系统总是执行最完整的处理路径，以生成包含所有信息的最终组合结果。
- **对于程序化调用:** 系统支持多种请求类型，每种类型对应一个独特的、最高效的处理路径，避免冗余计算。

##### 4.2. 请求类型与处理路径
不同的请求结果类型及其对应的内部处理逻辑路径如下：

| 请求结果类型 | 内部处理逻辑路径 | 描述 |
| :--- | :--- | :--- |
| **文字定位** | `1` | 仅执行文字定位。 |
| **文字识别** | `1 -> 2` | 执行文字定位，然后对定位结果执行文字识别。 |
| **版面分析** | `3` | 仅执行版面分析，识别页面分区，不解析分块内容。 |
| **表格识别** | `3 -> 4`，对每个表格区域执行`1 -> 2` | 先执行版面分析定位表格区域，然后对所有表格区域分别执行结构化识别。对于每个表格，将其裁剪出进行文字检测和文字识别，将识别出的文字填充进cell结果。若无表格区域直接返回 |
| **公式识别** | `3 -> 5` | 先执行版面分析定位公式区域，然后对版面分析的所有公式区域分别执行公式识别。若无公式区域直接返回 |
| **最终组合结果** | `(1->2) + (3) + (3->4) + (3->5)` | 执行全量流程：首先进行全局的文字识别；然后进行版面分析得到页面结构；最后分别对所有表格区域解析结构，文字识别的结果填充cell。对公式区域进行深度解析，并将所有结果填充进页面结构中。 |

##### 4.3. 标准输出数据结构
系统所有成功的请求都应产出符合以下标准结构的数据。对于非“最终组合结果”的请求，其产出是该标准结构的一个子集或部分填充的版本。bbox格式为[x1, y1, x2, y2...]

```json
{
  "request_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ok",
  "error_message": null,
  "results": {
    "text_recognition": [
      {
        "bbox": [10, 8, 810, 52],
        "words": "Project Phoenix Q2 Financial Report",
        "confidence": 0.98
      }
    ],
    "layout_analysis": [
      {
        "bbox": [10, 8, 810, 52],
        "type": "title",
        "confidence": 0.95,
        "content": [
          {
            "words": "Project Phoenix Q2 Financial Report",
            "confidence": 0.98
          }
        ]
      },
      {
        "bbox": [10, 60, 810, 550],
        "type": "table",
        "confidence": 0.92,
        "content": {
          "confidence": 0.91,
          "cells": [
            {
              "bbox": [10, 70, 105, 95], # 或者长度为8代表4个坐标
              "row_start": 0,
              "col_start": 0,
              "row_span": 1,
              "col_span": 1,
              "text": "demo text",
              "confidence": 0.99
            }
          ]
        }
      },
      {
        "bbox": [10, 560, 810, 680],
        "type": "formula",
        "confidence": 0.88,
        "content": [
          {
            "latex_string": "E = mc^2",
            "confidence": 0.93
          }
        ]
      },
      {
        "bbox": [850, 8, 1000, 158],
        "type": "image",
        "confidence": 0.99,
        "content": null
      }
    ]
  }
}
```
*(注：所有坐标`bbox`格式均为`[x1, y1, x2, y2]`，单位为像素，原点在左上角)*

##### 4.4. 部分请求的输出说明
- **请求“文字定位”或“文字识别”时:** `results`对象中仅包含`text_recognition`字段（仅定位时`words`字段可为空），`layout_analysis`字段为`null`或空数组。
- **请求“版面分析”时:** `results`对象中`text_recognition`字段可为空，`layout_analysis`数组将被填充，但其中所有元素的`content`字段均为`null`。
- **请求“表格识别”时:** `results`对象中`layout_analysis`数组将被填充，但仅`type`为`table`的元素的`content`字段有内容，其他类型（如`formula`）的`content`为`null`。

##### 4.5. 失败输出
若处理失败，输出结构将变为：
- `status` 字段为具体错误码，如 `err_img`, `err_internal` 等。
- `error_message` 字段提供人类可读的错误描述。
- `results` 字段为 `null`。

#### 5. Web 界面 (DEMO) 功能需求
- **文件上传:** 提供一个文件选择按钮，用于上传本地图像。
- **视图选择:** 提供一个下拉菜单，选项包括：
    - `文字定位`
    - `文字识别`
    - `版面分析`
    - `表格识别`
    - `公式识别`
    - `最终组合结果`
- **结果展示:** 界面需同时展示三个部分：**上传的原图**、**可视化结果**、**原始JSON数据**。
- **大图查看:** **点击原图/可视化结果图的任何位置**，放大查看该图。
- **原始JSON数据**：可以用鼠标一键点击复制json
- **可视化规则:**
    - **文字定位:** 以输入的原图为画布，在画布上绘制所有识别出的文本行边框。
    - **文字识别:** 以与原图size一致的白底图为画布，在画布上相应位置绘制识别出的文字。
    - **版面分析:** 以输入的原图为画布，在相应板块绘制不同颜色的透明蒙版，蒙版左上角用小字标注版面分析的类型。
    - **表格识别:** 以与原图size一致的白底图为画布，在画布上相应位置绘制表格边框，并在单元格内填充文字。
    - **公式识别:** 以与原图size一致的白底图为画布，在相应位置显示识别出的公式结果。
    - **最终组合结果:** 以与原图size一致的白底图为画布，在画布上相应位置绘制文字、公式、表格。在相应板块绘制不同颜色的透明蒙版表示版面分析结果，版面分析中的`image`、`unknown`等无内容区域则从原图中裁剪并贴到相应位置。