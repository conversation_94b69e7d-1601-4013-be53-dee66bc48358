# OCR文本定位独立模块(ocr_det) - 开发计划文档

## 目标代码目录结构

```
ocr_det/
├── __init__.py                 # 模块入口，导出OcrDet类
├── ocr_det.py                  # 统一实现文件，包含所有逻辑
├── config/
│   └── det_config.yaml         # 硬编码配置文件
├── models/
│   └── det_model.onnx          # ONNX检测模型
├── requirements.txt            # 依赖清单
└── test_ocr_det.py            # 测试文件
```

## 受影响的现有模块

### 复用的源代码模块
以下模块的代码将被复制到新的ocr_det模块中：

1. **modules/predictors/base.py**
   - 复用方法：`BaseOCR.__init__`, `_load_config`, `create_onnx_session`, `_determine_model_type`
   - 影响：无，仅复制代码逻辑

2. **modules/predictors/detector.py**
   - 复用方法：`TextDetector.__init__`, `init_model`, `detect`
   - 影响：无，仅复制代码逻辑

3. **modules/utils/det_pre_proc.py**
   - 复用类：`DBDetPreProc`及其所有方法
   - 影响：无，仅复制代码逻辑

4. **modules/utils/det_post_proc.py**
   - 复用类：`DBPostProcess`及其所有方法
   - 影响：无，仅复制代码逻辑

5. **configs/det/efvitsam_db_v0956_hr_v2_e39_onnx.yaml**
   - 复用：完整配置文件内容
   - 影响：无，仅复制配置

6. **assets/models_v2/onnx/det/efvitsam_db_v0956_hr_v2_e39.onnx**
   - 复用：ONNX模型文件
   - 影响：无，仅复制模型文件

### 不受影响的现有模块
- 所有现有模块保持不变，本开发为独立模块创建，不修改任何现有代码

## 渐进式小步迭代开发步骤

### 步骤1：创建基础目录结构和配置文件
**目标**：建立模块基础框架，确保目录结构正确
**可验证性**：目录结构创建完成，配置文件可正常加载

**开发内容**：
1. 创建 `ocr_det/` 目录
2. 创建 `ocr_det/__init__.py` 空文件
3. 创建 `ocr_det/config/` 目录
4. 复制配置文件：
   ```
   源文件：configs/det/efvitsam_db_v0956_hr_v2_e39_onnx.yaml
   目标：ocr_det/config/det_config.yaml
   ```
5. 创建 `ocr_det/models/` 目录
6. 复制模型文件：
   ```
   源文件：assets/models_v2/onnx/det/efvitsam_db_v0956_hr_v2_e39.onnx
   目标：ocr_det/models/det_model.onnx
   ```

**验证方法**：检查目录结构和文件存在性

### 步骤2：创建依赖清单文件
**目标**：分析并创建requirements.txt，确保依赖明确
**可验证性**：requirements.txt文件创建完成，包含所有必要依赖

**开发内容**：
1. 分析原始项目中文本检测相关的Python库依赖
2. 创建 `ocr_det/requirements.txt`，包含：
   ```
   onnxruntime
   numpy
   opencv-python
   PyYAML
   Pillow
   shapely
   pyclipper
   ```

**验证方法**：检查requirements.txt文件内容

### 步骤3：实现OcrDet类的基础框架
**目标**：创建OcrDet类的基本结构，实现初始化方法
**可验证性**：可以成功实例化OcrDet对象，配置加载正常

**开发内容**：
1. 创建 `ocr_det/ocr_det.py`
2. 实现基础类结构：
   ```python
   class OcrDet:
       def __init__(self):
           # 复制自BaseOCR.__init__和TextDetector.__init__
           self.config = None
           self.model_path = None
           self.model_type = None
           self.session = None
           self._load_config()
           self._init_model()
       
       def _load_config(self):
           # 复制自BaseOCR._load_config，硬编码配置路径
           pass
       
       def _determine_model_type(self, model_path):
           # 复制自BaseOCR._determine_model_type
           pass
       
       def _init_model(self):
           # 复制自TextDetector.init_model
           pass
       
       def _create_onnx_session(self, model_path):
           # 复制自BaseOCR.create_onnx_session
           pass
       
       def detect(self, image):
           # 占位方法，返回空列表
           return []
   ```

**验证方法**：
```python
from ocr_det import OcrDet
detector = OcrDet()  # 应该成功初始化
```

### 步骤4：实现配置加载和模型初始化逻辑
**目标**：完善初始化相关方法，确保配置和模型正确加载
**可验证性**：OcrDet对象初始化后，config和session属性正确设置

**开发内容**：
1. 完善 `_load_config` 方法：
   - 硬编码配置文件路径为 `./config/det_config.yaml`
   - 复制BaseOCR._load_config的YAML加载逻辑
   
2. 完善 `_determine_model_type` 方法：
   - 复制BaseOCR._determine_model_type的完整逻辑
   
3. 完善 `_create_onnx_session` 方法：
   - 复制BaseOCR.create_onnx_session的完整逻辑
   
4. 完善 `_init_model` 方法：
   - 复制TextDetector.init_model的完整逻辑
   - 调整模型路径为相对于模块的路径

**验证方法**：
```python
detector = OcrDet()
assert detector.config is not None
assert detector.session is not None
assert detector.model_type == 'onnx'
```

### 步骤5：实现图像预处理方法
**目标**：实现_preprocess方法，完成图像预处理功能
**可验证性**：输入测试图像，预处理方法返回正确格式的数据

**开发内容**：
1. 在OcrDet类中添加预处理相关方法：
   ```python
   def _preprocess(self, image):
       # 复制自DBDetPreProc.__call__
       pass
   
   def _normalize(self, image):
       # 复制自DBDetPreProc中的normalize方法
       pass
   
   def _resize_image(self, image):
       # 复制自DBDetPreProc中的resize方法
       pass
   ```

2. 复制DBDetPreProc类的完整逻辑到这些方法中
3. 调整方法调用关系，使其作为OcrDet的成员方法工作

**验证方法**：
```python
import numpy as np
detector = OcrDet()
test_image = np.zeros((100, 100, 3), dtype=np.uint8)
preprocessed = detector._preprocess(test_image)
assert preprocessed is not None
```

### 步骤6：实现ONNX推理方法
**目标**：实现_inference方法，完成模型推理功能
**可验证性**：预处理后的数据可以通过推理方法得到模型输出

**开发内容**：
1. 在OcrDet类中添加推理方法：
   ```python
   def _inference(self, input_data):
       # 复制自TextDetector.detect中的推理部分
       # 调用self.session.run进行ONNX推理
       pass
   ```

2. 从TextDetector.detect方法中提取推理逻辑
3. 确保输入输出格式与原始逻辑一致

**验证方法**：
```python
detector = OcrDet()
test_image = np.zeros((100, 100, 3), dtype=np.uint8)
preprocessed = detector._preprocess(test_image)
predictions = detector._inference(preprocessed)
assert predictions is not None
```

### 步骤7：实现后处理方法（第一部分）
**目标**：实现基础后处理方法，包括二值化和轮廓检测
**可验证性**：模型输出可以通过后处理得到二值化结果

**开发内容**：
1. 在OcrDet类中添加后处理基础方法：
   ```python
   def _postprocess(self, predictions, batch_data):
       # 复制自DBPostProcess.__call__
       pass
   
   def _get_pred_map(self, predictions):
       # 处理模型输出，获取预测图
       pass
   
   def _binarize(self, pred_map):
       # 二值化处理
       pass
   ```

2. 复制DBPostProcess.__call__方法的前半部分逻辑
3. 实现预测图处理和二值化功能

**验证方法**：
```python
# 使用模拟的预测数据测试二值化功能
```

### 步骤8：实现后处理方法（第二部分）
**目标**：实现轮廓检测和边界框提取方法
**可验证性**：二值化图像可以提取出文本边界框

**开发内容**：
1. 在OcrDet类中添加边界框提取方法：
   ```python
   def _boxes_from_bitmap(self, pred, bitmap, dest_width, dest_height):
       # 复制自DBPostProcess.boxes_from_bitmap
       pass
   
   def _get_mini_boxes(self, contour):
       # 复制自DBPostProcess.get_mini_boxes
       pass
   
   def _box_score_fast(self, pred, box):
       # 复制自DBPostProcess.box_score_fast
       pass
   
   def _unclip(self, box):
       # 复制自DBPostProcess.unclip
       pass
   ```

2. 复制DBPostProcess中所有相关方法的完整逻辑
3. 确保边界框格式符合需求规约

**验证方法**：
```python
# 使用包含文本的测试图像验证边界框提取
```

### 步骤9：完善detect主方法
**目标**：整合所有处理步骤，实现完整的detect方法
**可验证性**：输入图像可以得到正确格式的文本边界框列表

**开发内容**：
1. 完善detect方法：
   ```python
   def detect(self, image: numpy.ndarray) -> List[List[List[int]]]:
       # 输入验证
       if not isinstance(image, numpy.ndarray):
           # 按原始代码的错误处理方式
           pass
       
       # 预处理
       preprocessed_data = self._preprocess(image)
       
       # 推理
       predictions = self._inference(preprocessed_data)
       
       # 后处理
       boxes = self._postprocess(predictions, preprocessed_data)
       
       # 格式转换为List[List[List[int]]]
       return self._format_output(boxes)
   ```

2. 实现输出格式转换方法
3. 添加错误处理逻辑，与原始代码保持一致

**验证方法**：
```python
import cv2
detector = OcrDet()
test_image = cv2.imread('test_image.jpg')  # BGR格式
boxes = detector.detect(test_image)
assert isinstance(boxes, list)
# 验证输出格式符合List[List[List[int]]]
```

### 步骤10：完善模块入口和测试
**目标**：完善__init__.py文件，创建测试文件
**可验证性**：模块可以正常导入和使用，测试通过

**开发内容**：
1. 完善 `ocr_det/__init__.py`：
   ```python
   from .ocr_det import OcrDet
   
   __version__ = "1.0.0"
   __all__ = ["OcrDet"]
   ```

2. 创建 `ocr_det/test_ocr_det.py`：
   ```python
   import numpy as np
   from ocr_det import OcrDet
   
   def test_initialization():
       detector = OcrDet()
       assert detector is not None
   
   def test_detect_empty_image():
       detector = OcrDet()
       empty_image = np.zeros((100, 100, 3), dtype=np.uint8)
       result = detector.detect(empty_image)
       assert isinstance(result, list)
   
   def test_detect_invalid_input():
       detector = OcrDet()
       # 测试错误处理行为与原始代码一致
       pass
   
   if __name__ == "__main__":
       test_initialization()
       test_detect_empty_image()
       print("All tests passed!")
   ```

**验证方法**：
```bash
cd ocr_det
python test_ocr_det.py
```

### 步骤11：集成测试和行为验证
**目标**：验证模块行为与原始ocr_inference项目完全一致
**可验证性**：使用相同输入图像，新模块输出与原始项目输出一致

**开发内容**：
1. 创建对比测试脚本
2. 使用原始项目和新模块处理相同图像
3. 验证输出结果的一致性
4. 验证边界情况处理的一致性

**验证方法**：
```python
# 对比测试：原始项目 vs 新模块
# 确保输出完全一致
```

## 开发注意事项

### 代码复用原则
1. **严格复制**：所有核心逻辑必须从原始文件完整复制，不得修改算法逻辑
2. **最小变更**：仅进行必要的封装调整，如方法签名和调用关系
3. **行为一致**：确保所有场景下的行为与原始代码完全相同

### 验证标准
1. **功能验证**：每个步骤完成后必须可以运行和验证
2. **格式验证**：输出格式必须严格符合需求规约
3. **行为验证**：错误处理和边界情况必须与原始代码一致

### 文件大小控制
- `ocr_det.py` 预计约400-500行，符合单文件限制要求
- 通过合理的方法拆分保持代码可读性

---

**重要提醒**：每个步骤完成后必须进行验证，确保程序可运行且功能正常，然后再进行下一步开发。
