from pydantic import BaseModel
from uuid import UUID
from typing import List, Optional, Dict, Any


class OCRRequest(BaseModel):
    """OCR请求模型，包含图像数据和文件名

    支持两种图像输入方式：
    1. image_base64: Base64编码的图像数据
    2. image_url: 图像的URL地址

    注意：image_url优先级高于image_base64，至少需要提供其中一种
    """
    image_base64: Optional[str] = None
    image_url: Optional[str] = None
    filename: str


class BaseResponse(BaseModel):
    """所有OCR响应的基类，包含请求ID"""
    request_id: UUID
    status: str = "ok"
    error_message: Optional[str] = None


# --- 新的结果项模型，匹配 readme_srv_api.md 的格式 ---

class TextContent(BaseModel):
    """文本内容模型，包含识别的文本值和置信度"""
    value: str
    confidence: float


class TextDetectionItem(BaseModel):
    """文本检测结果项"""
    bbox: List[float]
    confidence: float


class TextRecognitionItem(BaseModel):
    """单个文本识别结果项"""
    bbox: List[float]
    confidence: float  # 来自文本检测的置信度
    text: TextContent  # 嵌套的文本内容，包含识别结果和置信度


class LayoutAnalysisItem(BaseModel):
    """版面分析项模型"""
    bbox: List[float]
    type: str
    confidence: float
    # text 字段在全量接口中用于文本类型的区域
    text: Optional[TextContent] = None
    # cells 字段在全量接口中用于表格类型的区域
    cells: Optional[List['TableCell']] = None


class TableCell(BaseModel):
    """表格单元格模型"""
    bbox: List[float]
    confidence: float  # 来自表格识别的置信度
    row_start: int
    col_start: int
    row_span: int
    col_span: int
    text: TextContent  # 嵌套的文本内容，包含识别结果和置信度


class TableRecognitionItem(BaseModel):
    """表格识别结果项"""
    bbox: List[float]
    confidence: float # 来自表格识别或者版面分析
    cells: List[TableCell]  # 直接包含cells数组，去掉content包装


class FormulaRecognitionItem(BaseModel):
    """公式识别结果项"""
    bbox: List[float]
    confidence: float  # 来自版面分析的置信度
    text: TextContent  # 嵌套的文本内容，包含公式识别结果和置信度


# --- 统一响应模型 ---
# 所有API都使用 FullOCRResponse 作为响应模型

class FullOCRResponse(BaseResponse):
    """统一的OCR结果响应模型，所有API都使用此模型"""
    results: Dict[str, Any]

# 更新前向引用
LayoutAnalysisItem.model_rebuild()