from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.core.config import settings
from app.core import errors
from app.api.endpoints import results, ocr, images, visualizations, web_ocr, health
from app.middleware.ip_restriction import ip_restriction_middleware
from app.services.initialization_manager import get_initialization_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    # 启动时执行
    logger.info("=== AgileOCR 服务启动中 ===")

    manager = None
    try:
        # 初始化所有算法服务
        logger.info("开始初始化所有OCR算法服务...")
        manager = get_initialization_manager()
        success = await manager.initialize_all_services(timeout=300.0)  # 5分钟超时

        if not success:
            logger.error("=== 部分OCR算法服务初始化失败 ===")
            raise RuntimeError("算法服务初始化失败")

        # 双重检查：确保所有服务真正就绪
        if not manager.is_ready():
            logger.error("=== 初始化完成但服务状态检查失败 ===")
            # 打印详细状态信息
            status = manager.get_initialization_status()
            logger.error(f"服务状态详情: {status}")
            raise RuntimeError("服务状态检查失败，服务未完全就绪")

        logger.info("=== 所有OCR算法服务初始化完成，服务已就绪 ===")
        logger.info("=== FastAPI 应用启动完成，可以接受请求 ===")

    except KeyboardInterrupt:
        logger.warning("=== 用户中断了服务初始化，应用将退出 ===")
        if manager:
            manager.set_interrupted()
        raise  # 重新抛出，让FastAPI处理

    except Exception as e:
        logger.error(f"服务启动过程中发生异常: {str(e)}", exc_info=True)
        if manager:
            manager.set_interrupted()
        raise

    yield  # 应用运行期间

    # 关闭时执行
    logger.info("=== AgileOCR 服务正在关闭 ===")
    try:
        # 清理初始化管理器资源
        if manager:
            manager.cleanup()
    except Exception as e:
        logger.error(f"清理资源时发生错误: {str(e)}", exc_info=True)

app = FastAPI(lifespan=lifespan)

# 添加IP限制中间件
app.middleware("http")(ip_restriction_middleware)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，实际上是根据请求的Origin头部判断
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有头部
)


app.include_router(
    ocr.router,
    prefix=f"{settings.API_V1_STR}/ocr",
    tags=["ocr"]
)

# 注册API路由器
app.include_router(
    results.router,
    prefix=f"{settings.API_V1_STR}/web/results",
    tags=["results"]
)

app.include_router(
    images.router,
    prefix=f"{settings.API_V1_STR}/web/images",
    tags=["images"]
)

app.include_router(
    visualizations.router,
    prefix=f"{settings.API_V1_STR}/web/visualizations",
    tags=["visualizations"]
)

# 注册前端专用API路由（带IP限制）
app.include_router(
    web_ocr.router,
    prefix=f"{settings.API_V1_STR}/web/ocr",
    tags=["web-ocr"]
)

# 注册健康检查路由
app.include_router(
    health.router,
    prefix=f"{settings.API_V1_STR}",
    tags=["health"]
)

@app.get("/health")
async def health_check():
    """健康检查端点，检查服务是否完全就绪"""
    try:
        manager = get_initialization_manager()
        if manager.is_ready():
            return {
                "status": errors.ErrCode.OK,
                "message": "服务就绪",
                "ready": True
            }
        else:
            # 返回详细的初始化状态
            status = manager.get_initialization_status()
            return {
                "status": "initializing",
                "message": "服务正在初始化中",
                "ready": False,
                "details": status
            }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "ready": False
        }


# 注册异常处理器
@app.exception_handler(errors.ClientErr)
async def client_error_handler(request: Request, exc: errors.ClientErr):
    logger.error(f"客户端预定义异常: {exc}")
    return JSONResponse(
        status_code=400,
        content={
            "request_id": "",
            "status": exc.code,
            "error_message": exc.msg,
            "results": None
        }
    )

@app.exception_handler(errors.ServerError)
async def server_error_handler(request: Request, exc: errors.ServerError):
    logger.error(f"服务端预定义异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "request_id": "",
            "status": exc.code,
            "error_message": exc.msg,
            "results": None
        }
    )

@app.exception_handler(HTTPException)
async def raise_http_exception(request: Request, exc: HTTPException):
    logger.error(f"HTTPException: {exc}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "request_id": "",
            "status": "http_exception",
            "error_message": exc.detail,
            "results": None
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理所有未捕获的异常"""
    logger.error(f"未捕获的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "request_id": "",
            "status": errors.ErrCode.SRV_INTERNAL,
            "error_message": "服务器内部错误",
            "results": None
        }
    )
