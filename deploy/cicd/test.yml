# 测试环境

k8s_build_test:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - docker pull $ALI_TEST_CONTAINER_IMAGE:latest || true
    - >-
      docker build -f Dockerfile \
        --cache-from $ALI_TEST_CONTAINER_IMAGE:latest \
        --build-arg CI_COMMIT_TAG=$CI_COMMIT_TAG \
        --tag $ALI_TEST_CONTAINER_IMAGE:$CI_COMMIT_TAG \
        --tag $ALI_TEST_CONTAINER_IMAGE:latest .
    - docker push $ALI_TEST_CONTAINER_IMAGE:$CI_COMMIT_TAG
    - docker push $ALI_TEST_CONTAINER_IMAGE:latest
  stage: build
  tags:
    - k8s
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^v.*-test$/

volc_k8s_test_ingress:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - >-
      deploy_helper -yaml=./deploy/k8s/volc_test/ingress.yaml \
        -cls=$VOLC_DEPLOY_CLS \
        -kind=deploy_ingress
  stage: deploy
  tags:
    - k8s
  dependencies:
    - k8s_build_test
  when: manual
  only:
    variables:
      - $CI_COMMIT_TAG =~ /^v.*-update-test$/


volc_k8s_test_deploy:
  image: "k8s-reg.cmcm.com/k8s/deploy_helper:latest"
  script:
    - >-
      deploy_helper -yaml=./deploy/k8s/volc_test/api.yaml \
        -kind=deploy_service \
        -cls=$VOLC_DEPLOY_CLS \
        modelzooapi-test.tag=$CI_COMMIT_TAG \
        modelzooapi-test.image=$ALI_TEST_CONTAINER_IMAGE
  stage: deploy
  tags:
    - k8s
  dependencies:
    - k8s_build_test
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-update-test$/
      when: manual  # 当增量部署时采用人工点击触发
    - if: $CI_COMMIT_TAG =~ /^v.*-test$/
      when: manual  # 先改为手动部署, always -> manual