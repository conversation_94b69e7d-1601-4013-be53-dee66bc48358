Global:
    checkpoints: "./models/det_model.onnx"
    device_type: "cuda"
    device_id: 0
    intra_op_num_threads: 8
    inter_op_num_threads: 1

# 前处理配置
PreProcess:
    img_mode: "BGR"
    limit_side_len: 1280  # 图像边长限制
    limit_image_lone_size: 2048 # 第一步数据预处理
    limit_type: "max"     # 限制类型，可选:"min"或"max"
    image_shape: null     # 指定图像形状，为null时根据limit_side_len自动计算
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

# 后处理配置
PostProcess:
    thresh: 0.30            # 二值化阈值
    box_thresh: 0.50        # 文本框置信度阈值
    unclip_ratio: 1.50      # 文本框扩展比例
    max_candidates: 1000    # 最大候选框数量
    min_text_size: 1        # 最小文本尺寸
    min_text_size_delta: 4  # 最小文本尺寸增量
    use_dilation: false     # 是否使用膨胀操作
    box_type: "quad"        # 框类型，可选:"quad"
    score_mode: "fast"      # 评分模式，可选:"fast"或"slow"

