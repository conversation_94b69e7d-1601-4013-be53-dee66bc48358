import unittest
from unittest import mock
import uuid
import tempfile
from pathlib import Path
from PIL import Image
import json
import io

from app.services.visualization_service import VisualizationService


class TestVisualizationService(unittest.TestCase):
    
    def test_initialization(self):
        """测试服务初始化是否正确创建目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 验证目录是否被创建
            self.assertTrue(Path(temp_dir).exists())
    
    def test_get_existing_visualization(self):
        """测试获取已存在的可视化结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 创建一个测试文件
            request_id = uuid.uuid4()
            view_type = "text_detection"
            test_file = Path(temp_dir) / f"{request_id}_{view_type}.png"
            
            # 创建一个空图像文件
            Image.new('RGB', (100, 100)).save(test_file)
            
            # 调用方法
            result = service.get_or_create_visualization(request_id, view_type)
            
            # 验证结果
            self.assertEqual(result, test_file)
            # 验证mock方法未被调用
            mock_persistence.get_result.assert_not_called()
            mock_image.get_image_path.assert_not_called()
    
    def test_create_new_visualization(self):
        """测试创建新的可视化结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'text_recognition': [
                        {'bbox': [10, 20, 110, 50], 'confidence': 0.95, 'words': 'Test'}
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service.get_or_create_visualization(request_id, "text_detection")
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
            
            # 验证mock方法被正确调用
            mock_persistence.get_result.assert_called_once_with(request_id)
            mock_image.get_image_path.assert_called_once_with(request_id)
    
    def test_generate_text_detection_visualization(self):
        """测试文本检测可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'text_recognition': [
                        {'bbox': [10, 20, 110, 50], 'confidence': 0.95, 'words': 'Test'}
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "text_detection", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_generate_text_recognition_visualization(self):
        """测试文本识别可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'text_recognition': [
                        {'bbox': [10, 20, 110, 50], 'confidence': 0.95, 'words': 'Test Text'}
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "text_recognition", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸 - 应该与原图相同
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_generate_layout_visualization(self):
        """测试版面分析可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'layout_analysis': [
                        {'bbox': [10, 20, 110, 50], 'type': 'text', 'confidence': 0.95},
                        {'bbox': [120, 20, 190, 80], 'type': 'table', 'confidence': 0.90}
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "layout_analysis", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_generate_table_visualization(self):
        """测试表格识别可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'layout_analysis': [
                        {
                            'bbox': [10, 20, 190, 80], 
                            'type': 'table', 
                            'confidence': 0.90,
                            'content': {
                                'cells': [
                                    {
                                        'bbox': [20, 30, 80, 50],
                                        'text': 'Header',
                                        'row_start': 0,
                                        'col_start': 0,
                                        'row_span': 1,
                                        'col_span': 1,
                                        'confidence': 0.95
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "table_recognition", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_generate_formula_visualization(self):
        """测试公式识别可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'layout_analysis': [
                        {
                            'bbox': [10, 20, 190, 50], 
                            'type': 'formula', 
                            'confidence': 0.90,
                            'content': [
                                {
                                    'latex_string': 'E=mc^2',
                                    'confidence': 0.95
                                }
                            ]
                        }
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "formula_recognition", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_generate_full_visualization(self):
        """测试完整OCR结果可视化生成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {
                'results': {
                    'text_recognition': [
                        {'bbox': [10, 20, 110, 50], 'confidence': 0.95, 'words': 'Test Text'},
                        {'bbox': [120, 60, 190, 90], 'confidence': 0.92, 'words': 'More Text'}
                    ],
                    'layout_analysis': [
                        {'bbox': [5, 15, 115, 55], 'type': 'text', 'confidence': 0.95},
                        {
                            'bbox': [120, 20, 190, 50], 
                            'type': 'formula', 
                            'confidence': 0.90,
                            'content': [
                                {
                                    'latex_string': 'E=mc^2',
                                    'confidence': 0.95
                                }
                            ]
                        },
                        {
                            'bbox': [120, 60, 190, 90], 
                            'type': 'image', 
                            'confidence': 0.85
                        }
                    ]
                }
            }
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100), color=(255, 200, 150))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "full_result", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_unknown_visualization_type(self):
        """测试未知的可视化类型"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {'results': {}}
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "unknown_type", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸 - 应该与原图相同
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))
    
    def test_missing_ocr_result(self):
        """测试OCR结果缺失的情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建mock对象
            mock_persistence = mock.Mock()
            mock_image = mock.Mock()
            
            # 配置mock行为
            request_id = uuid.uuid4()
            mock_persistence.get_result.return_value = {}  # 空结果
            
            # 创建测试图像
            test_image = Image.new('RGB', (200, 100))
            test_image_path = Path(temp_dir) / "test_image.png"
            test_image.save(test_image_path)
            
            mock_image.get_image_path.return_value = test_image_path
            
            # 创建服务实例
            service = VisualizationService(temp_dir, mock_persistence, mock_image)
            
            # 调用方法
            result = service._generate_visualization(
                request_id, "text_detection", mock_persistence.get_result.return_value, test_image_path
            )
            
            # 验证结果
            self.assertTrue(result.exists())
            # 验证图像尺寸
            with Image.open(result) as img:
                self.assertEqual(img.size, (200, 100))


if __name__ == "__main__":
    unittest.main() 