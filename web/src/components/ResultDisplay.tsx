import React from 'react';
import { Row, Col, Card } from 'antd';
import { OriginalImageViewer } from './OriginalImageViewer';
import { VisualizationViewer } from './VisualizationViewer';
import { JsonViewer } from './JsonViewer';
import { OCRResponse, ViewType } from '../types/ocr';

interface ResultDisplayProps {
  result: OCRResponse;
  viewType: ViewType;
}

export const ResultDisplay: React.FC<ResultDisplayProps> = ({ result, viewType }) => {
  return (
    <div>
      {/* 顶部两栏：原图 + 可视化结果 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} md={12}>
          <Card 
            title="原图" 
            size="small"
            style={{ height: '500px' }}
            bodyStyle={{ 
              height: 'calc(100% - 57px)', 
              padding: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <OriginalImageViewer requestId={result.request_id} />
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card 
            title="可视化结果" 
            size="small"
            style={{ height: '500px' }}
            bodyStyle={{ 
              height: 'calc(100% - 57px)', 
              padding: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <VisualizationViewer requestId={result.request_id} viewType={viewType} />
          </Card>
        </Col>
      </Row>

      {/* 底部：JSON 数据 */}
      <Row>
        <Col span={24}>
          <Card 
            title="原始JSON数据" 
            size="small"
            bodyStyle={{ padding: '16px' }}
          >
            <JsonViewer data={result} />
          </Card>
        </Col>
      </Row>
    </div>
  );
}; 