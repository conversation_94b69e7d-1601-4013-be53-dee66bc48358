# 公式识别批量处理需求文档 (PRD)

## 1. 需求背景

当前公式识别服务采用单边框处理模式，每次只能处理一个公式边框，导致GPU计算资源利用率不足。在实际应用中，版面分析算法会识别出多个公式边框，需要逐个调用公式识别服务，无法充分发挥GPU的批处理能力。

## 2. 核心目标

将公式识别服务从单边框处理模式改为批量边框处理模式，提高GPU计算效率和整体处理性能。

## 3. 现状分析

### 3.1 当前数据流向
```
版面分析算法 → 筛选公式边框 → 循环调用公式识别服务
```

### 3.2 当前接口
- **方法**: `FormulaRecognitionService.recognize()`
- **输入**: `image_bytes: bytes, formula_bbox: List[int]`
- **输出**: `Dict[str, Any]`
- **处理**: 单个边框的公式识别

## 4. 目标需求

### 4.1 目标数据流向
```
版面分析算法 → 筛选公式边框 → 一次性传入公式识别服务
```

### 4.2 目标接口
- **方法**: `FormulaRecognitionService.recognize()`
- **输入**: `image_bytes: bytes, formula_bboxes: List[List[int]]`
- **输出**: `List[Dict[str, Any]]`
- **处理**: 批量边框的公式识别

### 4.3 批处理策略
- 当输入边框数量超过batch_size时，内部进行批量划分，分多个批次顺序处理
- batch_size作为服务初始化参数，默认值为4
- 返回结果以列表形式，与输入边框保持一一对应的顺序

## 5. 功能要求

### 5.1 接口改动
- 修改`recognize`方法的输入参数：从单个边框改为边框列表
- 修改`recognize`方法的返回结果：从单个结果改为结果列表
- 保持结果与输入边框的顺序一致性

### 5.2 处理逻辑
- 内部根据batch_size进行分批处理
- 所有批次处理完成后，合并结果返回
- 充分利用GPU的批处理能力

### 5.3 影响范围
- 仅修改`recognize`方法的调用接口
- 不涉及服务初始化的改动
- 所有当前循环调用`recognize`方法的代码需要改为批量调用模式

## 6. 非功能要求

### 6.1 性能要求
- 提高GPU利用率
- 减少单次处理的总耗时
- 优化批量公式识别的处理效率

### 6.2 兼容性要求
- 不需要保持向后兼容
- 直接改为批量处理模式

## 7. 约束条件

### 7.1 当前阶段不考虑的问题
- 具体的batch_size实现细节
- 边界情况的错误处理
- GPU内存管理策略
- 动态batch_size调整

### 7.2 实现约束
- 暂时不考虑错误处理的复杂逻辑
- 专注于核心批处理功能的实现

## 8. 验收标准

### 8.1 功能验收
- [ ] `recognize`方法能够接收多个边框列表作为输入
- [ ] 返回结果为列表格式，与输入边框一一对应
- [ ] 内部能够按batch_size进行分批处理
- [ ] 所有调用方代码成功改为批量调用模式

### 8.2 性能验收
- [ ] GPU利用率相比单边框处理模式有明显提升
- [ ] 批量处理的总耗时优于逐个处理的累计耗时

## 9. 风险评估

### 9.1 技术风险
- 批处理逻辑的正确性
- 结果顺序的一致性保证

### 9.2 影响范围风险
- 需要同步修改所有调用方代码
- 可能影响现有的业务流程

---

**文档版本**: v1.0  
**创建日期**: 2025-06-30  
**最后更新**: 2025-06-30
