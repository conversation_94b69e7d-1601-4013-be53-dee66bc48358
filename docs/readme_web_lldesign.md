### **前端详细设计 (Low-Level Design): OCR 全流程处理系统**

### 1. 项目结构与总体设计 (Project Structure & Overall Design)

#### 1.1 目录结构树 (Directory Tree)

前端代码将完全包含在 `web/` 目录中，与现有后端代码并存于同一代码库。

```
ocr_system/
├── app/                     # (现有后端代码)
├── results/                 # (现有后端代码)
├── web/                # 前端代码根目录
│   ├── public/              # Vite public assets
│   ├── src/                 # 前端源码
│   │   ├── api/             # API客户端层
│   │   │   └── ocrApi.ts
│   │   ├── assets/          # 静态资源
│   │   ├── components/      # 可复用的React组件
│   │   │   ├── ImageUploader.tsx        # 图片上传组件
│   │   │   ├── ResultDisplay.tsx        # 结果展示区总布局
│   │   │   ├── OriginalImageViewer.tsx  # [新增]原图获取与展示组件
│   │   │   ├── VisualizationViewer.tsx    # 可视化结果图获取与展示组件
│   │   │   └── JsonViewer.tsx           # JSON数据显示组件
│   │   ├── hooks/           # 自定义Hooks
│   │   │   └── useOcrAnalysis.ts    # 封装Tanstack Query的API调用逻辑
│   │   ├── pages/           # 页面级组件
│   │   │   └── HomePage.tsx
│   │   ├── types/           # TypeScript类型定义
│   │   │   └── ocr.ts
│   │   └── main.tsx         # 应用入口
│   ├── .env.development     # 开发环境变量
│   ├── index.html           # HTML入口文件
│   ├── package.json
│   ├── pnpm-lock.yaml
│   ├── tsconfig.json
│   └── vite.config.ts
└── pyproject.toml           # (现有后端代码)
```

#### 1.2 界面布局设计 (UI Layout Design)

我们将采用**“两栏-单行”仪表盘式 (Dashboard) 布局**。

-   **布局示意图**:
    ```
    +--------------------------------------------------------------------------+
    |  OCR 全流程处理系统 DEMO                                        [Header] |
    +--------------------------------------------------------------------------+
    |  [PageHeader: Upload Button & View Selector Dropdown]                    |
    +--------------------------------------------------------------------------+
    |                        [Content Area]                                    |
    |                                                                          |
    |  +--------------------------------+  +--------------------------------+  |
    |  |             原图               |  |           可视化结果           |  |
    |  |   (点击可在新标签页打开)       |  |   (点击可在新标签页打开)       |  |
    |  +--------------------------------+  +--------------------------------+  |
    |                                                                          |
    |  +--------------------------------------------------------------------+  |
    |  |                           原始JSON数据                             |  |
    |  +--------------------------------------------------------------------+  |
    |                                                                          |
    +--------------------------------------------------------------------------+
    ```
-   **设计 rationale**:
    -   **主次分明**: 顶部并排展示最重要的视觉对比信息（原图 vs 结果图），符合用户直觉。
    -   **可读性最佳**: 为JSON数据提供完整的行宽，避免内容被挤压，便于查阅。
    -   **易于实现**: 可通过 Ant Design 的 `<Row>`, `<Col>`, `<Card>` 组件快速搭建。
    -   **响应式友好**: 在小屏幕上，顶部的两栏可以自然地垂直堆叠，保持布局的清晰性。
-   **界面状态管理**:
    -   **初始状态**: 内容区域使用 `<Empty />` 组件提示用户上传。
    -   **加载状态**: 内容区域使用 `<Spin />` 组件提供即时反馈。
    -   **成功状态**: 内容区域渲染 `ResultDisplay` 组件，展示三部分内容。
    -   **错误状态**: 页面顶部使用 `<Alert type="error" />` 组件显示错误信息。

#### 1.3 整体逻辑和交互时序图 (Overall Logic & Sequence Diagram)

工作流程基于方案B：上传图片时调用一次 `full_result` 接口获取完整数据，之后仅在切换视图时按需请求对应的可视化图。

-   **Mermaid `sequenceDiagram`**:
    ```mermaid
    sequenceDiagram
        participant User as 用户
        participant HomePage as pages/HomePage.tsx
        participant useOcrAnalysis as hooks/useOcrAnalysis.ts
        participant ocrApi as api/ocrApi.ts
        participant Backend as 后端API

        User->>+HomePage: 选择图片文件
        HomePage->>+useOcrAnalysis: 调用 analysisMutation.mutate(file)
        useOcrAnalysis->>+ocrApi: uploadImage(file)
        ocrApi->>+Backend: POST /api/v1/ocr/full_result
        Backend-->>-ocrApi: 返回 { request_id, results, ... } (JSON)
        ocrApi-->>-useOcrAnalysis: 返回 JSON 数据
        useOcrAnalysis-->>-HomePage: 更新 Tanstack Query 状态 (data, isSuccess)
        
        Note over HomePage: 页面根据状态，渲染ResultDisplay组件
        
        participant ResultDisplay as components/ResultDisplay.tsx
        participant VisViewer as components/VisualizationViewer.tsx

        HomePage->>+ResultDisplay: 传入 request_id 和 完整的JSON数据
        ResultDisplay->>+VisViewer: 传入 request_id 和 view_type
        
        VisViewer->>+ocrApi: getVisualizationImage(request_id, view_type)
        ocrApi->>+Backend: GET /api/v1/visualizations/{request_id}?view_type=...
        Backend-->>-ocrApi: 返回可视化图片数据
        ocrApi-->>-VisViewer: 返回图片Blob URL
        VisViewer-->>-ResultDisplay: 显示图片，并支持点击放大
    ```

### 2. 配置项 (Configuration)

| 环境变量              | 文件 (`web/.env.development`) | 描述                           | 示例值                            |
| :-------------------- | :--------------------------------- | :----------------------------- | :-------------------------------- |
| `VITE_API_BASE_URL` | `VITE_API_BASE_URL`                | 后端API服务器的基础URL。 | `http://127.0.0.1:6000/api/v1` |

---

### 3. 模块化文件详解 (File-by-File Breakdown)

#### `web/src/types/ocr.ts`

a. **文件用途说明**: 定义与后端API数据结构完全匹配的TypeScript类型，确保类型安全。
b. **文件内容概览**:
```typescript
// 顶层响应结构
export interface OCRResponse {
  request_id: string;
  status: 'ok' | 'error';
  error_message: string | null;
  results: Results | null;
}

// Results对象
export interface Results {
  text_recognition: TextRecognitionItem[];
  layout_analysis: LayoutAnalysisItem[];
}

// 文本识别条目
export interface TextRecognitionItem {
  bbox: [number, number, number, number];
  words: string;
  confidence: number;
}

// 版面分析条目
export interface LayoutAnalysisItem {
  bbox: [number, number, number, number];
  type: 'title' | 'table' | 'formula' | 'image' | 'text' | 'unknown';
  confidence: number;
  content: Content | null;
}

// Content联合类型
export type Content = TextContent[] | TableContent | FormulaContent[];

export interface TextContent {
    words: string;
    confidence: number;
}

// 表格内容
export interface TableContent {
  confidence: number;
  cells: TableCell[];
}

export interface TableCell {
  bbox: [number, number, number, number];
  row_start: number;
  col_start: number;
  row_span: number;
  col_span: number;
  text: string;
  confidence: number;
}

// 公式内容
export interface FormulaContent {
  latex_string: string;
  confidence: number;
}
```

#### `web/src/api/ocrApi.ts`

a. **文件用途说明**: 封装所有与后端交互的API调用。
b. **函数/方法详解**:
   - **`uploadImage(file: File): Promise<OCRResponse>`**:
     - **输入**: 用户上传的`File`对象。
     - **输出**: 一个解析为`OCRResponse`的Promise。
     - **实现**: 创建一个`FormData`实例，将文件附加进去。使用`fetch`或`axios`向`${VITE_API_BASE_URL}/ocr/full_result`发送POST请求。对响应进行JSON解析和错误处理。
   - **`getOriginalImage(requestId: string): Promise<string>`**:
     - **输入**: `requestId`字符串。
     - **输出**: 一个解析为图像Blob URL的Promise。
     - **实现**: 向`${VITE_API_BASE_URL}/images/${requestId}`发送GET请求。将响应体（`response.blob()`）转换为Blob对象，然后通过`URL.createObjectURL()`创建可用的URL字符串。
   - **`getVisualizationImage(requestId: string, viewType: string): Promise<string>`**:
     - **输入**: `requestId`和`viewType`字符串。
     - **输出**: 一个解析为图像Blob URL的Promise。
     - **实现**: 向`${VITE_API_BASE_URL}/visualizations/${requestId}?view_type=${viewType}`发送GET请求。后续处理同`getOriginalImage`。

#### `web/src/hooks/useOcrAnalysis.ts`

a. **文件用途说明**: 使用`tanstack-query`封装图片上传的异步逻辑和状态管理。
b. **函数/方法详解**:
   - **`useOcrAnalysis()`**:
     - **输出**: `useMutation` hook的返回对象，包含`mutate`, `data`, `isPending`, `isError`, `error`。
     - **实现**: 调用`useMutation`，其`mutationFn`属性被设置为`api.uploadImage`。配置`onSuccess`和`onError`回调来处理通知等副作用。

#### `web/src/pages/HomePage.tsx`

a. **文件用途说明**: 应用主页面，协调所有子组件和状态。
b. **函数/方法详解**:
   - **状态管理**:
     - `const analysisMutation = useOcrAnalysis();`
     - `const [viewType, setViewType] = useState('full_result');`
   - **组件结构**:
     - 使用`<Layout>`搭建整体页面。
     - 使用`<PageHeader>`承载标题、面包屑以及操作区。
     - 操作区放置`<ImageUploader />`和`<Select />`（视图选择器）。
   - **逻辑实现**:
     - 将`analysisMutation.mutate`传递给`ImageUploader`的`onUpload` prop。
     - `<Select>`组件的`disabled`属性绑定到`!analysisMutation.data`。其`onChange`事件调用`setViewType`。
     - 主内容区根据`analysisMutation`的状态（`isPending`, `isError`, `data`）条件渲染`<Spin>`, `<Alert>`或`<ResultDisplay>`。
     - 当`<ResultDisplay>`被渲染时，传入`result={analysisMutation.data}`和`viewType={viewType}`。

#### `web/src/components/ImageUploader.tsx`

a. **文件用途说明**: 封装 Ant Design 的上传组件。
b. **函数/方法详解**:
   - **Props**: `onUpload: (file: File) => void; isLoading: boolean;`
   - **实现**:
     - 使用`<Upload.Dragger>`。
     - `disabled={props.isLoading}`。
     - `beforeUpload={(file) => { props.onUpload(file); return false; }}`，返回`false`来阻止组件的默认上传行为。

#### `web/src/components/ResultDisplay.tsx`

a. **文件用途说明**: 实现仪表盘布局的核心容器。
b. **函数/方法详解**:
   - **Props**: `result: OCRResponse; viewType: string;`
   - **实现**:
     - `<Row gutter={16}>`
       - `<Col span={12}><Card title="原图"><OriginalImageViewer requestId={props.result.request_id} /></Card></Col>`
       - `<Col span={12}><Card title="可视化结果"><VisualizationViewer requestId={props.result.request_id} viewType={props.viewType} /></Card></Col>`
     - `</Row>`
     - `<Row style={{ marginTop: 16 }}>`
       - `<Col span={24}><Card title="原始JSON数据"><JsonViewer data={props.result} /></Card></Col>`
     - `</Row>`

#### `web/src/components/OriginalImageViewer.tsx`

a. **文件用途说明**: 专用于获取、展示和放大**原图**。
b. **函数/方法详解**:
   - **Props**: `requestId: string;`
   - **实现**:
     - 使用`useQuery`：`const { data, isPending, isError } = useQuery({ queryKey: ['originalImage', props.requestId], queryFn: () => api.getOriginalImage(props.requestId) });`
     - 条件渲染：`isPending`时显示`<Spin/>`，`isError`时显示`<Alert/>`。
     - 成功时渲染：`<a href={data} target="_blank" rel="noopener noreferrer"><img src={data} style={{ maxWidth: '100%' }} alt="Original Upload" /></a>`。这实现了点击在新标签页打开大图的功能。

#### `web/src/components/VisualizationViewer.tsx`

a. **文件用途说明**: 专用于获取、展示和放大**可视化结果图**。
b. **函数/方法详解**:
   - **Props**: `requestId: string; viewType: string;`
   - **实现**:
     - 使用`useQuery`：`const { data, isPending, isError } = useQuery({ queryKey: ['visualization', props.requestId, props.viewType], queryFn: () => api.getVisualizationImage(props.requestId, props.viewType) });`
     - **关键**: `queryKey`包含了`viewType`，因此当`viewType` prop改变时，`tanstack-query`会自动触发数据重新获取。
     - 渲染逻辑与`OriginalImageViewer`完全相同，包括加载、错误处理和最终带链接的图片展示。

#### `web/src/components/JsonViewer.tsx`

a. **文件用途说明**: 格式化并展示JSON对象。
b. **函数/方法详解**:
   - **Props**: `data: object;`
   - **实现**:
     - 使用一个带有样式的`div`容器，设置其`maxHeight`和`overflow: 'auto'`以实现内部滚动。
     - 内部使用`<pre><code>{JSON.stringify(props.data, null, 2)}</code></pre>`来格式化和显示JSON。