import logging
from typing import List
from app.core.config import settings

import os
import cv2
import time
import yaml
import torch
import numpy as np
import onnx
import onnxruntime as ort
from typing import Dict, List, Tuple
import pyclipper
from shapely.geometry import Polygon

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[34m{time.asctime() + "  " + __name__}\033[0m')


class TextDetectionService:
    """文本检测服务，负责在图像中定位文本区域"""

    def __init__(self):
        """
        Initializes the TextDetector instance.
        """
        logger.info("  初始化文本定位服务...")
        self.config_path = os.path.abspath(settings.TEXT_DET_CONFIG)
        
        # Load configuration from the specified YAML file
        self.config = self._load_config(self.config_path)

        # Initialize parameters for preprocessing and postprocessing
        self.txt_det_model_path = settings.TEXT_DET_WEIGHTS

        # 初始化模型
        self._init_model()

        # 初始化预处理/后处理参数
        self._init_process_param()

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"  配置文件不存在: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.load(f, Loader=yaml.Loader)
        return config

    def _init_model(self):
        # 设备配置
        self.device_type = self.config['Global'].get("device_type").lower() if torch.cuda.is_available() else "cpu"
        self.device_id = self.config['Global'].get("device_id")
        self.intra_op_num_threads = self.config['Global'].get("intra_op_num_threads", 8)
        self.inter_op_num_threads = self.config['Global'].get("inter_op_num_threads", 1)

        """初始化检测模型"""
        if not self.txt_det_model_path or not os.path.exists(self.txt_det_model_path):
            raise ValueError(f"模型路径未指定或不存在: {self.txt_det_model_path}")

        # 创建ONNX运行时会话 - PRD要求仅支持ONNX
        self.onnx_session = self._create_onnx_session(self.txt_det_model_path)

        # 获取输入名称和形状
        self.input_name = self.onnx_session.get_inputs()[0].name
        self.output_names = [output.name for output in self.onnx_session.get_outputs()]

    def _init_process_param(self):
        # 预处理参数
        preprocess_config = self.config.get("PreProcess", {})
        self.limit_side_len = preprocess_config.get("limit_side_len", 640)
        self.limit_image_lone_size = preprocess_config.get("limit_image_lone_size", 2048)
        self.limit_type = preprocess_config.get("limit_type", "max")
        self.image_shape = preprocess_config.get("image_shape", None)
        self.mean = preprocess_config.get("mean", [0.485, 0.456, 0.406])
        self.std = preprocess_config.get("std", [0.229, 0.224, 0.225])

        # 后处理参数
        postprocess_config = self.config.get("PostProcess", {})
        self.thresh = postprocess_config.get("thresh", 0.3)
        self.box_thresh = postprocess_config.get("box_thresh", 0.7)
        self.max_candidates = postprocess_config.get("max_candidates", 1000)
        self.unclip_ratio = postprocess_config.get("unclip_ratio", 2.0)
        self.use_dilation = postprocess_config.get("use_dilation", False)
        self.score_mode = postprocess_config.get("score_mode", "fast")
        self.box_type = postprocess_config.get("box_type", "quad")
        self.min_text_size = postprocess_config.get("min_text_size", 2)
        self.min_text_size_delta = postprocess_config.get("min_text_size_delta", 2)

        # 膨胀核设置
        assert self.score_mode in ['slow', 'fast'], f'Score mode must be in [slow, fast] but got: {self.score_mode}'
        self.dilation_kernel = None if not self.use_dilation else np.array([[1, 1], [1, 1]])

    def _create_onnx_session(self, model_path: str) -> ort.InferenceSession:
        """创建ONNX运行时会话"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        # 检测模型精度
        try:
            # 加载模型获取精度信息
            model = onnx.load(model_path, load_external_data=False)

            # 检查模型的精度类型
            is_fp16_model = False
            for tensor in model.graph.initializer:
                if tensor.data_type == 10:  # FLOAT16 = 10 in ONNX
                    is_fp16_model = True
                    break

            # 记录模型精度信息
            self.model_precision = "fp16" if is_fp16_model else "fp32"
            logger.info(f"  检测到模型精度: {self.model_precision.upper()}, 使用{self.model_precision.upper()}初始化模型")
        except Exception as e:
            # 如果检测失败，默认使用FP32
            logger.info(f"  模型精度检测失败，默认使用FP32初始化模型: {str(e)}")
            self.model_precision = "fp32"

        # 创建会话选项
        sess_options = ort.SessionOptions()
        sess_options.intra_op_num_threads = self.intra_op_num_threads
        sess_options.inter_op_num_threads = self.inter_op_num_threads
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

        return ort.InferenceSession(
            model_path,
            sess_options=sess_options,
            providers=self._get_ort_providers()
        )

    def _get_ort_providers(self) -> List[str]:
        """获取ONNX Runtime提供程序列表"""
        providers = ['CPUExecutionProvider']
        if self.device_type in ['gpu', 'cuda']:
            provider_options = [{'device_id': self.device_id}]
            providers = [('CUDAExecutionProvider', provider_options[0])] + providers
        return providers

    def _preprocess(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """图像预处理"""
        # 调整图像尺寸
        img, shape = self._resize_image(image)

        # 标准化图像
        img = self._normalize_image(img)

        # 转换为CHW格式
        img = self._to_chw_image(img)

        return img, shape

    def _resize_image(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        # 模块外输入处理，这里塞入预处理中
        h, w = img.shape[:2]
        max_side = max(h, w)
        # 如果最长边超过限制，缩放图像
        self.ratio = 1.0
        if max_side > self.limit_image_lone_size:
            self.ratio = self.limit_image_lone_size * 1.0 / max_side
            new_w = int(w * self.ratio)
            new_h = int(h * self.ratio)
            img = cv2.resize(img, (new_w, new_h))

        """调整图像尺寸"""
        src_h, src_w = img.shape[:2]

        if self.image_shape is not None:
            # 使用固定的图像形状
            resize_h, resize_w = self.image_shape
        else:
            # 根据短边限制调整尺寸
            if self.limit_type == 'max':
                if max(src_h, src_w) > self.limit_side_len:
                    if src_h > src_w:
                        resize_h = self.limit_side_len
                        resize_w = int(src_w * self.limit_side_len / src_h)
                    else:
                        resize_w = self.limit_side_len
                        resize_h = int(src_h * self.limit_side_len / src_w)
                else:
                    resize_h = src_h
                    resize_w = src_w
            else:
                if min(src_h, src_w) < self.limit_side_len:
                    if src_h < src_w:
                        resize_h = self.limit_side_len
                        resize_w = int(src_w * self.limit_side_len / src_h)
                    else:
                        resize_w = self.limit_side_len
                        resize_h = int(src_h * self.limit_side_len / src_w)
                else:
                    resize_h = src_h
                    resize_w = src_w

        # 确保高度和宽度都是32的倍数
        resize_h = ((resize_h + 31) // 32) * 32
        resize_w = ((resize_w + 31) // 32) * 32

        # 调整图像尺寸
        if resize_h != src_h or resize_w != src_w:
            img = cv2.resize(img, (resize_w, resize_h))

        # 计算缩放比例
        ratio_h = resize_h / src_h
        ratio_w = resize_w / src_w

        # 返回调整后的图像和形状信息
        return img, np.array([src_h, src_w, ratio_h, ratio_w])

    def _normalize_image(self, img: np.ndarray) -> np.ndarray:
        """标准化图像"""
        # 转换为浮点型
        img = img.astype(np.float32)

        # 缩放到[0,1]
        scale = 1.0 / 255.0
        img *= scale

        # 标准化，使用默认的均值和标准差
        mean = np.array(self.mean or [0.485, 0.456, 0.406], dtype=np.float32).reshape((1, 1, 3))
        std = np.array(self.std or [0.229, 0.224, 0.225], dtype=np.float32).reshape((1, 1, 3))
        img -= mean
        img /= std

        return img

    def _to_chw_image(self, img: np.ndarray) -> np.ndarray:
        """转换图像格式从HWC到CHW"""
        if len(img.shape) == 2:
            img = np.expand_dims(img, axis=2)
        return img.transpose((2, 0, 1))

    def _inference(self, img_chw: np.ndarray) -> np.ndarray:
        """ONNX模型推理"""
        img = np.expand_dims(img_chw, axis=0)

        # ONNX模型推理
        if self.onnx_session is not None:
            input_data = self._prepare_input_tensor(img)
            preds = self.onnx_session.run(self.output_names, {self.input_name: input_data})[0]
            preds = self._ensure_fp32_output(preds)
        else:
            raise RuntimeError("  TEXT DETECTOR ONNX model not properly initialized")

        return preds

    def _prepare_input_tensor(self, input_data: np.ndarray) -> np.ndarray:
        """准备输入张量"""
        if hasattr(self, 'model_precision') and self.model_precision == "fp16":
            return input_data.astype(np.float16)
        return input_data.astype(np.float32)

    def _ensure_fp32_output(self, output_data: np.ndarray) -> np.ndarray:
        """确保FP32输出格式"""
        if output_data is None:
            return None

        # If it's numpy array and not float32 type, convert to float32
        if isinstance(output_data, np.ndarray) and output_data.dtype != np.float32:
            return output_data.astype(np.float32)
        return output_data

    def _postprocess(self, predictions: np.ndarray, shape_list: List) -> List[Dict]:
        """后处理模型预测结果"""
        pred = predictions[:, 0, :, :]
        segmentation = self._binarize(pred)

        # Process each batch item
        boxes_batch = []

        for batch_index in range(pred.shape[0]):
            src_h, src_w, ratio_h, ratio_w = shape_list[batch_index]

            # Apply dilation if needed - copied from DBPostProcess.__call__:285-291
            if hasattr(self, 'dilation_kernel') and self.dilation_kernel is not None:
                mask = cv2.dilate(
                    np.array(segmentation[batch_index]).astype(np.uint8),
                    self.dilation_kernel
                )
            else:
                mask = segmentation[batch_index]

            # Extract boxes based on box_type - copied from DBPostProcess.__call__:300-315
            if self.box_type == 'quad':
                boxes, scores = self._boxes_from_bitmap(pred[batch_index], mask, src_w, src_h)
            else:
                raise ValueError("box_type can only be 'quad' in this implementation")

            boxes_batch.append({'points': boxes, 'scores': scores})

        points = boxes_batch[0]["points"] if boxes_batch else np.array([])
        scores = boxes_batch[0]["scores"] if boxes_batch else np.array([])
        return points, scores

    def _binarize(self, pred_map: np.ndarray) -> np.ndarray:
        """二值化预测图"""
        segmentation = pred_map > self.thresh
        return segmentation

    def _boxes_from_bitmap(self, pred: np.ndarray, bitmap: np.ndarray, dest_width: int, dest_height: int) -> Tuple[np.ndarray, List[float]]:
        """从二值图提取文本框"""
        height, width = bitmap.shape

        # Find contours - copied from DBPostProcess.boxes_from_bitmap:119-123
        outs = cv2.findContours((bitmap * 255).astype(np.uint8), cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if len(outs) == 3:
            img, contours, _ = outs[0], outs[1], outs[2]
        elif len(outs) == 2:
            contours, _ = outs[0], outs[1]

        num_contours = min(len(contours), self.max_candidates)

        boxes = []
        scores = []

        # Process each contour - copied from DBPostProcess.boxes_from_bitmap:132-172
        for index in range(num_contours):
            contour = contours[index]
            points, sside = self._get_mini_boxes(contour)
            if sside < self.min_text_size:
                continue
            points = np.array(points)
            if self.score_mode == 'fast':
                score = self._box_score_fast(pred, points.reshape(-1, 2))
            else:
                # Note: box_score_slow not implemented in this version
                score = self._box_score_fast(pred, points.reshape(-1, 2))

            if score < self.box_thresh:
                continue

            box = self._unclip(points, self.unclip_ratio)
            if len(box) > 1:
                continue
            box = np.array(box).reshape(-1, 1, 2)
            box, sside = self._get_mini_boxes(box)
            if sside < self.min_text_size + self.min_text_size_delta:
                continue
            box = np.array(box)

            # 缩放文本框坐标到目标尺寸
            box[:, 0] = np.clip(np.round(box[:, 0] / width * dest_width), 0, dest_width)
            box[:, 1] = np.clip(np.round(box[:, 1] / height * dest_height), 0, dest_height)
            box = box * 1.0 / self.ratio
            boxes.append(box.astype('int32'))
            scores.append(score)

        return np.array(boxes, dtype='int32'), scores

    def _get_mini_boxes(self, contour: np.ndarray) -> Tuple[List[List[float]], float]:
        """获取最小外接矩形"""
        # 确保轮廓格式正确
        if not isinstance(contour, np.ndarray):
            contour = np.array(contour)

        # OpenCV要求轮廓格式为(n, 1, 2)
        if len(contour.shape) == 2 and contour.shape[1] == 2:
            contour = contour.reshape(-1, 1, 2).astype(np.float32)
        elif len(contour.shape) == 3 and contour.shape[1] == 1 and contour.shape[2] == 2:
            if contour.dtype != np.float32 and contour.dtype != np.int32:
                contour = contour.astype(np.float32)

        # 验证轮廓有效性，至少需要3个点
        if contour.size == 0 or contour.shape[0] < 3:
            return [[0, 0], [0, 0], [0, 0], [0, 0]], 0

        bounding_box = cv2.minAreaRect(contour)
        points = sorted(list(cv2.boxPoints(bounding_box)), key=lambda x: x[0])

        index_1, index_2, index_3, index_4 = 0, 1, 2, 3
        if points[1][1] > points[0][1]:
            index_1 = 0
            index_4 = 1
        else:
            index_1 = 1
            index_4 = 0
        if points[3][1] > points[2][1]:
            index_2 = 2
            index_3 = 3
        else:
            index_2 = 3
            index_3 = 2

        box = [points[index_1], points[index_2], points[index_3], points[index_4]]
        return box, min(bounding_box[1])

    def _box_score_fast(self, bitmap: np.ndarray, box: np.ndarray) -> float:
        """计算文本框得分"""
        h, w = bitmap.shape[:2]
        box = box.copy()
        xmin = np.clip(np.floor(box[:, 0].min()).astype('int32'), 0, w - 1)
        xmax = np.clip(np.ceil(box[:, 0].max()).astype('int32'), 0, w - 1)
        ymin = np.clip(np.floor(box[:, 1].min()).astype('int32'), 0, h - 1)
        ymax = np.clip(np.ceil(box[:, 1].max()).astype('int32'), 0, h - 1)

        mask = np.zeros((ymax - ymin + 1, xmax - xmin + 1), dtype=np.uint8)
        box[:, 0] = box[:, 0] - xmin
        box[:, 1] = box[:, 1] - ymin
        cv2.fillPoly(mask, box.reshape(1, -1, 2).astype('int32'), 1)
        return cv2.mean(bitmap[ymin:ymax + 1, xmin:xmax + 1], mask)[0]

    def _unclip(self, box: np.ndarray, unclip_ratio: float) -> List:
        """扩展文本框"""
        poly = Polygon(box)
        distance = poly.area * unclip_ratio / poly.length
        offset = pyclipper.PyclipperOffset()
        offset.AddPath(box, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
        expanded = offset.Execute(distance)
        return expanded

    def _format_output(self, points: np.ndarray) -> List[List[List[int]]]:
        """格式化输出结果"""
        if len(points) == 0:
            return []

        # 转换为整数并转为列表格式
        if isinstance(points, np.ndarray):
            formatted_points = points.astype(int).tolist()
            return formatted_points

        return []


    def detect(self, image: np.ndarray) -> List[List[int]]:
        """检测图像中的文本区域

        Args:
            image: BGR格式的图像数组 (H, W, C)

        Returns:
            List[List[int]]: 文本边界框列表，每个边界框为 [[x1, y1], [x2, y2], [x4, y4], [x3, y3]]
        """

        start_time = time.time()

        if self.onnx_session is None:
            logger.error("ONNX session is not initialized.")
            raise RuntimeError("Detector is not properly initialized.")

        # 直接使用传入的numpy数组，无需重复解码
        image, shape = self._preprocess(image)

        preds = self._inference(image)

        shape_list = np.expand_dims(shape, axis=0)          # 对齐preds的batch维度
        points, scores = self._postprocess(preds, shape_list)
        # Format output to List[List[List[int]]]
        boxes =  self._format_output(points)

        logger.info(f"  本次文本定位调用耗时为:   {(time.time() - start_time) * 1000} ms")

        return boxes, scores
