import logging
import time
from typing import List, Dict, Any

import numpy as np
from app.processing.texteller.texteller_engine import TextellerEngine
from app.core.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(f'\033[32m{time.asctime() + "  " + __name__}\033[0m')

class FormulaRecognitionService:
    """公式识别服务，负责识别数学公式"""
    def __init__(self):
        logger.info("  初始化公式识别服务...")

        # Initialize Texteller engine with configuration from settings
        self.texteller_engine = TextellerEngine(
            model_path=settings.TEXTELLER_MODEL_PATH,
            use_onnx=settings.TEXTELLER_USE_ONNX,
            repo_name=settings.TEXTELLER_REPO_NAME
        )

        self.batch_size = settings.TEXTELLER_BATCH_SIZE  # Use configured batch size
        logger.info("  初始化公式识别服务完成")
    
    def recognize(self, image: np.ndarray, formula_bboxes: List[List[int]]) -> List[Dict[str, Any]]:
        """批量识别图像中多个指定区域的数学公式

        Args:
            image: BGR格式的图像数组 (H, W, C)
            formula_bboxes: 公式边界框列表，每个边框格式为 [x_min, y_min, x_max, y_max]

        Returns:
            List[Dict[str, Any]]: 公式识别结果列表，每个结果包含LaTeX字符串和置信度
        """
        start_time = time.time()

        # Handle empty input
        if not formula_bboxes:
            logger.info("  未提供公式边界框，返回空列表")
            return []

        # Use Texteller engine for recognition
        # The engine handles batching internally, so we can pass all bboxes at once
        all_results = self.texteller_engine.recognize(image, formula_bboxes)

        processing_time = (time.time() - start_time) * 1000
        total_bboxes = len(formula_bboxes)
        logger.info(f"  本次公式识别 batch_inference 总共处理 {total_bboxes} 个公式， 总耗时为 {processing_time:.2f} ms")

        return all_results
