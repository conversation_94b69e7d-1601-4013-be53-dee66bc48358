# 公式识别服务调用链分析

## 调用链

### 节点1: formula_recognition (API端点)

**所在代码文件相对路径**: `app/api/endpoints/ocr.py`  
**用途**: 处理公式识别的HTTP POST请求，作为公式识别服务的入口点  
**输入参数**:

- `request: OCRRequest` - 包含Base64编码图像的请求对象
- `service: OrchestrationService` - 通过依赖注入获得的编排服务实例

**输出说明**: 返回`FullOCRResponse`对象，包含公式识别结果

### 节点2: OrchestrationService.process_formula_recognition

**所在代码文件相对路径**: `app/services/orchestration.py`  
**用途**: 编排公式识别的完整处理流程，协调版面分析和公式识别服务  
**输入参数**:

- `image_base64: str` - Base64编码的图像字符串

**输出说明**: 返回`FullOCRResponse`对象，包含所有识别到的公式结果列表

### 节点3: LayoutAnalysisService.analyze

**所在代码文件相对路径**: `app/processing/layout_analysis.py`  
**用途**: 分析图像版面结构，识别出公式区域的边界框  
**输入参数**:

- `image_bytes: bytes` - 图像的二进制数据

**输出说明**: 返回版面分析结果列表，每个项目包含类型和边界框信息

### 节点4: FormulaRecognitionService.recognize (循环调用)

**所在代码文件相对路径**: `app/processing/formula_recognition.py`  
**用途**: 识别单个边界框内的数学公式，转换为LaTeX格式  
**输入参数**:

- `image_bytes: bytes` - 图像的二进制数据
- `formula_bbox: List[int]` - 公式边界框坐标 [x_min, y_min, x_max, y_max]

**输出说明**: 返回字典，包含`latex_string`和`confidence`字段

## 额外发现的调用点

### 节点5: OrchestrationService.process_full_result (另一个调用点)

**所在代码文件相对路径**: `app/services/orchestration.py`  
**用途**: 处理全量OCR请求，在版面分析结果中对每个公式区域进行识别  
**调用位置**: 第415行  
**调用方式**: 在处理版面分析结果时，对类型为"formula"的区域调用公式识别服务  
**输入参数**:

- `image_bytes: bytes` - 图像的二进制数据
- `item["bbox"]` - 版面分析结果中的公式边界框

**输出说明**: 返回字典，包含`latex_string`和`confidence`字段

### 节点6: 前端调用链

**前端入口**: `web/src/pages/HomePage.tsx`  
**API调用**: `web/src/api/ocrApi.ts` 中的 `uploadImage` 方法  
**调用端点**: `/api/v1/ocr/full_result` (POST)  
**说明**: 前端只调用全量OCR接口，不直接调用公式识别接口，但支持选择显示公式识别结果

## 整体用途

公式识别服务调用链的整体用途是：

1. 接收包含数学公式的图像（Base64编码）
2. 通过版面分析算法识别出图像中所有的公式区域
3. 对每个公式区域进行单独的公式识别处理
4. 将所有公式识别结果汇总并返回给客户端

当前实现采用**循环调用**模式：版面分析识别出N个公式边框后，逐个调用公式识别服务进行处理，这种方式未能充分利用GPU的批处理能力。

## 目录结构

调用链涉及到的文件及其所属的目录结构：

```text
app/
├── main.py                           # FastAPI应用入口
├── api/
│   └── endpoints/
│       └── ocr.py                     # OCR相关API端点
├── services/
│   ├── orchestration.py              # 编排服务
│   └── injector.py                    # 依赖注入服务
└── processing/
    ├── layout_analysis.py             # 版面分析服务
    └── formula_recognition.py         # 公式识别服务
```

## 调用时序图

### 时序图1: 单独公式识别接口

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as app/api/endpoints/ocr.py
    participant Orch as app/services/orchestration.py
    participant Layout as app/processing/layout_analysis.py
    participant Formula as app/processing/formula_recognition.py

    Client->>API: POST /api/v1/ocr/formula_recognition
    Note over Client,API: 请求参数: {image_base64: "base64_string"}
    
    API->>Orch: process_formula_recognition(image_base64)
    Note over API,Orch: 传递Base64图像字符串
    
    Orch->>Layout: analyze(image_bytes)
    Note over Orch,Layout: 传递图像二进制数据
    Layout-->>Orch: 返回版面分析结果
    Note over Layout,Orch: 返回: [{"type": "formula", "bbox": [x1,y1,x2,y2]}, ...]
    
    loop 对每个公式边框
        Orch->>Formula: recognize(image_bytes, formula_bbox)
        Note over Orch,Formula: 传递图像数据和单个边框坐标
        Formula-->>Orch: 返回公式识别结果
        Note over Formula,Orch: 返回: {"latex_string": "...", "confidence": 0.95}
    end
    
    Orch-->>API: 返回FullOCRResponse
    Note over Orch,API: 返回: {"request_id": "...", "results": {"formula_recognition": [...]}}
    
    API-->>Client: 返回JSON响应
    Note over API,Client: HTTP 200 + 公式识别结果
```

### 时序图2: 全量OCR接口中的公式识别调用

```mermaid
sequenceDiagram
    participant WebClient as 前端客户端
    participant API as app/api/endpoints/ocr.py
    participant Orch as app/services/orchestration.py
    participant Layout as app/processing/layout_analysis.py
    participant Formula as app/processing/formula_recognition.py
    participant TextDet as app/processing/text_detection.py
    participant TextRec as app/processing/text_recognition.py

    WebClient->>API: POST /api/v1/ocr/full_result
    Note over WebClient,API: 请求参数: {image_base64: "base64_string"}
    
    API->>Orch: process_full_result(image_base64)
    Note over API,Orch: 传递Base64图像字符串
    
    Orch->>TextDet: detect(image_bytes)
    TextDet-->>Orch: 返回文本边界框
    
    Orch->>TextRec: recognize(image_bytes, bboxes)
    TextRec-->>Orch: 返回文本识别结果
    
    Orch->>Layout: analyze(image_bytes)
    Layout-->>Orch: 返回版面分析结果
    Note over Layout,Orch: 返回: [{"type": "formula", "bbox": [...]}, {"type": "table", "bbox": [...]}, ...]
    
    loop 对每个版面区域
        alt 如果是公式区域
            Orch->>Formula: recognize(image_bytes, item["bbox"])
            Note over Orch,Formula: 传递图像数据和公式边框
            Formula-->>Orch: 返回公式识别结果
            Note over Formula,Orch: 返回: {"latex_string": "...", "confidence": 0.95}
        else 其他类型区域
            Note over Orch: 处理表格、文本等其他类型
        end
    end
    
    Orch-->>API: 返回FullOCRResponse
    Note over Orch,API: 返回: {"request_id": "...", "results": {"layout_analysis": [...]}}
    
    API-->>WebClient: 返回JSON响应
    Note over API,WebClient: HTTP 200 + 完整OCR结果
```

## 影响范围总结

根据调用链分析，公式识别服务的批量改造将影响以下文件和方法：

### 直接影响的文件

1. **`app/processing/formula_recognition.py`**
   - 需要修改 `FormulaRecognitionService.recognize` 方法
   - 将输入参数从单个边框改为多个边框列表
   - 将输出结果从单个结果改为结果列表

2. **`app/services/orchestration.py`**
   - 需要修改 `OrchestrationService.process_formula_recognition` 方法（第322行）
   - 需要修改 `OrchestrationService.process_full_result` 方法（第415行）
   - 将循环调用改为批量调用

### 不需要修改的文件

1. **`app/api/endpoints/ocr.py`** - API端点不需要修改
2. **`app/services/injector.py`** - 依赖注入不需要修改
3. **`app/processing/layout_analysis.py`** - 版面分析服务不需要修改
4. **前端代码** - 所有前端代码都不需要修改

### 批量改造要点

1. **数据流向改变**：
   - 原流向：版面分析 → 循环调用公式识别
   - 新流向：版面分析 → 批量调用公式识别

2. **接口参数改变**：
   - 原参数：`recognize(image_bytes, formula_bbox)`
   - 新参数：`recognize(image_bytes, formula_bboxes)`

3. **返回结果改变**：
   - 原返回：`{"latex_string": "...", "confidence": 0.95}`
   - 新返回：`[{"latex_string": "...", "confidence": 0.95}, ...]`

4. **批处理策略**：
   - 在 `FormulaRecognitionService.recognize` 内部按 batch_size 分批处理
   - 保证返回结果顺序与输入边框顺序一致

### 测试影响

- **`app/services/test_orchestration.py`** 中的所有测试用例都需要更新
- 需要更新 mock 对象的返回值格式
