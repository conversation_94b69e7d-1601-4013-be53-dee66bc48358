from pathlib import Path
import os
import argparse

from PIL import Image
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks

from app.utils.visual_table import visual_cells
from app.utils import table_logic


if __name__ == "__main__":
    table_recognition = pipeline(Tasks.table_recognition, model='iic/cv_dla34_table-structure-recognition_cycle-centernet')

    default_output = Path(__file__).parent.parent.parent / "output/images/"
    args = argparse.ArgumentParser()
    args.add_argument("--input", type="str", help="input dir of images")
    args.add_argument("--output", type="str", default=str(default_output), help="output dir of images")


    output = Path(args.output)
    input = Path(args.input)
    images = os.listdir(args.input)
    images.sort()
    for img_name in images:
        img_path = str(input/img_name)
        result = table_recognition(img_path)
        result = result['polygons'].tolist()
        print("\n========================" + img_name)
        print(result)
        cells = table_logic.process_table_structure(result)
        table_logic.sort_cells(cells)
        img = Image.open(img_path)
        table_img = Image.new('RGBA', img.size, color="white")
        visual_cells(cells, table_img)
        table_img.save(str(output/f"out_{img_name}"), format="PNG")
