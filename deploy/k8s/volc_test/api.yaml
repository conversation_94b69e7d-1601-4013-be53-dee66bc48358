# Token 找对应小组长申请

token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoibW9kZWx6b29hcGktdGVzdCIsIm5hbWVzcGFjZSI6ImthbnBpYyIsImtpbmQiOiJTZXJ2aWNlIiwiVmVyc2lvbiI6InYxIiwiaXNzIjoiazhzLWFwaS1saXRlIn0.Zhhx-EE340CipM5up4N_8DqKMuksWrgWijHppOVndKQ"
name: modelzooapi-test
namespace: kanpic
desc: "agileocr的测试环境"

labels:
  APPTYPE: modelzooapi-test  # 标记具体的进程类型

podScheduling:
  nodeSelector:
    kanpic_group: l20
#  podAntiAffinity:
#    preferredDuringSchedulingIgnoredDuringExecution:
#      - weight: 100
#        podAffinityTerm:
#          labelSelector:
#            matchLabels:
#              APPTYPE: comfyui
#          topologyKey: kubernetes.io/hostname
  tolerations:
    - key: "node_type"
      operator: "Equal"
      value: "GPU_DIGHTAL_TRAIN"
      effect: "NoSchedule"
    - key: "node_type"
      operator: "Equal"
      value: "GPU_DIGHTAL_TRAIN"
      effect: "NoExecute"

volumes:
  - name: aidrawp-share
    persistentVolumeClaim:
      claimName: aidrawp-share

replicas: 1

ports:
  - name: http
    port: 6000

deployment_strategy:
   rolling_update:
     max_unavailable: 1
     max_surge: 1
   type: RollingUpdate

containers:
  - name: modelzooapi-test
    env:
      - name: CGPU_DISABLE
        value: false
      - name: HOSTIP
        valueFrom:
          fieldRef:
            fieldPath: status.podIP
      - name: TORCH_CUDA_ARCH_LIST
        value: "8.6"
      - name: BACKEND_HOST
        value: "localhost"
      - name: BACKEND_PORT
        value: "6000"
      - name: FRONTEND_PORT
        value: "5000"
      - name: INDEX_URL
        value: https://mirrors.aliyun.com/pypi/simple/
      - name: prometheus_service_name
        value: agileocr_test
    requests:
      cpu: 1
      memory: 8Gi
    limits:
      cpu: 22
      memory: 90Gi
      extra:
        nvidia.com/gpu: 1
    livenessProbe:
      httpGet:
        path: /api/v1/health
        port: 6000
      initialDelaySeconds: 60
      timeoutSeconds: 15
      periodSeconds: 10
      failureThreshold: 5
    readinessProbe:
      httpGet:
        path: /api/v1/ready
        port: 6000
      initialDelaySeconds: 30
      timeoutSeconds: 10
      periodSeconds: 5
      failureThreshold: 3
    volumeMounts:
      - mountPath: "/appdata"
        name: aidrawp-share