#!/usr/bin/env python3
"""
Simplified LaTeX postprocessor for texteller output.
Provides basic LaTeX formatting and cleanup functionality.
"""

import re
from typing import List

from .latex_utils import remove_style, add_newlines


def format_latex(text: str, keep_style: bool = False) -> str:
    """
    Format LaTeX text with basic formatting options.

    This is the main API function for formatting LaTeX text.
    It provides basic cleanup and formatting for texteller output.

    Args:
        text: LaTeX text to format
        keep_style: Whether to keep style commands like \textbf, \mathbf, etc.

    Returns:
        Formatted LaTeX text
    """
    if not text or not isinstance(text, str):
        return ""
    
    # Basic cleanup
    formatted_text = clean_text(text)
    
    # Remove style commands if requested
    if not keep_style:
        formatted_text = remove_style(formatted_text)
    
    # Add appropriate newlines
    formatted_text = add_newlines(formatted_text)
    
    return formatted_text.strip()


def clean_text(text: str) -> str:
    """
    Clean LaTeX text by removing extra whitespace and normalizing formatting.
    
    Args:
        text: Raw LaTeX text
        
    Returns:
        Cleaned LaTeX text
    """
    if not text:
        return ""
    
    # Remove trailing whitespace from lines
    text = re.sub(r' +\n', '\n', text)
    
    # Normalize multiple consecutive newlines to single newlines
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    # Basic LaTeX cleanup
    text = normalize_spacing(text)
    
    return text


def normalize_spacing(text: str) -> str:
    """
    Normalize spacing in LaTeX text.
    
    Args:
        text: LaTeX text
        
    Returns:
        Text with normalized spacing
    """
    # Normalize spaces around common LaTeX commands
    text = re.sub(r'\s*\\begin\s*\{', r'\\begin{', text)
    text = re.sub(r'\s*\\end\s*\{', r'\\end{', text)
    
    # Normalize spaces around math delimiters
    text = re.sub(r'\s*\$\s*', '$', text)
    text = re.sub(r'\s*\\\[\s*', r'\\[', text)
    text = re.sub(r'\s*\\\]\s*', r'\\]', text)
    
    # Normalize spaces around braces
    text = re.sub(r'\s*\{\s*', '{', text)
    text = re.sub(r'\s*\}\s*', '}', text)
    
    # Remove extra spaces
    text = re.sub(r'  +', ' ', text)
    
    return text


def postprocess_batch_results(raw_results: List[str], keep_style: bool = False) -> List[str]:
    """
    Postprocess a batch of raw LaTeX results from the model.
    
    Args:
        raw_results: List of raw LaTeX strings from model output
        keep_style: Whether to keep style commands
        
    Returns:
        List of formatted LaTeX strings
    """
    processed_results = []
    
    for raw_text in raw_results:
        try:
            formatted_text = format_latex(raw_text, keep_style=keep_style)
            processed_results.append(formatted_text)
        except Exception as e:
            # If formatting fails, return the original text
            processed_results.append(raw_text.strip() if raw_text else "")
    
    return processed_results


def extract_confidence_from_generation(generated_ids, tokenizer, model_output=None) -> List[float]:
    """
    Extract confidence scores from model generation.
    
    This is a placeholder implementation that returns 1.0 for all results.
    In a more sophisticated implementation, this could analyze the model's
    output probabilities to compute actual confidence scores.
    
    Args:
        generated_ids: Generated token IDs from the model
        tokenizer: Tokenizer used for decoding
        model_output: Optional model output containing logits/probabilities
        
    Returns:
        List of confidence scores (currently always 1.0)
    """
    # For now, return 1.0 confidence for all results
    # This could be enhanced to compute actual confidence from model outputs
    if hasattr(generated_ids, 'shape'):
        batch_size = generated_ids.shape[0]
    else:
        batch_size = len(generated_ids)
    
    return [1.0] * batch_size
