import logging
import numpy as np
import cv2
from typing import List, Dict, Any

from app.processing.unitable.table_structure.table_structure_unitable import TableStructureUnitable
from app.processing.unitable.table_matcher.matcher import TableMatch
from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService
from app.processing.table_recognition import TableRecognitionService
from app.utils.table_logic import sort_cells

logger = logging.getLogger(__name__)

class UnitableTableRecognition(TableRecognitionService):
    """表格识别服务，负责识别表格结构和内容"""
    
    def __init__(self, config: Dict, text_recognition: TextRecognitionService, text_detection: TextDetectionService = None):
        """初始化表格识别服务
        
        Args:
            config: 配置字典，包含模型路径和设备信息
            text_recognition: 文本识别服务实例
            text_detection: 文本检测服务实例（可选）
        """
        super().__init__(config, text_recognition, text_detection)
        
        # 创建Unitable配置字典并适配为TableStructureUnitable期望的格式
        unitable_config = self._adapt_config(config)
        
        # 初始化TableStructureUnitable实例
        self.unitable = TableStructureUnitable(unitable_config)
        
        # 初始化TableMatch实例
        self.matcher = TableMatch(filter_ocr_result=True)
        
        logger.info("表格识别服务初始化完成")
    
    def _adapt_config(self, flat_config: Dict) -> Dict:
        """将扁平结构的配置转换为TableStructureUnitable期望的嵌套结构
        
        Args:
            flat_config: 扁平结构的配置字典
            
        Returns:
            Dict: 转换后的嵌套结构配置字典
        """
        return {
            "model_path": {
                "encoder": flat_config.get("UNITABLE_ENCODER_WEIGHTS"),
                "decoder": flat_config.get("UNITABLE_DECODER_WEIGHTS"),
                "vocab": flat_config.get("UNITABLE_VOCAB_PATH"),
            },
            "use_cuda": flat_config.get("USE_CUDA", False)
        }
    
    def recognize_table(self, image_bytes: bytes, table_bbox: List[int] = None, text_results=None) -> Dict[str, Any]:
        """识别图像中的表格结构和内容
        
        Args:
            image_bytes: 图像的二进制数据
            table_bbox: 可选的表格边界框 [x_min, y_min, x_max, y_max]
            
        Returns:
            Dict[str, Any]: 表格识别结果，包含单元格信息
        """
        logger.info(f"开始识别表格，边界框: {table_bbox if table_bbox else '整图'}")
        
        # 将图像二进制数据转换为numpy数组
        image = self._prepare_image(image_bytes)
        original_height, original_width = image.shape[:2]
        
        # 如果提供了表格边界框，裁剪图像
        offset_x, offset_y = 0, 0
        if table_bbox: # 暂时关闭表格边界框裁剪
            x_min, y_min, x_max, y_max = table_bbox
            # 确保边界框在图像范围内
            x_min = max(0, x_min - 100)
            y_min = max(0, y_min - 100)
            x_max = min(original_width, x_max + 100)
            y_max = min(original_height, y_max + 100)
            
            # 保存偏移量，用于后续坐标映射
            offset_x, offset_y = x_min, y_min
            
            # 裁剪图像
            table_image = image[y_min:y_max, x_min:x_max]
            logger.info(f"裁剪表格区域: [{x_min}, {y_min}, {x_max}, {y_max}]")
        else:
            table_image = image
        
        # 调用Unitable模型识别表格结构和单元格边界框
        structure_str_list, cell_bboxes, processing_time = self.unitable(table_image)
        logger.info(f"Unitable模型处理完成，耗时: {processing_time:.2f}秒")
        
        # 如果有偏移，调整单元格边界框坐标
        if offset_x > 0 or offset_y > 0:
            # 调整边界框坐标，加上偏移量
            adjusted_cell_bboxes = np.copy(cell_bboxes)
            adjusted_cell_bboxes[:, 0] += offset_x  # x_min
            adjusted_cell_bboxes[:, 1] += offset_y  # y_min
            adjusted_cell_bboxes[:, 2] += offset_x  # x_max
            adjusted_cell_bboxes[:, 3] += offset_y  # y_max
            cell_bboxes = adjusted_cell_bboxes
        
        # 创建表格区域的二进制图像，用于裁剪
        if table_bbox:
            # 如果提供了表格边界框，直接使用
            table_region = table_bbox
        else:
            # 如果没有提供表格边界框，使用所有单元格边界框的外接矩形
            x_min = np.min(cell_bboxes[:, 0])
            y_min = np.min(cell_bboxes[:, 1])
            x_max = np.max(cell_bboxes[:, 2])
            y_max = np.max(cell_bboxes[:, 3])
            table_region = [x_min, y_min, x_max, y_max]
        
        # 对表格区域进行文字检测
        if not text_results and self.text_detection:
            # 如果提供了文字检测服务，使用它对表格区域进行文字检测
            logger.info("对表格区域进行文字检测")
            
            # 裁剪表格区域图像
            x_min, y_min, x_max, y_max = table_region
            table_region_image = image[int(y_min):int(y_max), int(x_min):int(x_max)]
            
            # 将裁剪后的图像编码为二进制数据
            _, table_region_bytes = cv2.imencode('.png', table_region_image)
            
            # 对表格区域进行文字检测
            text_bboxes = self.text_detection.detect(table_region_bytes.tobytes())
            
            # 调整文字边界框坐标，加上表格区域的偏移量
            for i in range(len(text_bboxes)):
                text_bboxes[i][0] += x_min
                text_bboxes[i][1] += y_min
                text_bboxes[i][2] += x_min
                text_bboxes[i][3] += y_min
            
            # 对检测到的文字进行识别
            text_results = self.text_recognition.recognize(image_bytes, text_bboxes)
            logger.info(f"检测到 {len(text_bboxes)} 个文字区域")
        else:
            # 如果没有提供文字检测服务，回退到使用单元格边界框
            logger.warning("未提供文字检测服务，使用单元格边界框进行文字识别")
            text_results = []
        
        # 将结果转换为标准格式
        result = self._convert_to_table_result(cell_bboxes, structure_str_list, text_results)
        
        logger.info("表格识别完成")
        return result
    
    def _prepare_image(self, image_bytes: bytes) -> np.ndarray:
        """将图像二进制数据转换为numpy数组
        
        Args:
            image_bytes: 图像的二进制数据
            
        Returns:
            np.ndarray: 图像的numpy数组
        """
        # 使用cv2.imdecode将二进制数据解码为numpy数组
        image = cv2.imdecode(np.frombuffer(image_bytes, np.uint8), cv2.IMREAD_COLOR)
        return image
    
    def _convert_to_table_result(self, bboxes: np.ndarray, structure_str_list: List[str], text_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """将HTML表格和边界框转换为标准的表格识别结果格式
        
        Args:
            bboxes: 单元格边界框坐标数组
            structure_str_list: html表示的表格结构字符串列表
            text_results: 文本识别结果列表
            
        Returns:
            Dict[str, Any]: 标准格式的表格识别结果
        """
        # 使用decode_logic_points方法解析表格结构，获取正确的行列信息
        logic_points = self.matcher.decode_logic_points(structure_str_list)
        
        # 创建单元格列表
        cells = []
        
        # 将逻辑坐标和边界框坐标对应起来
        for i, (logic_point, bbox) in enumerate(zip(logic_points, bboxes)):
            row_start, row_end, col_start, col_end = logic_point
            row_span = row_end - row_start + 1
            col_span = col_end - col_start + 1
            
            # 将四边形边界框转换为矩形边界框
            # 四边形格式：[x1, y1, x2, y2, x3, y3, x4, y4]
            # 转换为矩形格式：[左上x, 左上y, 右下x, 右下y]
            if len(bbox) == 8:  # 确保是四边形格式
                x_coords = [bbox[0], bbox[2], bbox[4], bbox[6]]
                y_coords = [bbox[1], bbox[3], bbox[5], bbox[7]]
                rect_bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            else:
                # 如果不是四边形格式，保持原样
                rect_bbox = bbox.tolist()
            
            # 找到与当前单元格重叠的文本
            cell_text = ""
            cell_confidence = 0.0
            text_count = 0
            
            for text_item in text_results:
                text_bbox = text_item["bbox"]
                
                # 计算文本边界框有多大比例在单元格边界框内
                # 文本边界框：[left, top, right, bottom]
                # 单元格矩形：[left, top, right, bottom]
                
                # 计算交集矩形
                inter_left = max(rect_bbox[0], text_bbox[0])
                inter_top = max(rect_bbox[1], text_bbox[1])
                inter_right = min(rect_bbox[2], text_bbox[2])
                inter_bottom = min(rect_bbox[3], text_bbox[3])
                
                # 如果没有交集，跳过
                if inter_left >= inter_right or inter_top >= inter_bottom:
                    continue
                    
                # 计算交集面积
                inter_area = (inter_right - inter_left) * (inter_bottom - inter_top)
                
                # 文本边界框面积
                text_area = (text_bbox[2] - text_bbox[0]) * (text_bbox[3] - text_bbox[1])
                
                # 文本在单元格内的比例
                text_in_cell_ratio = inter_area / text_area
                
                # 如果文本边界框至少80%在单元格内，认为文本属于这个单元格
                if text_in_cell_ratio >= 0.8:
                    if cell_text:
                        cell_text += " "  # 如果有多个文本块，用空格连接
                    cell_text += text_item["words"]
                    cell_confidence += text_item["confidence"]
                    text_count += 1
                    
                    # 添加调试日志
                    logger.debug(f"单元格 #{i} 匹配到文本: '{text_item['words']}' (覆盖率={text_in_cell_ratio:.2f})")
            
            # 计算平均置信度
            if text_count > 0:
                cell_confidence /= text_count
            else:
                cell_confidence = 0.9  # 默认置信度
                
            # 如果没有找到文本，使用空字符串
            if not cell_text:
                cell_text = ""
                logger.debug(f"单元格 #{i} 未匹配到文本，位置: 行={row_start}, 列={col_start}")
            
            # 创建单元格对象
            cell = {
                "bbox": bbox.tolist(),
                "row_start": row_start,
                "col_start": col_start,
                "row_span": row_span,
                "col_span": col_span,
                "text": {
                    "value": cell_text,
                    "confidence": float(cell_confidence)
                },
                "confidence": 0.9  # 表格结构识别的置信度（默认值）
            }
            cells.append(cell)
        
        # 创建结果字典
        sort_cells(cells)
        result = {
            "confidence": 0.9,
            "cells": cells
        }
        
        return result 