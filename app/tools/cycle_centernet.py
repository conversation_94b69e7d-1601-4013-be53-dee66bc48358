#!/usr/bin/env python
# -*- coding: utf-8 -*-
from pathlib import Path
import json

from PIL import Image
import numpy as np
import cv2
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks


table_recognition = pipeline(Tasks.table_recognition, model='iic/cv_dla34_table-structure-recognition_cycle-centernet')
assets = Path(__file__).parent.parent / "assets"
img_path = str(assets / "test03.png")
# test_img = Image.open(img_path)
with open(img_path, 'rb') as f:
    image_bytes = f.read()
test_img = cv2.imdecode(np.frombuffer(image_bytes, np.uint8), cv2.IMREAD_COLOR)
result = table_recognition(test_img)

result = result["polygons"].tolist()
print(json.dumps(result, indent=2, ensure_ascii=False))
# 打印结果如下:
# [
#     [466.1, 4, 466, 39.3, 696, 39, 696, 4],
#     [696, 4, 696, 39, 927, 39.5, 927, 4],
#     [6, 74, 6, 109, 236, 108, 236, 74],
#     [466, 39, 466, 74, 696, 74, 696, 39],
#     [466, 74, 466, 108, 696, 108.7, 696, 74],
#     [236, 74, 236, 108, 466, 108, 466, 74],
#     [696, 39, 696, 108, 927, 108, 927, 39],
#     [236, 39, 236, 74, 466, 74.4, 466, 39],
#     [6, 39, 6, 74, 236.6, 74, 236, 39],
#     [6, 4, 6, 39, 466, 39, 466, 4.9]
# ]
