# 文字检测模块调用链分析 (ONNX - V20250409)

## 调用链

### 节点: `main.py` (脚本入口)

*   **所在代码文件相对路径**: `main.py`
*   **用途**:
    1.  调用 `get_available_models()` 获取模型配置信息。
    2.  根据用户指定的模型（此处为 `V20250409-ONNX（208MB）`），获取其 `det_config`。
    3.  使用该配置初始化 `OCRPipeline`。
    4.  调用 `ocr.process_batch()` 启动OCR流程。
*   **输入参数**:
    *   `my_image_dir` (str): 待处理图片的目录路径。
*   **输出说明**:
    *   在控制台打印总运行时间。
    *   处理结果保存到 `my_output_dir` 目录。

---

### 节点: `OCRPipeline.__init__`

*   **所在代码文件相对路径**: `modules/pipelines/ocr_pipeline.py`
*   **用途**: 初始化OCR流水线，创建 `TextDetector` 实例。
*   **输入参数**:
    *   `det_config` (str): 文本检测模型的配置文件路径 (`./configs/det/efvitsam_db_v0956_hr_v2_e39_onnx.yaml`)。
*   **输出说明**: 返回一个配置好的 `OCRPipeline` 实例。

---

### 节点: `TextDetector.__init__`

*   **所在代码文件相对路径**: `modules/predictors/detector.py`
*   **用途**: 初始化文本检测器。
*   **输入参数**:
    *   `config_path` (str): 检测器配置文件的路径。
*   **函数处理流程**:
    1.  调用父类 `BaseOCR.__init__`。
    2.  调用 `self.init_model()`。
*   **输出说明**: 返回一个初始化完成的 `TextDetector` 实例。

---

### 节点: `BaseOCR.__init__`

*   **所在代码文件相对路径**: `modules/predictors/base.py`
*   **用途**: 加载配置文件并确定模型类型。
*   **输入参数**:
    *   `config_path` (str): 配置文件路径。
*   **函数处理流程**:
    1.  调用 `self._load_config(config_path)` 加载YAML配置文件。
    2.  从配置中读取 `checkpoints` 字段获取模型路径。
    3.  调用 `self._determine_model_type(self.model_path)` 根据模型文件后缀判断模型类型为 `onnx`。
*   **输出说明**: 无返回值，但会初始化 `self.config` 和 `self.model_type` 等属性。

---

### 节点: `TextDetector.init_model`

*   **所在代码文件相对路径**: `modules/predictors/detector.py`
*   **用途**: 加载ONNX模型并创建前后处理器。
*   **输入参数**: 无。
*   **函数处理流程**:
    1.  从 `self.config` 中获取ONNX模型路径 (`./assets/models_v2/onnx/det/efvitsam_db_v0956_hr_v2_e39.onnx`)。
    2.  调用 `self.create_onnx_session(model_path)` 创建ONNX推理会话。
    3.  调用 `build_preprocess(self.config["PreProcess"])` 创建预处理器。
    4.  调用 `build_postprocess(self.config["PostProcess"])` 创建后处理器。
*   **输出说明**: 无返回值，但会初始化 `self.session`, `self.preprocess`, `self.post_process`。

---

### 节点: `build_preprocess`

*   **所在代码文件相对路径**: `modules/utils/det_pre_proc.py`
*   **用途**: 根据配置构建预处理器实例。
*   **输入参数**:
    *   `config` (dict): `PreProcess` 部分的配置。
*   **函数处理流程**:
    1.  从配置中获取 `name` 字段 (`DBDetPreProc`)。
    2.  实例化 `DBDetPreProc(**config)`。
*   **输出说明**: 返回 `DBDetPreProc` 的实例。

---

### 节点: `build_postprocess`

*   **所在代码文件相对路径**: `modules/utils/det_post_proc.py`
*   **用途**: 根据配置构建后处理器实例。
*   **输入参数**:
    *   `config` (dict): `PostProcess` 部分的配置。
*   **函数处理流程**:
    1.  从配置中获取 `name` 字段 (`DBPostProcess`)。
    2.  实例化 `DBPostProcess(**config)`。
*   **输出说明**: 返回 `DBPostProcess` 的实例。

---

### 节点: `OCRPipeline.process_batch` -> `OCRPipeline.process_image`

*   **所在代码文件相对路径**: `modules/pipelines/ocr_pipeline.py`
*   **用途**: 遍历所有图片，并对每张图片调用 `self.detector.detect()`。
*   **输入参数**:
    *   `ori_im` (numpy.ndarray): BGR格式的图像数据。
*   **输出说明**: 返回检测到的文本框 `dt_boxes`。

---

### 节点: `TextDetector.detect`

*   **所在代码文件相对路径**: `modules/predictors/detector.py`
*   **用途**: 执行单张图像的检测流程。
*   **输入参数**:
    *   `image` (numpy.ndarray): BGR格式的图像数据。
*   **函数处理流程**:
    1.  调用 `self.preprocess(img)` 进行预处理。
    2.  调用 `self.session.run(...)` 执行ONNX推理。
    3.  调用 `self.post_process(...)` 进行后处理。
*   **输出说明**: 返回文本框坐标数组。

---

### 节点: `DBDetPreProc.__call__`

*   **所在代码文件相对路径**: `modules/utils/det_pre_proc.py`
*   **用途**: 执行图像预处理。
*   **输入参数**:
    *   `img` (numpy.ndarray): BGR格式的图像数据。
*   **函数处理流程**:
    1.  `resize_image(img)`: 根据 `limit_side_len` 和 `limit_type` 调整图像尺寸。
    2.  `normalize_image(img)`: 对图像进行归一化（除以255，减均值，除以标准差）。
    3.  `to_chw_image(img)`: 将图像从HWC格式转为CHW格式。
*   **输出说明**: 返回预处理后的图像和形状信息。

---

### 节点: `DBPostProcess.__call__`

*   **所在代码文件相对路径**: `modules/utils/det_post_proc.py`
*   **用途**: 执行模型输出的后处理。
*   **输入参数**:
    *   `outs_dict` (dict): 包含模型输出 `maps` 的字典。
    *   `batch`: 包含原始图像尺寸信息的批次数据。
*   **函数处理流程**:
    1.  对 `preds = outs_dict['maps']` 进行阈值处理，得到二值化图 `segmentation`。
    2.  根据 `box_type` (`'quad'`)，调用 `self.boxes_from_bitmap(pred, mask, ...)`。
*   **输出说明**: 返回一个包含 `points` 和 `scores` 的字典列表。

---

### 节点: `DBPostProcess.boxes_from_bitmap`

*   **所在代码文件相对路径**: `modules/utils/det_post_proc.py`
*   **用途**: 从二值化图中提取四边形文本框。
*   **输入参数**:
    *   `pred` (numpy.ndarray): 模型的原始预测图。
    *   `bitmap` (numpy.ndarray): 二值化后的掩码图。
    *   `dest_width`, `dest_height`: 原始图像的宽高。
*   **函数处理流程**:
    1.  使用 `cv2.findContours` 找到连通区域。
    2.  调用 `self.get_mini_boxes(contour)` 获取每个轮廓的最小外接矩形。
    3.  调用 `self.box_score_fast(pred, box)` 计算每个框的置信度。
    4.  根据 `box_thresh` 过滤低置信度的框。
    5.  调用 `self.unclip(box)` 对框进行扩展。
*   **输出说明**: 返回过滤和扩展后的文本框坐标列表和对应的分数列表。

## 整体用途

该调用链的整体用途是：从一个指定的图像目录开始，逐一读取图片，利用基于ONNX模型的文本检测器找出图片中所有文本行的位置（边界框），为后续的文本方向分类和文本内容识别提供输入。整个流程从高级的 `OCRPipeline` 逐层调用到具体的 `TextDetector`，其中涉及配置加载、模型初始化、图像预处理、模型推理和结果后处理等一系列步骤。

## 目录结构

```
ocr_inference/
├── main.py
├── configs/
│   └── det/
│       └── efvitsam_db_v0956_hr_v2_e39_onnx.yaml
├── assets/
│   └── models_v2/
│       └── onnx/
│           └── det/
│               └── efvitsam_db_v0956_hr_v2_e39.onnx
└── modules/
    ├── pipelines/
    │   ├── __init__.py
    │   ├── ocr_pipeline.py
    │   └── dto.py
    ├── predictors/
    │   ├── __init__.py
    │   ├── base.py
    │   └── detector.py
    └── utils/
        ├── __init__.py
        ├── det_pre_proc.py
        └── det_post_proc.py
```

## 调用时序图

```mermaid
sequenceDiagram
    participant main.py
    participant ocr_pipeline.py
    participant detector.py
    participant base.py
    participant det_pre_proc.py
    participant det_post_proc.py

    main.py->>ocr_pipeline.py: OCRPipeline(det_config)
    ocr_pipeline.py->>detector.py: TextDetector(config_path)
    detector.py->>base.py: super().__init__(config_path)
    base.py-->>detector.py: return
    detector.py->>detector.py: init_model()
    detector.py->>det_pre_proc.py: build_preprocess(config)
    det_pre_proc.py-->>detector.py: return DBDetPreProc instance
    detector.py->>det_post_proc.py: build_postprocess(config)
    det_post_proc.py-->>detector.py: return DBPostProcess instance
    detector.py-->>ocr_pipeline.py: return TextDetector instance
    ocr_pipeline.py-->>main.py: return OCRPipeline instance

    main.py->>ocr_pipeline.py: process_batch(image_dir)
    ocr_pipeline.py->>ocr_pipeline.py: process_image(image_path)
    ocr_pipeline.py->>detector.py: detect(image)
    detector.py->>det_pre_proc.py: __call__(image)
    det_pre_proc.py-->>detector.py: return preprocessed_image
    detector.py->>detector.py: session.run(preprocessed_image)
    detector.py->>det_post_proc.py: __call__(preds)
    det_post_proc.py->>det_post_proc.py: boxes_from_bitmap(preds, mask)
    det_post_proc.py-->>detector.py: return boxes
    detector.py-->>ocr_pipeline.py: return dt_boxes
    ocr_pipeline.py-->>main.py: return results
```
