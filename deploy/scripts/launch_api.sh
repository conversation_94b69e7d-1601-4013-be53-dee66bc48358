#!/bin/bash

#
## AgileOCR API 启动脚本
## 用于 Kubernetes 容器环境中启动 OCR 服务
#
#set -e  # 遇到错误立即退出
#
#echo "=== AgileOCR API 启动脚本开始 ==="
#echo "工作目录: ${WK_DIR}"
#echo "时间: $(date)"
#
## 1. 初始化模型文件挂载
#echo "=== 步骤1: 初始化模型文件挂载 ==="
#${WK_DIR}/deploy/scripts/init_mount.sh
#
## 2. 启动后端服务（后台运行）
#echo "=== 步骤2: 启动后端 FastAPI 服务 ==="
#cd ${WK_DIR}
#
## 启动后端服务函数
#start_backend() {
#    echo "启动后端服务: python3 -m uvicorn app.main:app --host 0.0.0.0 --port 6000"
#    # python3 -m uvicorn app.main:app --host 0.0.0.0 --port 6000
#    poetry run python3 -m uvicorn app.main:app --host $HOST --port $PORT
#}
#
## 后台启动后端
#start_backend &
#BACKEND_PID=$!
#echo "后端服务已启动，PID: ${BACKEND_PID}"
#
## 等待后端服务启动
#echo "等待后端服务启动..."
#sleep 5
#
## 检查后端服务是否正常启动
#if kill -0 ${BACKEND_PID} 2>/dev/null; then
#    echo "后端服务启动成功"
#else
#    echo "错误: 后端服务启动失败"
#    exit 1
#fi
#
## 3. 启动前端服务
#echo "=== 步骤3: 启动前端开发服务器 ==="
#
## 检查前端目录是否存在
#if [ ! -d "${WK_DIR}/web" ]; then
#    echo "警告: 未找到前端目录 ${WK_DIR}/web，跳过前端服务启动"
#    echo "仅运行后端服务..."
#    wait ${BACKEND_PID}
#    exit 0
#fi
#
#cd ${WK_DIR}/web
#
## 设置前端环境变量
#export VITE_HOST="0.0.0.0"
#export VITE_PORT="5000"
#export VITE_BACKEND_HOST="localhost"
#export VITE_BACKEND_PORT="6000"
#
#echo "前端服务配置:"
#echo "  Host: ${VITE_HOST}"
#echo "  Port: ${VITE_PORT}"
#echo "  Backend Host: ${VITE_BACKEND_HOST}"
#echo "  Backend Port: ${VITE_BACKEND_PORT}"
#
## 安装前端依赖
#echo "安装前端依赖..."
#if command -v pnpm &> /dev/null; then
#    echo "使用 pnpm 安装依赖"
#    pnpm install
#elif command -v npm &> /dev/null; then
#    echo "使用 npm 安装依赖"
#    npm install
#else
#    echo "错误: 未找到 npm 或 pnpm，无法启动前端服务"
#    echo "仅运行后端服务..."
#    wait ${BACKEND_PID}
#    exit 0
#fi
#
## 启动前端服务函数
#start_frontend() {
#    if command -v pnpm &> /dev/null; then
#        echo "使用 pnpm 启动前端开发服务器"
#        pnpm run dev
#    elif command -v npm &> /dev/null; then
#        echo "使用 npm 启动前端开发服务器"
#        npm run dev
#    else
#        echo "错误: 未找到 npm 或 pnpm"
#        return 1
#    fi
#}
#
## 前台启动前端
#start_frontend &
#FRONTEND_PID=$!
#echo "前端服务已启动，PID: ${FRONTEND_PID}"
#
## 4. 设置信号处理，确保优雅关闭
#cleanup() {
#    echo ""
#    echo "=== 接收到退出信号，正在关闭服务 ==="
#
#    if [ -n "${FRONTEND_PID}" ] && kill -0 ${FRONTEND_PID} 2>/dev/null; then
#        echo "关闭前端服务 (PID: ${FRONTEND_PID})"
#        kill ${FRONTEND_PID}
#    fi
#
#    if [ -n "${BACKEND_PID}" ] && kill -0 ${BACKEND_PID} 2>/dev/null; then
#        echo "关闭后端服务 (PID: ${BACKEND_PID})"
#        kill ${BACKEND_PID}
#    fi
#
#    echo "所有服务已关闭"
#    exit 0
#}
#
## 注册信号处理器
#trap cleanup SIGTERM SIGINT
#
## 5. 等待服务运行
#echo "=== AgileOCR 服务启动完成 ==="
#echo "后端服务: http://0.0.0.0:6000"
#echo "前端服务: http://0.0.0.0:5000"
#echo "按 Ctrl+C 停止所有服务"
#
## 等待任一服务退出
#wait

#./start_app.sh &
#./start_web_dev.sh &
#wait


# 1. 初始化模型文件挂载
echo "=== 步骤1: 初始化模型文件挂载 ==="
./deploy/scripts/init_mount.sh

# 2. 启动后端服务（后台运行）
echo "=== 步骤2: 启动后端 FastAPI 服务 ==="
./start_app.sh
