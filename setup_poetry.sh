#!/bin/bash

# 显示命令执行
set -x

# 检查是否已安装Poetry
if ! command -v poetry &> /dev/null; then
    echo "正在安装Poetry..."
    curl -sSL https://install.python-poetry.org | python3 -
    
    # 临时添加Poetry到PATH以便在当前脚本中使用
    export PATH="$HOME/.local/bin:$PATH"
    
    # 检查是否成功安装
    if ! command -v poetry &> /dev/null; then
        echo ""
        echo "===== Poetry安装后环境变量配置指南 ====="
        echo "Poetry已安装，但需要手动添加到PATH才能在终端中使用。"
        echo "请根据您的shell类型执行以下操作:"
        echo ""
        echo "1. 将Poetry添加到PATH (根据您的shell类型选择):"
        echo "   对于bash: echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bashrc"
        echo "   对于zsh:  echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.zshrc"
        echo "   对于macOS bash: echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bash_profile"
        echo ""
        echo "2. 重新加载配置:"
        echo "   对于bash: source ~/.bashrc"
        echo "   对于zsh:  source ~/.zshrc"
        echo "   对于macOS bash: source ~/.bash_profile"
        echo ""
        echo "3. 验证安装:"
        echo "   poetry --version"
        echo ""
        exit 1
    fi
    
    echo ""
    echo "===== Poetry安装后环境变量配置指南 ====="
    echo "Poetry已成功安装并在当前脚本中可用。"
    echo "但要在新的终端中使用，请将其添加到PATH中:"
    echo ""
    echo "1. 将Poetry添加到PATH (根据您的shell类型选择):"
    echo "   对于bash: echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bashrc"
    echo "   对于zsh:  echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.zshrc"
    echo "   对于macOS bash: echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bash_profile"
    echo ""
    echo "2. 重新加载配置:"
    echo "   对于bash: source ~/.bashrc"
    echo "   对于zsh:  source ~/.zshrc"
    echo "   对于macOS bash: source ~/.bash_profile"
    echo ""
else
    echo "Poetry已安装，版本："
    poetry --version
fi

# 配置Poetry使用项目内的虚拟环境
echo "配置Poetry使用项目内的虚拟环境..."
poetry config virtualenvs.in-project true

# 配置镜像源
# echo "配置Poetry使用镜像源..."
# poetry config repositories.default https://mirrors.ivolces.com/pypi/simple/

# 安装项目依赖
echo "安装项目依赖..."
poetry install

# 显示已安装的依赖
echo "已安装的依赖："
poetry show

echo "Poetry设置完成！"
echo "使用以下命令运行应用："
echo "poetry run python3 -m uvicorn app.main:app --reload" 