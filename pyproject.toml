[tool.poetry]
name = "agileocr"
version = "0.1.0"
description = "My OCR service project"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10, <3.11"

# project
fastapi = ">=0.115.0, <0.116.0"
uvicorn = ">=0.29.0, <0.30.0"
pydantic = "^2.6.0"
pydantic-settings = ">=2.2.0, <2.3.0"
pillow = ">=10.2.0, <10.3.0"
python-multipart = ">=0.0.9, <0.0.10"
torch = "2.5.1"
torchvision = "0.20.1"
matplotlib = "3.10.3"
#tokenizers = "0.13.0"

# inference
onnx = "1.18.0"
onnxruntime-gpu = "1.21.1"

# unimerner
albumentations = "2.0.8"
ipykernel = "6.29.5"
ipywidgets = "8.1.5"
omegaconf = "2.3.0"
setuptools = "75.8.0"
wand = "0.6.10"
transformers = "4.48.1"

# cycle-centernet
modelscope = {extras = ["framework"], version = "*"} # 假设 modelscope 包存在

# text_det
PyYAML = "6.0"
#numpy = "1.21.0"
numpy = ">=1.21.0, <2.0"  
opencv-python = "^4.8.0"
shapely = "2.1.0"
pyclipper = "1.3.0"
# typing-extensions = "4.0.0" # 通常作为其他库的依赖被自动安装，可以不写
#protobuf = "~3.20.0"      # <--- 新增这一行：锁定 protobuf 到 3.20.x 版本

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"