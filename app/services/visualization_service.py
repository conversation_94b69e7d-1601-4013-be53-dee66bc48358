from pathlib import Path
from uuid import UUID
import copy

import logging
from PIL import Image

from app.utils.ocr_visualization import generate_ocr_visualization
from app.core import errors

logger = logging.getLogger(__name__)


class VisualizationService:
    """可视化结果生成服务"""

    def __init__(self, visualizations_dir: str, persistence_service, image_service):
        """初始化可视化服务

        Args:
            visualizations_dir: 可视化结果存储目录路径
            persistence_service: 持久化服务实例
            image_service: 图片服务实例
        """
        self.visualizations_dir = Path(visualizations_dir)
        self.persistence_service = persistence_service
        self.image_service = image_service

        # 确保目录存在
        self.visualizations_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"可视化结果存储目录: {self.visualizations_dir.absolute()}")

    def get_or_create_visualization(self, request_id: UUID, view_type: str) -> Path:
        """获取或创建可视化结果图

        Args:
            request_id: 请求ID
            view_type: 视图类型

        Returns:
            Path: 可视化结果图的路径

        Raises:
            FileNotFoundError: 当原始图片或OCR结果不存在时
            各种PIL相关异常: 当图片处理失败时
        """
        # 构建文件名
        file_name = f"{request_id}_{view_type}.png"
        visualization_path = self.visualizations_dir / file_name

        # 如果文件已存在，直接返回
        if visualization_path.exists():
            logger.info(f"可视化结果已存在: {visualization_path}")
            return visualization_path

        # 获取OCR结果数据
        ocr_result = self.persistence_service.get_result(request_id)

        # 获取原始图片路径
        original_image_path = self.image_service.get_image_path(request_id)

        # 生成可视化结果
        generated_path = self._generate_visualization(
            request_id, view_type, ocr_result, original_image_path
        )
        logger.info(f"可视化结果已生成: {generated_path}")
        return generated_path

    def _generate_visualization(self, request_id: UUID, view_type: str,
                              ocr_result: dict, original_image_path: Path) -> Path:
        """生成可视化结果图

        Args:
            request_id: 请求ID
            view_type: 视图类型
            ocr_result: OCR结果数据
            original_image_path: 原始图片路径

        Returns:
            Path: 生成的可视化图片路径
        """
        target_ocr = copy.deepcopy(ocr_result)
        target_result = target_ocr.get('results', {})
        if view_type == "text_detection" and not "text_detection" in target_result:
            target_result["text_detection"] = target_result.get("text_recognition")
        no_table = (not "table_recognition" in target_result)
        no_formula = (not "formula_recognition" in target_result)
        if no_table:
            target_result["table_recognition"] = []
        if no_formula:
            target_result["formula_recognition"] = []
        layouts = target_result.get("layout_analysis")
        if not layouts:
            layouts = []
        for layout_item in layouts:
            layout_type = layout_item["type"].lower()
            if view_type == "table_recognition" and no_table and layout_type == "table":
                target_result["table_recognition"].append(layout_item)
            if view_type == "formula_recognition" and no_formula and layout_type == "formula":
                target_result["formula_recognition"].append(layout_item)

        # 打开原始图片
        with Image.open(original_image_path) as original_img:
            result_img = generate_ocr_visualization(view_type, original_img, target_ocr.get('results', {}))
            file_name = f"{request_id}_{view_type}.png"
            output_path = self.visualizations_dir / file_name
            result_img.save(output_path, 'PNG')

            return output_path

