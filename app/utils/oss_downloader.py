#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/6/5 10:45
# <AUTHOR> <EMAIL>
# @FileName: oss_downloader

import os
import oss2
from tqdm import tqdm
from app.utils.log import LOGGER


# 配置你的阿里云AccessKeyId, AccessKeySecret
access_key_id = 'LTAI5tRQ3sXTAdLqToLkiPkQ'
access_key_secret = '******************************'
endpoint = 'oss-cn-shenzhen.aliyuncs.com'
bucket_name = 'opscf'


def get_persisted_remote_file_size(local_file_path):
    """尝试从同名txt文件中读取远程文件大小"""
    size_file_path = f"{local_file_path}.txt"
    if os.path.exists(size_file_path):
        try:
            with open(size_file_path, 'r') as f:
                size = int(f.read().strip())
                LOGGER.info(f"Loaded remote file size from {size_file_path}: {size}")
                return size
        except Exception as e:
            LOGGER.error(f"Failed to read remote file size from {size_file_path}: {e}")
            return None
    return None


def persist_remote_file_size(local_file_path, remote_file_size):
    """将远程文件大小写入到同名txt文件中"""
    size_file_path = f"{local_file_path}.txt"
    try:
        with open(size_file_path, 'w') as f:
            f.write(str(remote_file_size))
        LOGGER.info(f"Persisted remote file size to {size_file_path}: {remote_file_size}")
    except Exception as e:
        LOGGER.error(f"Failed to persist remote file size to {size_file_path}: {e}")


def oss_download_file(object_name, local_file_path, max_retries=5):
    LOGGER.info(f"oss_download_file Downloading start {object_name} to {local_file_path}")
    # 初始化OSS Auth和Bucket
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)

    # 尝试从本地缓存读取远程文件大小
    remote_file_size = get_persisted_remote_file_size(local_file_path)

    # 如果本地没有缓存，调用head_object获取远程文件大小
    if remote_file_size is None:
        for attempt in range(max_retries):
            try:
                # 获取远程文件的Content-Length
                head_result = bucket.head_object(object_name)
                remote_file_size = head_result.content_length
                LOGGER.info(f"Fetched remote file size: {remote_file_size}")

                # 成功获取远程文件大小后，持久化到本地
                persist_remote_file_size(local_file_path, remote_file_size)
                break
            except oss2.exceptions.NoSuchKey:
                raise Exception(f"File {object_name} does not exist in the bucket.")
            except Exception as e:
                LOGGER.error(f"An error occurred during head_object attempt {attempt + 1}: {e}")
        else:
            raise Exception(f"Failed to fetch remote file size after {max_retries} attempts.")

    # 如果本地文件存在，检查本地文件的大小
    if os.path.exists(local_file_path):
        local_file_size = os.path.getsize(local_file_path)
        LOGGER.info(f"Local file size: {local_file_size}, Remote file size: {remote_file_size}")

        # 如果本地文件大小和远程文件大小相同，则无需下载
        if local_file_size == remote_file_size:
            LOGGER.info(f"Local file {local_file_path} is already up-to-date.")
            return

    # 下载OSS文件到本地文件，显示进度条
    for attempt in range(max_retries):
        try:
            LOGGER.info(f"oss_download_file resumable_download {object_name} to {local_file_path}")
            with tqdm(total=remote_file_size, unit='B', unit_scale=True, desc=local_file_path, ascii=True) as pbar:
                def progress_callback(consumed_bytes, total_bytes):
                    pbar.update(consumed_bytes - pbar.n)

                oss2.resumable_download(bucket, object_name, local_file_path, progress_callback=progress_callback)

            # 下载完成后重新检查本地文件大小
            local_file_size = os.path.getsize(local_file_path)
            LOGGER.info(f"After download local file size: {local_file_size}, Remote file size: {remote_file_size}")

            if local_file_size == remote_file_size:
                LOGGER.info(f"File {object_name} downloaded to {local_file_path} successfully.")
                return
            else:
                LOGGER.warning(f"File size mismatch for {local_file_path}. Retrying download...")

        except Exception as e:
            LOGGER.error(f"An error occurred during download attempt {attempt + 1}: {e}")

    raise Exception(f"Failed to download {object_name} after {max_retries} attempts due to file size mismatch.")