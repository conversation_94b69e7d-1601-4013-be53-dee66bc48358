#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import copy
from typing import Dict, List, Tuple, Optional
from shapely.geometry import Polygon

def to_polygon(bbox):
    points = []
    point_type = type(bbox[0])
    if not point_type in (tuple, list):
        for i in range(0, len(bbox), 2):
            point = bbox[i:i+2]
            points.append(tuple(point))
    if not points:
        points = copy.deepcopy(bbox)
    if len(points) == 2:
        x_min, y_min = points[0]
        x_max, y_max = points[1]
        points = [
            (x_min, y_min), # 左上
            (x_max, y_min), # 右上
            (x_max, y_max), # 右下
            (x_min, y_max), # 左下
        ]
    return points

def flaten_points(points):
    if not points:
        return []
    point_type = type(points[0])
    if not point_type in (tuple, list):
        return points
    return [x for p in points for x in p]

def to_flat_polygon(bbox):
    points = to_polygon(bbox)
    return flaten_points(points)

def sort_polygon_points(points: List[List[int]]) -> List[List[int]]:
    """
    对多边形顶点进行顺时针排序
    
    按照以下规则排序：
    1. 计算所有顶点的几何中心点
    2. 顶点按照相对于中心点的角度进行顺时针排序
    
    参数:
        points: 表示多边形顶点坐标的列表，格式为[[x1, y1], ..., [xn, yn]]，或者[(x1, y1), ..., (xn, yn)]
        
    返回:
        按顺时针顺序排列的顶点坐标列表
    """
    if len(points) < 3:
        raise ValueError("多边形顶点数量必须大于等于3")
    
    # 计算几何中心点
    center_x = sum(p[0] for p in points) / len(points)
    center_y = sum(p[1] for p in points) / len(points)
    
    def calculate_angle(point):
        dx = point[0] - center_x
        dy = point[1] - center_y
        if abs(dy) < 0.0001 and abs(dx) < 0.0001:
            err = f"{point}和中心点{round(center_x)}, {round(center_y)}重叠. points: {points}"
            raise ValueError(err)
        # 使用atan2计算角度，并调整为顺时针方向（图像坐标系中y轴向下）
        angle = math.atan2(dy, dx)
        if abs(angle - math.pi) < 0.0001: # angle == math.pi
            angle = -math.pi
        return angle

    sorted_points = sorted(points, key=calculate_angle)
    
    return sorted_points

def move_polygon(polygon: List, x, y):
    point_type = type(polygon[0])
    if point_type in (tuple, list):
        new_polygon = []
        for point in polygon:
            new_polygon.append(
                [point[0] + x,
                point[1] + y]
            )
        return new_polygon

    new_polygon = list(polygon)
    for i in range(0, len(polygon), 2):
        new_polygon[i] += x
    for i in range(1, len(polygon), 2):
        new_polygon[i] += y
    return new_polygon

def inter_polygon(polygon1, polygon2):
    """返回交集多边形"""
    polygon1 = to_polygon(polygon1)
    polygon2 = to_polygon(polygon2)
    poly1 = Polygon(polygon1)
    poly2 = Polygon(polygon2)
    if not poly1.is_valid or not poly2.is_valid:
        return None
    intersection_poly = poly1.intersection(poly2)
    return intersection_poly

def polygon_area(polygon):
    polygon = Polygon(to_polygon(polygon))
    if not polygon.is_valid:
        return 0
    return Polygon(polygon).area

def rotate_size(width, height, angle):
    """将图像围绕中心点旋转angle度，返回旋转后的图像宽高。逆时针旋转时angle为正"""
    cx = width / 2.0
    cy = height / 2.0
    corners = [
        (-cx, cy),  # 左上
        (cx, cy),   # 右上
        (-cx, -cy), # 左下
        (cx, -cy),  # 右下
    ]
    angle_rad = math.radians(angle)
    cos_rad = math.cos(angle_rad)
    sin_rad = math.sin(angle_rad)
    min_x, max_x = 0, 0
    min_y, max_y = 0, 0
    for corner_x, corner_y in corners:
        rotated_corner_x = corner_x * cos_rad - corner_y * sin_rad
        rotated_corner_y = corner_x * sin_rad + corner_y * cos_rad
        min_x = min(min_x, rotated_corner_x)
        max_x = max(max_x, rotated_corner_x)
        min_y = min(min_y, rotated_corner_y)
        max_y = max(max_y, rotated_corner_y)
    return int(max_x - min_x), int(max_y - min_y)

def rotate_point(x, y, angle, center_x, center_y):
    """将图像围绕中心点(center_x center_y)旋转angle度，返回(x y)在旋转后图像中的坐标。
    逆时针旋转时angle为正"""

    # 1. 将角度转换为弧度，并计算sin和cos值
    angle_rad = math.radians(angle)
    cos_rad = math.cos(angle_rad)
    sin_rad = math.sin(angle_rad)

    # 2. 将输入点转换为相对于图像中心的坐标
    dx = x - center_x
    dy = center_y - y

    # 3. 旋转相对于中心的点
    new_x = dx * cos_rad - dy * sin_rad
    new_y = dx * sin_rad + dy * cos_rad

    # 4. 旋转四个角点
    corners = [
        (-center_x, center_y),  # 左上
        (center_x, center_y),   # 右上
        (-center_x, -center_y),   # 左下
        (center_x, -center_y)     # 右下
    ]
    new_left_x, new_top_y = 0, 0
    for corner_x, corner_y in corners:
        rotated_corner_x = corner_x * cos_rad - corner_y * sin_rad
        rotated_corner_y = corner_x * sin_rad + corner_y * cos_rad
        # 更新旋转后的左上角
        new_left_x = min(new_left_x, rotated_corner_x)
        new_top_y = max(new_top_y, rotated_corner_y)

    final_x = new_x - new_left_x
    final_y = new_top_y - new_y
    return final_x, final_y

if __name__ == "__main__":
    assert sort_polygon_points([[25, 10], [25, 30], [35, 20], [5, 15], [10, 25], [15, 5]]) == [[5, 15], [15, 5], [25, 10], [35, 20], [25, 30], [10, 25]]
    assert sort_polygon_points([[25, 10], [25, 5], [5, 10], [5, 5]]) == [[5, 5], [25, 5], [25, 10], [5, 10]]
    assert sort_polygon_points([[15, 5], [15, 15], [20, 10], [10, 10]]) == [[10, 10], [15, 5], [20, 10], [15, 15]]
    
    def round_point(point):
        return tuple([round(x) for x in point])

    assert round_point(rotate_point(0, 500, 90, 1000/2, 2000/2)) == (500, 1000)
    assert round_point(rotate_point(0, 500, -90, 1000/2, 2000/2)) == (1500, 0)
    assert round_point(rotate_point(500, 0, 90, 1000/2, 2000/2)) == (0, 500)
    assert round_point(rotate_point(500, 0, -90, 1000/2, 2000/2)) == (2000, 500)
    assert round_point(rotate_point(10, 20, 90, 1000/2, 2000/2)) == (20, 1000-10)
    assert round_point(rotate_point(10, 20, -90, 1000/2, 2000/2)) == (2000-20, 10)