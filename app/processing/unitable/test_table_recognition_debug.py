import unittest
import os
import numpy as np
import cv2
import json
import logging
from pathlib import Path
from pprint import pprint
from unittest.mock import MagicMock, patch

from app.core.config import proj_dir, settings
from app.processing.table_recognition import TableRecognitionService
from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestTableRecognitionDebug(unittest.TestCase):
    """TableRecognitionService的调试测试类，用于观察表格识别流程的结果"""
    
    @classmethod
    def setUpClass(cls):
        """在所有测试开始前执行一次，用于加载模型"""
        # 获取项目根目录
        cls.proj_dir = Path(__file__).parent.parent.parent
        cls.test_image_path = proj_dir / "app/assets/test02.png"
        
        with open(cls.test_image_path, "rb") as f:
            cls.test_image_bytes = f.read()
        
        # 创建文本识别服务
        cls.text_recognition = TextRecognitionService()
        cls.text_detection = TextDetectionService()
        
        # 创建配置
        cls.config = {
            "UNITABLE_ENCODER_WEIGHTS": settings.UNITABLE_ENCODER_WEIGHTS,
            "UNITABLE_DECODER_WEIGHTS": settings.UNITABLE_DECODER_WEIGHTS,
            "UNITABLE_VOCAB_PATH": settings.UNITABLE_VOCAB_PATH,
            "USE_CUDA": settings.USE_CUDA
        }
        
        # 创建表格识别服务
        cls.service = TableRecognitionService(cls.config, cls.text_recognition, cls.text_detection)
    
    def test_print_unitable_raw_output(self):
        """打印Unitable模型的原始输出，包括结构字符串列表和边界框"""
        # 定义测试表格边界框 [x_min, y_min, x_max, y_max]
        table_bbox = [100, 100, 500, 400]
        
        # 解码图像
        image = cv2.imdecode(np.frombuffer(self.test_image_bytes, np.uint8), cv2.IMREAD_COLOR)
        
        # 如果提供了表格边界框，裁剪图像
        if table_bbox:
            x_min, y_min, x_max, y_max = table_bbox
            # 确保边界框在图像范围内
            x_min = max(0, x_min)
            y_min = max(0, y_min)
            x_max = min(image.shape[1], x_max)
            y_max = min(image.shape[0], y_max)
            
            # 裁剪图像
            table_image = image[y_min:y_max, x_min:x_max]
            logger.info(f"裁剪表格区域: [{x_min}, {y_min}, {x_max}, {y_max}]")
        else:
            table_image = image
        
        # 直接调用Unitable模型
        logger.info("调用Unitable模型识别表格结构...")
        structure_str_list, cell_bboxes, processing_time = self.service.unitable(table_image)
        
        # 打印结构字符串列表
        logger.info("Unitable模型输出的结构字符串列表:")
        for i, structure_str in enumerate(structure_str_list):
            logger.info(f"{i}: {structure_str}")
        
        # 打印边界框
        logger.info("Unitable模型输出的单元格边界框:")
        for i, bbox in enumerate(cell_bboxes):
            logger.info(f"单元格 {i+1}: {bbox.tolist()}")
        
        # 打印处理时间
        logger.info(f"Unitable模型处理时间: {processing_time:.2f}秒")
        
        # 使用matcher解析表格结构
        logger.info("使用TableMatch解析表格结构...")
        logic_points = self.service.matcher.decode_logic_points(structure_str_list)
        
        # 打印逻辑坐标
        logger.info("解析后的逻辑坐标 (行开始,行结束,列开始,列结束):")
        for i, logic_point in enumerate(logic_points):
            logger.info(f"单元格 {i+1}: {logic_point}")
        
        # 打印表格的行列结构
        max_row = max([point[1] for point in logic_points]) + 1 if logic_points else 0
        max_col = max([point[3] for point in logic_points]) + 1 if logic_points else 0
        logger.info(f"表格结构: {max_row}行 x {max_col}列")
        
        # 创建一个可视化的表格结构
        table_structure = [['.' for _ in range(max_col)] for _ in range(max_row)]
        for i, (logic_point, bbox) in enumerate(zip(logic_points, cell_bboxes)):
            row_start, row_end, col_start, col_end = logic_point
            for r in range(row_start, row_end + 1):
                for c in range(col_start, col_end + 1):
                    if r < max_row and c < max_col:
                        table_structure[r][c] = f'{i+1}'
        
        # 打印表格结构
        logger.info("表格结构可视化 (数字表示单元格ID):")
        for row in table_structure:
            logger.info(' '.join(f"{cell:3s}" for cell in row))
    
    def test_print_table_recognition_results(self):
        """打印表格识别的详细结果，不进行断言验证"""
        # 定义测试表格边界框 [x_min, y_min, x_max, y_max]
        table_bbox = [100, 100, 500, 400]
        
        # 调用表格识别方法
        logger.info("开始进行表格识别...")
        result = self.service.recognize_table(self.test_image_bytes, table_bbox)
        
        # 打印结果
        logger.info("表格识别结果:")
        logger.info(f"表格置信度: {result['confidence']}")
        logger.info(f"单元格数量: {len(result['cells'])}")
        
        # 打印每个单元格的详细信息
        logger.info("单元格详细信息:")
        for i, cell in enumerate(result['cells']):
            logger.info(f"单元格 {i+1}:")
            logger.info(f"  位置: 行={cell['row_start']}, 列={cell['col_start']}")
            logger.info(f"  跨度: 行跨度={cell['row_span']}, 列跨度={cell['col_span']}")
            logger.info(f"  边界框: {cell['bbox']}")
            logger.info(f"  文本: \"{cell['text']}\"")
            logger.info(f"  置信度: {cell['confidence']}")
        
        # 构建表格的文本表示
        max_row = max([cell['row_start'] + cell['row_span'] for cell in result['cells']])
        max_col = max([cell['col_start'] + cell['col_span'] for cell in result['cells']])
        
        # 创建空表格
        table = [['' for _ in range(max_col)] for _ in range(max_row)]
        
        # 填充表格内容
        for cell in result['cells']:
            row = cell['row_start']
            col = cell['col_start']
            text = cell['text']
            table[row][col] = text
        
        # 打印表格
        logger.info("表格内容:")
        for row in table:
            logger.info(' | '.join([f"{cell:20s}" for cell in row]))
        
        # 打印JSON格式的结果
        logger.info("JSON格式的结果:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 不进行断言验证，只用于观察结果
        
    def test_print_table_recognition_with_text_detection(self):
        """打印使用文字检测的表格识别详细结果，不进行断言验证"""
        # 定义测试表格边界框 [x_min, y_min, x_max, y_max]
        table_bbox = [100, 100, 500, 400]
        
        # 打印调试信息
        logger.info("开始进行表格识别（使用文字检测）...")
        
        # 解码图像
        image = cv2.imdecode(np.frombuffer(self.test_image_bytes, np.uint8), cv2.IMREAD_COLOR)
        
        # 裁剪表格区域
        x_min, y_min, x_max, y_max = table_bbox
        table_image = image[y_min:y_max, x_min:x_max]
        
        # 将裁剪后的图像编码为二进制数据
        _, table_image_bytes = cv2.imencode('.png', table_image)
        
        # 对表格区域进行文字检测
        logger.info("对表格区域进行文字检测...")
        text_bboxes = self.text_detection.detect(table_image_bytes.tobytes())
        logger.info(f"检测到 {len(text_bboxes)} 个文字区域")
        
        # 打印检测到的文字区域
        logger.info("检测到的文字区域:")
        for i, bbox in enumerate(text_bboxes):
            logger.info(f"文字区域 {i+1}: {bbox}")
        
        # 对检测到的文字进行识别
        logger.info("对检测到的文字进行识别...")
        
        # 调整文字边界框坐标，加上表格区域的偏移量
        adjusted_text_bboxes = []
        for bbox in text_bboxes:
            adjusted_bbox = [
                bbox[0] + x_min,
                bbox[1] + y_min,
                bbox[2] + x_min,
                bbox[3] + y_min
            ]
            adjusted_text_bboxes.append(adjusted_bbox)
        
        text_results = self.text_recognition.recognize(self.test_image_bytes, adjusted_text_bboxes)
        
        # 打印识别结果
        logger.info("识别结果:")
        for i, result in enumerate(text_results):
            logger.info(f"文字 {i+1}: \"{result['words']}\" (置信度: {result['confidence']})")
        
        # 调用表格识别方法
        logger.info("进行完整表格识别...")
        result = self.service.recognize_table(self.test_image_bytes, table_bbox)
        
        # 打印结果
        logger.info("表格识别结果:")
        logger.info(f"表格置信度: {result['confidence']}")
        logger.info(f"单元格数量: {len(result['cells'])}")
        
        # 构建表格的文本表示
        max_row = max([cell['row_start'] + cell['row_span'] for cell in result['cells']])
        max_col = max([cell['col_start'] + cell['col_span'] for cell in result['cells']])
        
        # 创建空表格
        table = [['' for _ in range(max_col)] for _ in range(max_row)]
        
        # 填充表格内容
        for cell in result['cells']:
            row = cell['row_start']
            col = cell['col_start']
            text = cell['text']
            table[row][col] = text
        
        # 打印表格
        logger.info("表格内容:")
        for row in table:
            logger.info(' | '.join([f"{cell:20s}" for cell in row]))
        
        # 不进行断言验证，只用于观察结果


if __name__ == "__main__":
    unittest.main()