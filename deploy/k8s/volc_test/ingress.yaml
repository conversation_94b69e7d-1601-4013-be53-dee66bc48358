# 测试环境

token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoibW9kZWx6b29hcGktdGVzdCIsIm5hbWVzcGFjZSI6ImthbnBpYyIsImtpbmQiOiJJbmdyZXNzIiwiVmVyc2lvbiI6InYxIiwiaXNzIjoiazhzLWFwaS1saXRlIn0.7mvE0jEvX8JCooZSIf8Bo3kMydsnYCIrq5TuZGe6Aa4"
name: modelzooapi-test
namespace: kanpic

rules:
  - host: modelzooapi-test-inner.zhhainiao.com
    http:
      paths:
        # 后端API路由
        - path: /
          backend:
            serviceName: modelzooapi-test
            servicePort: 6000
#        # 前端静态文件路由
#        - path: /
#          backend:
#            serviceName: modelzooapi-test
#            servicePort: 5000
