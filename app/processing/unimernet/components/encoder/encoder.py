from typing import Op<PERSON>, <PERSON><PERSON>, Union
from transformers import <PERSON>win<PERSON>odel, DonutSwinConfig
from transformers.models.swin.modeling_swin import SwinEncoder, SwinPatchMerging, SwinModelOutput
from torch import nn
from .embeddings.embeddings import UniMERNetEmbeddings
from .layers.stage import UniMERNetEncoderStage
from .config import UniMERNetEncoderConfig
import torch

from transformers import AutoConfig, AutoModel


class UniMERNetEncoderModel(SwinModel):
    config_class = UniMERNetEncoderConfig

    def __init__(self, config, add_pooling_layer=True, use_mask_token=False):
        super().__init__(config, add_pooling_layer, use_mask_token)

        self.embeddings = UniMERNetEmbeddings(
            config, use_mask_token=use_mask_token)
        self.encoder = UniMERNetEncoder(config, self.embeddings.patch_grid)

        # Initialize weights and apply final processing
        self.post_init()

    def forward(
        self,
        pixel_values: Optional[torch.FloatTensor] = None,
        bool_masked_pos: Optional[torch.BoolTensor] = None,
        head_mask: Optional[torch.FloatTensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        interpolate_pos_encoding: bool = False,
        return_dict: Optional[bool] = None,
    ) -> Union[Tuple, SwinModelOutput]:
        r"""
        bool_masked_pos (`torch.BoolTensor` of shape `(batch_size, num_patches)`, *optional*):
            Boolean masked positions. Indicates which patches are masked (1) and which aren't (0).
        """
        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = (
            output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        )
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        if pixel_values is None:
            raise ValueError("You have to specify pixel_values")

        # Prepare head mask if needed
        # 1.0 in head_mask indicate we keep the head
        # attention_probs has shape bsz x n_heads x N x N
        # input head_mask has shape [num_heads] or [num_hidden_layers x num_heads]
        # and head_mask is converted to shape [num_hidden_layers x batch x num_heads x seq_length x seq_length]
        head_mask = self.get_head_mask(head_mask, len(self.config.depths))

        embedding_output, input_dimensions = self.embeddings(
            pixel_values, bool_masked_pos=bool_masked_pos, interpolate_pos_encoding=interpolate_pos_encoding
        )

        encoder_outputs = self.encoder(
            embedding_output,
            input_dimensions,
            head_mask=head_mask,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = encoder_outputs[0]

        pooled_output = None
        if self.pooler is not None:
            pooled_output = self.pooler(sequence_output.transpose(1, 2))
            pooled_output = torch.flatten(pooled_output, 1)

        if not return_dict:
            output = (sequence_output, pooled_output) + encoder_outputs[1:]

            return output

        return SwinModelOutput(
            last_hidden_state=sequence_output,
            pooler_output=pooled_output,
            hidden_states=encoder_outputs.hidden_states,
            attentions=encoder_outputs.attentions,
            reshaped_hidden_states=encoder_outputs.reshaped_hidden_states,
        )


class UniMERNetEncoder(SwinEncoder):
    def __init__(self, config, grid_size):
        super().__init__(config, grid_size)

        dpr = [x.item() for x in torch.linspace(
            0, config.drop_path_rate, sum(config.depths))]
        self.layers = nn.ModuleList(
            [
                UniMERNetEncoderStage(
                    config=config,
                    dim=int(config.embed_dim * 2**i_layer),
                    input_resolution=(
                        grid_size[0] // (2**i_layer), grid_size[1] // (2**i_layer)),
                    depth=config.depths[i_layer],
                    num_heads=config.num_heads[i_layer],
                    drop_path=dpr[sum(config.depths[:i_layer]): sum(
                        config.depths[: i_layer + 1])],
                    downsample=SwinPatchMerging if (
                        i_layer < self.num_layers - 1) else None,
                )
                for i_layer in range(self.num_layers)
            ]
        )

        self.gradient_checkpointing = False
