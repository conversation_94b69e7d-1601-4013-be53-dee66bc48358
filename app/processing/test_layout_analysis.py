import unittest
import os
import logging
from pathlib import Path
import numpy as np

from app.processing.layout_analysis import LayoutAnalysisService, IDX_CLS_DICT
from app.core.config import assets_dir

# 设置日志级别
logging.basicConfig(level=logging.INFO)

class TestLayoutAnalysisService(unittest.TestCase):
    
    def setUp(self):
        """初始化测试环境"""
        self.service = LayoutAnalysisService()
        self.test_image_path = str(assets_dir / "test01.png")
        self.assertTrue(os.path.exists(self.test_image_path), f"测试图像不存在: {self.test_image_path}")
        
        # 读取测试图像
        with open(self.test_image_path, "rb") as f:
            self.image_bytes = f.read()
    
    def test_analyze(self):
        """测试版面分析功能的整体结果"""
        # 调用分析方法
        results = self.service.analyze(self.image_bytes)
        
        # 验证结果基本结构
        self.assertIsInstance(results, list)
        self.assertTrue(len(results) > 0, "分析结果不应为空")
        
        # 验证结果项结构
        for item in results:
            self.assertIsInstance(item, dict)
            self.assertIn("bbox", item)
            self.assertIn("type", item)
            self.assertIn("confidence", item)
            
            # 验证bbox格式
            bbox = item["bbox"]
            self.assertIsInstance(bbox, list)
            self.assertEqual(len(bbox), 4)
            self.assertTrue(all(isinstance(coord, int) for coord in bbox))
            
            # 验证type是预期的类别之一
            self.assertIn(item["type"], list(IDX_CLS_DICT.values()))
            
            # 验证confidence是numpy.float32且在0-1之间
            confidence = item["confidence"]
            self.assertIsInstance(confidence, np.float32)
            self.assertTrue(0 <= float(confidence) <= 1)
    
    def test_preprocess(self):
        """测试预处理函数"""
        im_data, img_h, img_w = self.service._preprocess(self.image_bytes)
        
        # 验证输出类型
        self.assertIsInstance(im_data, np.ndarray)
        self.assertIsInstance(img_h, int)
        self.assertIsInstance(img_w, int)
        
        # 验证张量形状
        self.assertEqual(im_data.shape[0], 1, "批次大小应为1")
        self.assertEqual(im_data.shape[1], 3, "通道数应为3 (RGB)")
        self.assertEqual(im_data.shape[2], self.service.imgsz, "高度应为设定的图像大小")
        self.assertEqual(im_data.shape[3], self.service.imgsz, "宽度应为设定的图像大小")
    
    def test_inference(self):
        """测试推理函数"""
        results = self.service._inference(self.image_bytes)
        
        # 验证结果类型和结构
        self.assertIsInstance(results, list)
        if results:
            self.assertIsInstance(results[0], dict)
            self.assertIn("bbox", results[0])
            self.assertIn("type", results[0])
            self.assertIn("confidence", results[0])
    
    def test_postprocess(self):
        """测试后处理函数"""
        # 模拟输入数据
        labels = np.array([0, 1, 2])
        boxes = np.array([[10, 20, 100, 200], [30, 40, 300, 400], [50, 60, 500, 600]])
        scores = np.array([0.9, 0.8, 0.7])
        img_h, img_w = 1000, 1000
        
        # 调用后处理函数
        results = self.service._postprocess(labels, boxes, scores, img_h, img_w)
        
        # 验证结果
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)  # 应有3个结果项
        
        # 验证结果项结构
        for i, item in enumerate(results):
            self.assertIsInstance(item, dict)
            self.assertIn("bbox", item)
            self.assertIn("type", item)
            self.assertIn("confidence", item)
            
            # 验证bbox值
            expected_bbox = list(map(int, boxes[i]))
            self.assertEqual(item["bbox"], expected_bbox)
            
            # 验证type值
            expected_type = IDX_CLS_DICT[labels[i]]
            self.assertEqual(item["type"], expected_type)
            
            # 验证confidence值
            self.assertEqual(item["confidence"], scores[i])

if __name__ == "__main__":
    unittest.main() 