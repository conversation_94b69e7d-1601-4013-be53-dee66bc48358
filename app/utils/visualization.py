"""
可视化工具模块 - 提供通用的工具函数
"""

import os
from io import BytesIO

from typing import List, Dict, Tuple, Optional, Any, Union
from PIL import Image, ImageDraw, ImageFont, ImageColor
from matplotlib import mathtext
import matplotlib.font_manager as mfm

DEFAULT_ALPHA = 0.65

# 类别颜色映射
CATEGORY_COLORS = {
    "background": "gray",
    "text": "blue",
    "title": "red",
    "image": "green",
    "caption": "lime",
    "table": "purple",
    "header": "cyan",
    "footer": "cyan",
    "reference": "orange",
    "formular": "yellow",
}

def is_transparent(*colors):
    for color in colors:
        if color is None:
            continue
        if isinstance(color, str):
            continue
        if len(color) == 4 and color[3] < 255:
            return True
    return False

def new_overlay(size)->Tuple[Image.Image, ImageDraw.ImageDraw]:
    overlay = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(overlay)
    return overlay, draw

default_font: ImageFont.FreeTypeFont = None
def get_font(font_path: str=None, font_size: int=12) -> ImageFont.FreeTypeFont:
    """
    获取字体，支持中文字符。尝试使用指定字体，如果失败则使用默认字体
    
    Args:
        font_path: 字体文件路径
        font_size: 字体大小
        
    Returns:
        字体对象
    """
    # 尝试加载指定字体
    global default_font

    if font_path:
        return ImageFont.truetype(font_path, font_size)
    
    # 使用默认字体
    if default_font is None:
        local_dir = os.path.dirname(__file__)
        font_path = os.path.join(local_dir, "simfang.ttf")
        try:
            default_font = ImageFont.truetype(font_path, font_size)
        except Exception:
            default_font = ImageFont.load_default(font_size)
        return default_font

    if default_font.size == font_size:
        return default_font
    return default_font.font_variant(size=font_size)


def create_transparent_color(color: Union[str, Tuple[int, int, int]], alpha: float = DEFAULT_ALPHA) -> Tuple[int, int, int, int]:
    """
    创建带透明度的颜色
    
    Args:
        color: 颜色值，可以是颜色名称字符串或RGB元组
        alpha: 透明度，范围0-1，1为不透明
        
    Returns:
        RGBA颜色元组
    """
    # 确保 alpha 在有效范围内
    alpha = max(0.0, min(1.0, alpha))
    alpha_int = int(alpha * 255)
    
    # 如果是字符串颜色名称，转换为RGB
    if isinstance(color, str):
        r, g, b = ImageColor.getrgb(color)
        return (r, g, b, alpha_int)
    return (color[0], color[1], color[2], alpha_int)


def get_category_color(label: str, color_mapping: Dict[str, str] = None) -> str:
    """
    根据标签文本确定类别颜色
    
    Args:
        label: 标签文本
        color_mapping: 类别到颜色的映射字典，如果为None则使用CATEGORY_COLORS
        
    Returns:
        颜色名称
    """
    if color_mapping is None:
        color_mapping = CATEGORY_COLORS
    
    # 尝试匹配包含关键词的标签
    for key, color in color_mapping.items():
        if key.lower() == label.lower():
            return color
    
    # 默认返回灰色
    return "gray"


def calculate_scale_factors(orig_width: int, orig_height: int, 
                            input_width: Optional[int] = None, 
                            input_height: Optional[int] = None) -> Tuple[float, float]:
    """
    计算图像坐标缩放因子
    
    Args:
        orig_width: 原始图像宽度
        orig_height: 原始图像高度
        input_width: 模型输入图像宽度（可选）
        input_height: 模型输入图像高度（可选）
        
    Returns:
        (scale_x, scale_y)元组，如果未提供input_width或input_height，则返回(1.0, 1.0)
    """
    # 如果没有提供输入尺寸，则不需要缩放
    if input_width is None or input_height is None:
        return 1.0, 1.0
    
    # 防止除零错误
    if input_width == 0 or input_height == 0:
        return 1.0, 1.0
    
    # 计算缩放因子
    scale_x = float(orig_width) / float(input_width)
    scale_y = float(orig_height) / float(input_height)
    
    return scale_x, scale_y


def scale_bbox(bbox: List[int], scale_x: float, scale_y: float) -> List[int]:
    """
    缩放边界框坐标
    
    Args:
        bbox: 边界框坐标 [x1, y1, x2, y2]
        scale_x: x轴缩放因子
        scale_y: y轴缩放因子
        
    Returns:
        缩放后的边界框坐标 [x1', y1', x2', y2']
    """
    # 确保边界框格式正确
    if len(bbox) != 4:
        raise ValueError(f"边界框格式错误，应为[x1, y1, x2, y2]，实际为{bbox}")
    
    # 应用缩放因子
    x1 = int(bbox[0] * scale_x)
    y1 = int(bbox[1] * scale_y)
    x2 = int(bbox[2] * scale_x)
    y2 = int(bbox[3] * scale_y)

    return [x1, y1, x2, y2]

def draw_label(img: Image.Image, position: Tuple[int, int], label: str, 
               color: Tuple[int, int, int, int], font: ImageFont.FreeTypeFont, 
               with_background: bool = True) -> None:
    """
    在图像上绘制标签文本
    
    Args:
        img: PIL图像对象
        position: 文本位置 (x, y)
        label: 标签文本
        color: RGBA颜色元组
        font: 字体对象
        with_background: 是否绘制半透明背景
    """
    # 获取文本尺寸
    if is_transparent(color) or with_background:
        overlay, draw = new_overlay(img.size)
    else:
        overlay, draw = img, ImageDraw.Draw(img)
    stroke_width = 0.3
    text_width, text_height = draw.textbbox((0, 0), label, font=font)[2:] # , stroke_width=stroke_width
    x, y = position
    # 如果需要背景，先绘制半透明背景
    if with_background:
        # 创建背景颜色（灰色半透明）
        bg_color = (204, 204, 204, 128)
        
        # 绘制背景矩形，比文本空间略大
        draw.rectangle(
            [
                (x, y),
                (x + text_width + 2, y + text_height + 2)
            ],
            fill=bg_color
        )
    
    # 绘制文本
    draw.text((x+1, y+1), label, fill=color, font=font) # , stroke_width=stroke_width
    if is_transparent(color) or with_background:
        img.alpha_composite(overlay)
        del overlay, draw

def wrap_text_by_chars(text: str, font: ImageFont.FreeTypeFont, max_width: int, max_lines: int) -> Tuple[List[str], str]:
    """
    按字符换行文本
    
    Args:
        text: 需要换行的文本
        font: 字体对象
        max_width: 最大宽度
        max_lines: 最大行数
        
    Returns:
        (已换行的文本行列表, 剩余文本)元组
    """
    lines, remaining = [], text
    while remaining and len(lines) < max_lines:
        char_count = 0
        line_width = 0
        while char_count < len(remaining):
            c = remaining[char_count]
            x1, _, x2, _ = font.getbbox(c)
            char_width = x2 - x1
            line_width += char_width * 1.01
            if line_width > max_width:
                break
            char_count += 1
        
        if char_count == 0:
            break
        
        lines.append(remaining[:char_count])
        remaining = remaining[char_count:]
    
    return lines, remaining


def fit_text_to_box(text: str, box_width: int, box_height: int, font: ImageFont.FreeTypeFont, 
                    min_font_size: int = 8, max_font_size: int = 72) -> Tuple[ImageFont.FreeTypeFont, str, float]:
    """
    使用二分法找到适合文本框的最大字体大小和排版
    
    Args:
        text: 需要显示的文本
        box_width: 文本框宽度
        box_height: 文本框高度
        font_path: 字体路径
        min_font_size: 最小字体大小
        max_font_size: 最大字体大小
        
    Returns:
        (最佳字体, 处理后的文本, 行高)元组
    """
    line_spacing = 1.3
    low, high = min_font_size, max_font_size
    best = None

    # 二分法搜索最大可行字体
    while low <= high:
        mid = (low + high) // 2
        font = font.font_variant(size=mid)
        
        # 计算实际行高和最大允许行数
        _, y1, _, y2 = font.getbbox("Ay")
        line_height = (y2 - y1) * line_spacing  # 实际测量行高
        max_lines = int(box_height // line_height) if line_height > 0 else 0
        
        # 跳过行高超限的字体
        if max_lines < 1:
            high = mid - 1
            continue
        
        # 尝试换行并检查完整性
        wrapped, remaining = wrap_text_by_chars(text, font, box_width, max_lines)
        if remaining:
            high = mid - 1
        else:
            # 记录最佳候选并尝试更大字体
            best = (font, "\n".join(wrapped), line_height)
            low = mid + 1

    # 找到合法解：返回最佳结果
    if best:
        return best

    # 无解时使用最小字体显示完整文本，允许宽度溢出
    font = font.font_variant(size=min_font_size)
    _, y1, _, y2 = font.getbbox("Ay")
    line_height = (y2 - y1) * line_spacing
    return font, text, line_height


def draw_rect_text(img: Image.Image, bbox, 
    bg_color=None, line_color=None, line_width=1, 
    font:ImageFont.FreeTypeFont=None, min_font_size=8, max_font_size=72, 
    text=None, text_color=(0, 0, 0), 
    label=None, label_color=(255, 0, 0)):
    """
    在图像上绘制带文本的矩形框，并且自适应调整text大小适应bbox
    
    Args:
        img: PIL图像对象
        bbox: 边界框坐标 [x1, y1, x2, y2]
        bg_color: 矩形背景颜色
        line_color: 矩形边框颜色
        line_width: 矩形边框宽度
        font: 字体对象
        min_font_size: 最小字体大小
        max_font_size: 最大字体大小
        text: 要显示的文本
        text_color: 文本颜色
        label: 标签文本
        label_color: 标签颜色
    """
    if is_transparent(bg_color, line_color, text_color, label_color):
        overlay, draw = new_overlay(img.size)
    else:
        overlay, draw = img, ImageDraw.Draw(img)
    draw.rectangle(bbox, fill=bg_color, outline=line_color, width=line_width)

    x1, y1, x2, y2 = bbox
    if text:
        box_width = x2 - x1
        box_height = y2 - y1
        txt_font, text, _ = fit_text_to_box(
                text=text, box_width=box_width, box_height=box_height, 
                font=font, min_font_size=min_font_size, max_font_size=max_font_size
            )
        draw.multiline_text((x1+5, y1+5), text, fill=text_color, font=txt_font, align="left")
    if is_transparent(bg_color, line_color, text_color, label_color):
        img.alpha_composite(overlay)
        del overlay, draw
    if label:
        label_font = font.font_variant(size=10)
        draw_label(img, (x1+2, y1+2), label, label_color, label_font, True)


def draw_polygon(img: Image.Image, quad_points: List[Tuple[int, int]], 
             outline_color=None, fill_color=None, line_width: int = 1) -> None:
    """
    在图像上绘制四边形
    
    Args:
        img: PIL图像对象
        quad_points: 四边形的四个顶点坐标，格式为[(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        outline_color: 边框颜色，可以是颜色名称字符串或RGB/RGBA元组
        fill_color: 填充颜色，可以是颜色名称字符串或RGB/RGBA元组，None表示不填充
        line_width: 边框线宽
    """
    
    # 检查是否需要透明层
    if is_transparent(outline_color, fill_color):
        overlay, draw = new_overlay(img.size)
    else:
        overlay, draw = img, ImageDraw.Draw(img)
    
    # 绘制四边形
    draw.polygon(quad_points, fill=fill_color, outline=outline_color, width=line_width)
    
    # 如果使用了透明层，合成到原图
    if is_transparent(outline_color, fill_color):
        img.alpha_composite(overlay)
        del overlay, draw


def draw_polygon_text(img: Image.Image, quad_points, 
    bg_color=None, line_color=None, line_width=1, 
    font:ImageFont.FreeTypeFont=None, min_font_size=8, max_font_size=72, 
    text=None, text_color=(0, 0, 0), 
    label=None, label_color=(255, 0, 0)):
    """
    在图像上绘制带文本的四边形，并且自适应调整text大小适应最小外接矩形
    
    Args:
        img: PIL图像对象
        quad_points: 四边形的四个点坐标 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
                    或者 [x1, y1, x2, y2, x3, y3, x4, y4]
        bg_color: 四边形背景颜色
        line_color: 四边形边框颜色
        line_width: 四边形边框宽度
        font: 字体对象
        min_font_size: 最小字体大小
        max_font_size: 最大字体大小
        text: 要显示的文本
        text_color: 文本颜色
        label: 标签文本
        label_color: 标签颜色
    """
    point_type = type(quad_points[0])
    if not point_type in (tuple, list):
        tuple_points = []
        for i in range(0, len(quad_points), 2):
            point = quad_points[i:i+2]
            tuple_points.append(tuple(point))
        quad_points = tuple_points
    
    if len(quad_points) == 2:
        x1, y1 = quad_points[0]
        x2, y2 = quad_points[1]
        quad_points = [
            quad_points[0], 
            (x2, y1), 
            quad_points[1],
            (x1, y2), 
        ]
    # 绘制四边形
    draw_polygon(img, quad_points, outline_color=line_color, fill_color=bg_color, line_width=line_width)
    
    # 调用draw_rect_text绘制文本，但不重复绘制矩形边框
    if text or label:
        # 计算最小外接矩形
        xs = [p[0] for p in quad_points]
        ys = [p[1] for p in quad_points]
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        bbox = [min_x, min_y, max_x, max_y]
        draw_rect_text(img, bbox, 
            bg_color=None, line_color=None, line_width=0,
            font=font, min_font_size=min_font_size, max_font_size=max_font_size,
            text=text, text_color=text_color,
            label=label, label_color=label_color)

_latex_prop = mfm.FontProperties(size=72, weight='normal')
def latex_to_img(latex):
    global _latex_prop

    img_io = BytesIO()
    mathtext.math_to_image(latex, img_io, dpi=300, prop=_latex_prop)
    img_io.seek(0)
    return Image.open(img_io)

def fit_img_polygon(img: Image.Image, polygon, margin=5):
    if not polygon:
        return None
    min_x, min_y, max_x, max_y = float("inf"), float("inf"), float("-inf"), float("-inf")
    for point in polygon:
        min_x = min(min_x, point[0])
        max_x = max(max_x, point[0])
        min_y = min(min_y, point[1])
        max_y = max(max_y, point[1])

    min_x = min(min_x+margin, max_x)
    min_y = min(min_y+margin, max_y)
    max_x = max(max_x-margin, min_x)
    max_y = max(max_y-margin, min_y)
    width = max_x - min_x
    height = max_y - min_y
    if width < margin or height < margin:
        return None

    width_scale = width*1.0 / img.width
    height_scale = height*1.0 / img.height
    scale = min(width_scale, height_scale)
    to_width = int(img.width * scale)
    to_height = int(img.height * scale)
    img = img.resize((to_width, to_height), Image.LANCZOS)
    return img

if __name__ == "__main__":
    # 创建测试图像
    img_width, img_height = 800, 600
    image = Image.new('RGBA', (img_width, img_height), color=(128, 128, 128))
    font = get_font(font_size=10)
    text = "在图像上绘制带文本的矩形框，并且自适应调整text大小适应bbox。当文本非常非常非常非常非常非常非常非常长时，自动折行并调整字体大小"
    draw_rect_text(image, (200, 200, 400, 300), 
        bg_color=(255, 0, 0, 64), line_color=(255, 0, 0, 200), line_width=1, 
        font=font, text=text, text_color=(0, 0, 0), 
        label="Test", label_color=(0, 0, 255, 128))
    
    # 测试四边形绘制
    quad_points = [(500, 100), (700, 150), (650, 300), (450, 250)]
    quad_points = [500, 100, 700, 150, 650, 300, 450, 250]
    draw_polygon_text(image, quad_points, 
            bg_color=(255, 0, 0, 64), line_color=(255, 0, 0, 200), line_width=1, 
        font=font, text=text, text_color=(0, 0, 0), 
        label="Test", label_color=(0, 0, 255, 128))
    
    image.save("test.png")