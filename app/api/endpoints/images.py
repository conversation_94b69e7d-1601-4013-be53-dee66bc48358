from fastapi import APIRouter, Depends
from fastapi.responses import FileResponse
from uuid import UUID
import logging

from app.services.image_service import ImageService
from app.services.injector import get_image_service
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/{request_id}")
async def get_original_image(
    request_id: UUID,
    service: ImageService = Depends(get_image_service)
) -> FileResponse:
    """获取原始上传的图片
    
    Args:
        request_id: 请求ID
        service: 图片服务实例（通过依赖注入）
        
    Returns:
        FileResponse: 图片文件的二进制响应
    """
    logger.info(f"请求获取原始图片: {request_id}")
    
    image_path = service.get_image_path(request_id)
    logger.info(f"返回图片文件: {image_path}")
    
    return FileResponse(
        path=str(image_path),
        media_type="image/jpeg",
        filename=f"{request_id}.jpg"
    )