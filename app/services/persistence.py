from pathlib import Path
import json
from uuid import UUID
from pydantic import BaseModel

from app.core import errors

class PersistenceService:
    """负责OCR结果的持久化存储与检索"""

    def __init__(self, results_dir: str):
        """初始化持久化服务
        
        Args:
            results_dir: 结果存储目录路径
        """
        self.results_dir = Path(results_dir)
        # 确保结果目录存在
        self.results_dir.mkdir(parents=True, exist_ok=True)
    
    def save_result(self, request_id: UUID, result_data: BaseModel) -> None:
        """将结果保存为JSON文件
        
        Args:
            request_id: 请求唯一标识
            result_data: 包含结果数据的Pydantic模型
        """
        # 构造文件路径
        file_path = self.results_dir / f"{request_id}.json"
        # 将Pydantic模型转换为JSON字符串
        json_string = result_data.model_dump_json(indent=2)
        # 写入文件
        file_path.write_text(json_string, encoding='utf-8')
    
    def get_result(self, request_id: UUID) -> dict:
        """根据请求ID获取结果
        
        Args:
            request_id: 请求唯一标识
            
        Returns:
            dict: 结果数据字典
        """
        # 构造文件路径
        file_path = self.results_dir / f"{request_id}.json"
        # 检查文件是否存在
        if not file_path.exists():
            raise errors.ClientErr(errors.ErrCode.CLIENT_NOT_FOUND, "not found")
        
        # 读取文件内容
        json_string = file_path.read_text(encoding='utf-8')
        # 解析JSON字符串为Python字典
        return json.loads(json_string) 