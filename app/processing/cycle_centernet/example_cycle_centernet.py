#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CycleCenterNet表格识别使用示例

这个示例展示了如何使用CycleCenterNetTable类进行表格识别。
"""

import logging
import json
from pathlib import Path

from .table_rec_cycle_centernet import CycleCenterNetTable
from app.processing.text_recognition import TextRecognitionService
from app.processing.text_detection import TextDetectionService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数，演示CycleCenterNet表格识别的使用"""
    
    # 1. 配置参数
    config = {
        "USE_CUDA": False,  # 是否使用GPU，根据实际情况设置
    }
    
    # 2. 创建文本识别服务
    # 注意：这里需要根据实际情况创建真实的文本识别服务
    # 这里使用简化的示例
    class SimpleTextRecognitionService(TextRecognitionService):
        def __init__(self):
            pass
            
        def recognize(self, image_bytes, text_bboxes):
            """简化的文本识别实现"""
            results = []
            for i, bbox in enumerate(text_bboxes):
                # 在实际应用中，这里应该调用真实的OCR模型
                results.append({
                    "bbox": bbox,
                    "words": f"识别文本{i+1}",  # 实际应该是OCR识别的结果
                    "confidence": 0.95
                })
            return results
    
    # 3. 创建文本检测服务（可选）
    class SimpleTextDetectionService(TextDetectionService):
        def __init__(self):
            pass
            
        def detect(self, image_bytes):
            """简化的文本检测实现"""
            # 在实际应用中，这里应该调用真实的文本检测模型
            # 返回检测到的文本边界框列表
            return [
                [20, 15, 120, 35],   # [x_min, y_min, x_max, y_max]
                [150, 15, 250, 35],
                [20, 55, 120, 75],
                [150, 55, 250, 75]
            ]
    
    # 4. 创建服务实例
    text_recognition = SimpleTextRecognitionService()
    text_detection = SimpleTextDetectionService()  # 可选，如果不需要可以传None
    
    table_recognition = CycleCenterNetTable(
        config=config,
        text_recognition=text_recognition,
        text_detection=text_detection
    )
    
    # 5. 准备测试图像
    assets_dir = Path(__file__).parent.parent / "assets"
    test_image_path = assets_dir / "test03.png"
    
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        logger.info("请确保在assets目录下有test03.png测试图像")
        return
    
    # 6. 读取图像数据
    with open(test_image_path, 'rb') as f:
        image_bytes = f.read()
    
    logger.info(f"开始识别表格，图像: {test_image_path}")
    
    try:
        # 7. 执行表格识别
        result = table_recognition.recognize_table(image_bytes)
        
        # 8. 处理识别结果
        logger.info("表格识别完成！")
        logger.info(f"识别置信度: {result['confidence']}")
        logger.info(f"识别到 {len(result['cells'])} 个单元格")
        
        # 打印详细结果
        print("\n=== 表格识别结果 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 9. 分析单元格信息
        print("\n=== 单元格详细信息 ===")
        for i, cell in enumerate(result["cells"]):
            print(f"单元格 {i+1}:")
            print(f"  位置: 第{cell['row_start']+1}行, 第{cell['col_start']+1}列")
            print(f"  跨度: {cell['row_span']}行 x {cell['col_span']}列")
            print(f"  文本: '{cell['text']}'")
            print(f"  置信度: {cell['confidence']:.2f}")
            print(f"  边界框: {cell['bbox']}")
            print()
        
        # 10. 示例：如何使用带表格边界框的识别
        logger.info("演示带表格边界框的识别...")
        table_bbox = [50, 50, 800, 400]  # [x_min, y_min, x_max, y_max]
        
        result_with_bbox = table_recognition.recognize_table(image_bytes, table_bbox)
        logger.info(f"带边界框识别到 {len(result_with_bbox['cells'])} 个单元格")
        
    except Exception as e:
        logger.error(f"表格识别失败: {e}")
        raise

def usage_tips():
    """使用提示"""
    print("""
=== CycleCenterNet表格识别使用提示 ===

1. 模型特点：
   - 基于CycleCenterNet架构，专门用于表格结构识别
   - 输出多边形格式的单元格边界框
   - 适合处理复杂表格结构

2. 输入要求：
   - 支持常见图像格式（PNG, JPG等）
   - 图像质量要求较高，表格边界清晰
   - 建议图像分辨率不要过低

3. 输出格式：
   - confidence: 整体识别置信度
   - cells: 单元格列表，每个单元格包含：
     * bbox: 多边形边界框坐标
     * row_start, col_start: 起始行列位置
     * row_span, col_span: 跨越的行列数
     * text: 单元格内文本内容
     * confidence: 单元格识别置信度

4. 集成建议：
   - 配合高质量的OCR服务使用效果更佳
   - 可以先进行表格检测，再对表格区域进行结构识别
   - 对于复杂表格，建议进行预处理（如去噪、矫正等）

5. 性能优化：
   - 如果有GPU，设置USE_CUDA=True可以提升速度
   - 对于大图像，建议先裁剪表格区域再识别
   - 批量处理时可以复用模型实例
    """)

if __name__ == "__main__":
    # 显示使用提示
    usage_tips()
    
    # 运行示例
    main()
