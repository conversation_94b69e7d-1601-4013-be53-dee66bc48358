import unittest
import json
from pathlib import Path

from app.utils import table_logic

case_dir = Path(__file__).parent / "test_cases"

class TestTableLogic(unittest.TestCase):
    def test_process_table_structure(self):
        with open(case_dir/"table_logic.json", "r") as fp:
            test_cases = json.load(fp)

        for case_name, test_case in test_cases.items():
            with self.subTest(case_name=case_name):
                image_size = test_case["image_size"]
                in_cells = test_case["cells"]
                expected = test_case["expected"]

                got = table_logic.process_table_structure(in_cells, image_size=image_size)
                table_logic.sort_cells(got)
                
                self.assertEqual(got, expected)

if __name__ == '__main__':
    unittest.main() 