import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: parseInt(process.env.VITE_PORT || '5000'),
    host: process.env.VITE_HOST || 'localhost',
    proxy: {
      '/api': {
        target: `http://${process.env.VITE_BACKEND_HOST || 'localhost'}:${process.env.VITE_BACKEND_PORT || '6000'}`,
        changeOrigin: true,
        rewrite: (path) => path
      }
    }
  },
  // 使用默认的dist目录作为构建输出
  build: {}
}) 