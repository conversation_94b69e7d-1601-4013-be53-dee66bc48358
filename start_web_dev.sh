#!/bin/bash

# 设置默认值
HOST="0.0.0.0"
PORT="5000"
BACKEND_HOST="************"
BACKEND_PORT="1251"

# 解析命令行参数
for i in "$@"
do
case $i in
    --host=*)
    HOST="${i#*=}"
    ;;
    --port=*)
    PORT="${i#*=}"
    ;;
    --backend-host=*)
    BACKEND_HOST="${i#*=}"
    ;;
    --backend-port=*)
    BACKEND_PORT="${i#*=}"
    ;;
    *)
    # 未知参数
    echo "未知参数: $i"
    ;;
esac
done

# 打印参数
echo "前端服务配置:"
echo "  --host=$HOST"
echo "  --port=$PORT"
echo "后端服务配置:"
echo "  --backend-host=$BACKEND_HOST"
echo "  --backend-port=$BACKEND_PORT"

cd web
pnpm install

# 设置环境变量
export VITE_HOST=$HOST
export VITE_PORT=$PORT
export VITE_BACKEND_HOST=$BACKEND_HOST
export VITE_BACKEND_PORT=$BACKEND_PORT

# 启动开发服务器
npm run dev