# 详细设计 (LLD): OCR_RECT 模块

本文档为 `OCR_RECT` 模块提供详细的、可供直接编码的实现级设计，严格遵循“忠实复用，不新增逻辑”的核心原则。

---

## 1. 目录结构树

```
ocr_rect/
├── __init__.py         # 模块入口，暴露 OCRRect 类
├── ocr_rect.py         # 定义 OCRRect 流程编排类
├── classifier.py       # 定义 TextClassifier 类及图像裁剪辅助函数
├── recognizer.py       # 定义 TextRecognizer 类及图像旋转辅助函数
├── configs/
│   └── rec_config_template.yaml # 模块配置文件模板
└── requirements.txt    # 模块的全部依赖
```

---

## 2. 整体逻辑和交互时序图

核心工作流程与 HLD 中定义的一致。`OCRRect` 作为统一入口，依次调用 `classifier` 和 `recognizer` 模块中的功能，完成从图像裁剪到文本识别的全过程。

```mermaid
sequenceDiagram
    participant User as 调用方
    participant OCRRect as 接口层
    participant TextClassifier as 核心逻辑层 (分类)
    participant TextRecognizer as 核心逻辑层 (识别)

    User->>OCRRect: run(image, bounding_boxes)
    activate OCRRect
    loop 遍历每个 bounding_box
        Note over OCRRect, TextClassifier: OCRRect 调用与 TextClassifier 关联的辅助函数
        OCRRect->>TextClassifier: crop_image(image, box)
        activate TextClassifier
        TextClassifier-->>OCRRect: cropped_image
        deactivate TextClassifier

        OCRRect->>TextClassifier: classify(cropped_image)
        activate TextClassifier
        TextClassifier-->>OCRRect: direction, confidence
        deactivate TextClassifier

        Note over OCRRect, TextRecognizer: OCRRect 调用与 TextRecognizer 关联的辅助函数
        OCRRect->>TextRecognizer: rotate_image(cropped_image, direction)
        activate TextRecognizer
        TextRecognizer-->>OCRRect: rotated_image
        deactivate TextRecognizer

        OCRRect->>TextRecognizer: recognize(rotated_image)
        activate TextRecognizer
        TextRecognizer-->>OCRRect: text, confidence
        deactivate TextRecognizer
    end
    OCRRect-->>User: result_list
    deactivate OCRRect
```

---

## 3. 配置项

模块将通过一个 YAML 文件进行配置，模板位于 `ocr_rect/configs/rec_config_template.yaml`。该配置完全基于原始项目中的配置文件和代码中的硬编码参数，确保了100%的逻辑复用。

```yaml
# rec_config_template.yaml

# ===================================================================
# 方向分类器配置
# 来源: ./configs/cls/default_onnx.yaml
# ===================================================================
TextClassifier:
  model_path: "./assets/models_v2/onnx/cls/default.onnx"
  # 分类器在原始 pipeline 中逐个处理，因此 batch_size 为 1
  batch_size: 1
  
  # 预处理参数 (复用自 cls_pre_proc.py 和 default_onnx.yaml)
  pre_process:
    # 图像缩放尺寸 [H, W]，来自 default_onnx.yaml
    image_shape: [80, 160] 
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
    
  # 后处理参数 (复用自 cls_post_proc.py 和 default_onnx.yaml)
  post_process:
    # 标签列表，来自 default_onnx.yaml -> Global: label_map
    label_list: ["0", "180"]
    # 分类置信度阈值。此为新增配置项，提取自原始代码 cls_post_proc.py 中的硬编码值 0.9
    threshold: 0.9 

# ===================================================================
# 文本识别器配置
# 来源: ./configs/rec/bq_svtr_v2_7M_onnx.yaml
# ===================================================================
TextRecognizer:
  model_path: "./assets/models_v2/onnx/rec/bq_svtr_v2_7M.onnx"
  # 批量大小，来自 bq_svtr_v2_7M_onnx.yaml -> Global: infer_batch
  batch_size: 4
  
  # 预处理参数 (复用自 rec_pre_proc.py 和 bq_svtr_v2_7M_onnx.yaml)
  pre_process:
    # 图像缩放尺寸 [C, H, W]
    image_shape: [3, 48, 320]
    mean: [0.5, 0.5, 0.5]
    std: [0.5, 0.5, 0.5]
    
  # 后处理参数 (复用自 rec_post_proc.py 和 bq_svtr_v2_7M_onnx.yaml)
  post_process:
    character_dict_path: "./assets/word_labels/bq_ocr_keys_v2.txt"
    use_space_char: true
    # 识别置信度阈值。此为新增配置项，提取自原始代码 ocr_pipeline.py 中用于二次识别的硬编码值 0.8
    threshold: 0.8
```

---

## 4. 模块化文件详解

### `ocr_rect/ocr_rect.py`

**a. 文件用途说明**

作为模块的唯一对外接口和流程编排器，封装了完整的“裁剪-分类-旋转-识别”流程。

**b. 文件内类图**

```mermaid
classDiagram
    class OCRRect {
        - TextClassifier classifier
        - TextRecognizer recognizer
        + __init__(config_path: str)
        + run(image: numpy.ndarray, bounding_boxes: List) List[Dict]
    }
```

**c. 函数/方法详解**

#### `__init__(self, config_path: str)`

-   **用途**: 初始化 `OCRRect` 实例，加载配置并创建内部的 `TextClassifier` 和 `TextRecognizer` 实例。
-   **输入参数**: `config_path` (str): 指向 `rec_config.yaml` 的路径。
-   **输出**: 无。
-   **实现步骤和要点**:
    1.  读取并解析 `config_path` 处的 YAML 文件。
    2.  根据解析出的 `TextClassifier` 配置部分，实例化 `classifier.TextClassifier`。
    3.  根据解析出的 `TextRecognizer` 配置部分，实例化 `recognizer.TextRecognizer`。
    4.  **代码复用**: 此初始化逻辑复用了 `main.py` 中创建 `OCRPipeline` 时对 `TextClassifier` 和 `TextRecognizer` 的初始化过程。

#### `run(self, image: numpy.ndarray, bounding_boxes: List)`

-   **用途**: 对单张图中的多个文本框执行完整的 OCR 流程。
-   **输入参数**:
    -   `image` (numpy.ndarray): OpenCV BGR 格式的图像。
    -   `bounding_boxes` (List): 包含多个文本框的列表，每个框为 `[[x1, y1], [x2, y2], [x3, y3], [x4, y4]]`。
-   **输出**: (List[Dict]): 结果列表，每个字典包含 `bbox`, `words`, `confidence`, `direction`。
-   **实现步骤和要点**:
    1.  初始化一个空的结果列表 `results`。
    2.  遍历 `bounding_boxes` 中的每一个 `box`：
        a.  调用 `classifier.crop_image(image, box)` 得到裁剪后的图像 `cropped_image`。
        b.  调用 `self.classifier.classify(cropped_image)` 得到方向 `direction`。
        c.  调用 `recognizer.rotate_image(cropped_image, direction)` 得到旋转校正后的图像 `rotated_image`。
        d.  调用 `self.recognizer.recognize(rotated_image)` 得到文本 `text` 和置信度 `rec_confidence`。
        e.  将 `box`, `text`, `rec_confidence`, `direction` 组装成一个字典，追加到 `results` 列表。
    3.  返回 `results`。
    4.  **代码复用**: 此流程编排逻辑是对 `modules/pipelines/ocr_pipeline.py` 中 `OCRPipeline.process_image` 方法核心逻辑的忠实复刻和重组。

### `ocr_rect/classifier.py`

**a. 文件用途说明**

封装文本方向分类的原子能力，并提供其所需的图像裁剪辅助函数。

**b. 文件内类图**

```mermaid
classDiagram
    class TextClassifier {
        - config
        - onnx_session
        - pre_processor
        - post_processor
        + __init__(config: Dict)
        + classify(image: numpy.ndarray) Tuple[str, float]
    }
    <<function>> crop_image(image, box)
```

**c. 函数/方法详解**

#### `crop_image(image: numpy.ndarray, box: List) -> numpy.ndarray`

-   **用途**: 从原始图像中根据四点坐标裁剪出最小面积的外接矩形区域。
-   **代码复用**: 此函数是 `modules/utils/utils.py` 中 `get_minarea_rect_crop` 函数的直接迁移。

#### `TextClassifier.__init__(self, config: Dict)`

-   **用途**: 初始化分类器，加载模型和预/后处理器。
-   **代码复用**: 完全复用 `modules/predictors/classifier.py` 中 `TextClassifier.__init__` 的逻辑。包括：
    -   加载 ONNX 模型创建 `onnxruntime.InferenceSession`。
    -   根据配置实例化内部的预处理器（逻辑来自 `modules/pre_process/cls_pre_proc.py`）。
    -   根据配置实例化内部的后处理器（逻辑来自 `modules/post_process/cls_post_proc.py`）。

#### `TextClassifier.classify(self, image: numpy.ndarray)`

-   **用途**: 对单张已裁剪的图像进行方向分类。
-   **代码复用**: 核心逻辑复用 `modules/predictors/classifier.py` 中 `TextClassifier.classify` 的实现。流程如下：
    1.  调用 `self.pre_processor` 对输入 `image` 进行预处理。
    2.  执行 `self.onnx_session.run()` 进行模型推理。
    3.  调用 `self.post_processor` 对推理结果进行后处理，得到最终的方向和置信度。

### `ocr_rect/recognizer.py`

**a. 文件用途说明**

封装文本识别的原子能力，并提供其所需的图像旋转辅助函数。

**b. 文件内类图**

```mermaid
classDiagram
    class TextRecognizer {
        - config
        - onnx_session
        - pre_processor
        - post_processor
        + __init__(config: Dict)
        + recognize(image: numpy.ndarray) Tuple[str, float]
        - batch_inference(images: List) # 内部批量推理
    }
    <<function>> rotate_image(image, direction)
```

**c. 函数/方法详解**

#### `rotate_image(image: numpy.ndarray, direction: str) -> numpy.ndarray`

-   **用途**: 根据分类器输出的方向，将图像旋转到水平位置。
-   **代码复用**: 此函数的逻辑来自于 `modules/pre_process/rec_pre_proc.py` 中 `RecPreProcess` 类里包含的图像旋转逻辑。如果 `direction` 是 '180'，则将图像旋转180度。

#### `TextRecognizer.__init__(self, config: Dict)`

-   **用途**: 初始化识别器，加载模型和预/后处理器。
-   **代码复用**: 完全复用 `modules/predictors/recognizer.py` 中 `TextRecognizer.__init__` 的逻辑。

#### `TextRecognizer.recognize(self, image: numpy.ndarray)`

-   **用途**: 对单张已校正的图像进行文本识别，内部可能包含二次识别逻辑。
-   **代码复用**: 完全复用 `modules/predictors/recognizer.py` 中 `TextRecognizer.recognize` 的逻辑，包括对 `batch_inference` 的调用和二次旋转识别的策略。

#### `TextRecognizer.batch_inference(self, images: List)`

-   **用途**: (内部方法) 对一批图像进行批量推理。
-   **代码复用**: 完全复用 `modules/predictors/recognizer.py` 中 `TextRecognizer.batch_inference` 的逻辑。

---

## 5. 迭代演进依据

此详细设计方案严格遵循了 HLD 中确立的原则，确保了未来的可维护性和扩展性：

1.  **高内聚、低耦合**: 每个模块（`classifier.py`, `recognizer.py`）都封装了完整的原子能力及其依赖的辅助函数，独立性强。
2.  **职责清晰**: `ocr_rect.py` 只负责流程编排，不关心具体实现，使得核心业务流程一目了然。
3.  **易于替换和升级**: 如果未来需要升级方向分类模型，只需修改 `classifier.py` 内部实现，而完全不影响 `OCRRect` 的调用和其他组件，反之亦然。
