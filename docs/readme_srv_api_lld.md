API修改方案。核心思想是：**统一所有API的响应结构，使其都包含一个`results`字段，并精确匹配每个子功能所要求的输出格式。**

### 修改方案伪代码

#### 1. 修改数据模型 (`app/schemas/ocr_results.py`)

**依据**: `readme_srv_api.md` 为每个API端点都定义了精确的、嵌套在`results`对象内部的输出格式。当前的Pydantic模型要么是扁平的（如`TextRecognitionResponse`），要么字段不匹配（如`words`应为`text`，`latex_string`应为`text`），因此需要进行调整。

**伪代码修改方案**:

```python
# app/schemas/ocr_results.py (修改后 - 伪代码)

# --- 步骤 1: 定义或修改各项结果的子模型，以匹配 'readme_srv_api.md' ---

class TextDetectionItem(BaseModel):
    """文本检测结果项 (新增)"""
    bbox: List[float]
    confidence: float

class TextRecognitionItem(BaseModel):
    """单个文本识别结果项 (修改)"""
    bbox: List[float]
    # 'words' -> 'text' 以匹配文档
    text: str 
    confidence: float

class LayoutAnalysisItem(BaseModel):
    """版面分析项模型 (修改)"""
    bbox: List[float]
    type: str
    confidence: float
    # 'content' 字段在独立调用版面分析时不返回，因此设为 Optional 且默认 None
    # 在全量接口中，此字段将被填充
    content: Optional[Any] = None

class TableRecognitionItem(BaseModel):
    """表格识别结果项 (新增)"""
    bbox: List[float]
    confidence: float
    content: TableContent # 复用已有的 TableContent

class FormulaRecognitionItem(BaseModel):
    """公式识别结果项 (新增)"""
    bbox: List[float]
    # 'latex_string' -> 'text'
    text: str
    confidence: float


# --- 步骤 2: 删除不再需要的旧的、扁平化的响应模型 ---
# 将被删除: 
#   - TextDetectionResponse
#   - TextRecognitionResponse
#   - LayoutAnalysisResponse
#   - TableRecognitionResponse
#   - FormulaRecognitionResponse
#   - FormulaContent (其字段被合并到 FormulaRecognitionItem 中)


# --- 步骤 3: 复用 FullOCRResponse 作为所有API的统一响应模型 ---
# 此模型已符合 `readme_srv_api.md` 的顶层结构，无需修改
class FullOCRResponse(BaseResponse):
    """全量OCR结果响应模型"""
    results: Dict[str, Any] 
```

#### 2. 修改服务层 (`app/services/orchestration.py`)

**依据**: 服务层是数据处理和构造的核心。它必须将底层模型处理后的结果，包装成 `FullOCRResponse` 所定义的、带有 `results` 字段的结构。

**伪代码修改方案** (以 `text_recognition` 为例):

```python
# app/services/orchestration.py (修改后 - 伪代码)

# 返回类型被统一为 FullOCRResponse
def process_text_recognition(self, image_base64: str) -> FullOCRResponse:
    request_id = self.persistence.create_request(...)
    
    # 内部识别逻辑不变，它返回一个 item 列表
    # 假设 text_recognizer.recognize 返回 List[TextRecognitionItem]
    text_items = self.text_recognizer.recognize(...)

    # 关键改动：将结果包装在带有 "text_recognition" 键的字典中
    result_payload = {"text_recognition": text_items}

    return FullOCRResponse(
        request_id=request_id,
        results=result_payload
    )

# ... 所有其他 process_* 方法都遵循此模式 ...

def process_text_detection(self, image_base64: str) -> FullOCRResponse:
    # ...
    detection_items = self.text_detector.detect(...) # 返回 List[TextDetectionItem]
    return FullOCRResponse(
        request_id=...,
        results={"text_detection": detection_items}
    )
```

#### 3. 修改API层 (`app/api/endpoints/ocr.py`)

**依据**: API端点需要声明正确的响应模型 (`response_model`)，以便FastAPI能够生成正确的API文档并验证输出。所有端点现在都应声明 `FullOCRResponse`。

**伪代码修改方案** (以 `text_recognition` 为例):

```python
# app/api/endpoints/ocr.py (修改后 - 伪代码)

# 导入模型将发生变化，不再导入各种独立的 Response 模型
from app.schemas.ocr_results import OCRRequest, FullOCRResponse

# ...

# 关键改动: response_model 和函数返回类型注解
@router.post("/text_recognition", response_model=FullOCRResponse)
async def text_recognition(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """处理文本识别请求"""
    check_img_base64(request)
    # service.process_text_recognition 现在返回 FullOCRResponse
    return service.process_text_recognition(image_base64=request.image_base64)

# ... 所有其他API端点 (text_detection, layout_analysis 等) 均作类似修改 ...
```
