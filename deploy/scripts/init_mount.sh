#!/bin/bash
# 用于内网机器
# 提供一个容器外挂载目录，持久化文件将导出到此挂载
MOUNT_ROOT=/appdata
if [ -n "${MODELZOO_RESOURCE}" ]; then
  echo "found specified MODELZOO_RESOURCE: ${MODELZOO_RESOURCE}"
else
  MODELZOO_RESOURCE=modelzooapi
  echo "using default MODELZOO_RESOURCE: ${MODELZOO_RESOURCE}"
fi

# 检查并创建应用挂载子目录
APP_DATA=${MOUNT_ROOT}/${MODELZOO_RESOURCE}
if [ ! -d ${APP_DATA} ]; then
  mkdir ${APP_DATA} || exit 1
fi

if [ ! -d ${APP_DATA}/modelscope ]; then
  mkdir ${APP_DATA}/modelscope || exit 1
fi

if [ ! -d ${APP_DATA}/models ]; then
  mkdir ${APP_DATA}/models || exit 1
fi

# 创建必要的软链接挂载
APP_CACHE=/root/.cache

if [ ! -d ${APP_CACHE}/modelscope ]; then
  ln -sf ${APP_DATA}/modelscope ${APP_CACHE}/modelscope || exit 1
fi

if [ ! -d ${APP_CACHE}/huggingface ]; then
  ln -sf ${APP_DATA}/huggingface ${APP_CACHE}/huggingface || exit 1
fi

rm -rf ${WK_DIR}/models || exit 1
ln -snf ${APP_DATA}/models ${WK_DIR}/models || exit 1
