import { useMutation } from '@tanstack/react-query';
import { message } from 'antd';
import { ocrApi, OcrApiError } from '../api/ocrApi';
import { OCRResponse } from '../types/ocr';

export const useOcrAnalysis = () => {
  return useMutation<OCRResponse, Error, File>({
    mutationFn: (file: File) => ocrApi.uploadImage(file),
    onSuccess: (data) => {
      if (data.status === 'ok') {
        message.success('图片处理成功！');
      } else {
        message.error(`处理失败 [${data.status}]: ${data.error_message || '未知错误'}`);
      }
    },
    onError: (error) => {
      console.error('OCR分析失败:', error);
      
      if (error instanceof OcrApiError) {
        message.error(
          `图片处理失败 [HTTP ${error.httpCode}] [${error.statusCode}]: ${error.errorMessage}`,
          6
        );
      } else {
        message.error(error instanceof Error ? error.message : '图片处理失败，请重试');
      }
    }
  });
}; 