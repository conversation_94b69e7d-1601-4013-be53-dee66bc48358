# 系统概要设计 (HLD): OCR_RECT 模块

本文档遵循简约至上（KISS）和拒绝过度设计（YAGNI）的原则，旨在为 `OCR_RECT` 模块的重构任务提供清晰、可执行的概要设计。

---

## 1. 架构概览

系统将采用分层架构，确保职责清晰、易于维护和扩展。

- **接口层 (Interface Layer)**: 提供一个统一、简洁的对外接口。由 `OCRRect` 类实现。
- **核心逻辑层 (Core Logic Layer)**: 封装两个核心的原子能力——文本方向分类和文本识别。由 `TextClassifier` 和 `TextRecognizer` 类实现。
- **工具层 (Utility Layer)**: 提供通用的辅助功能，如图像处理。由 `utils` 模块实现。

### 核心请求流程

```mermaid
sequenceDiagram
    participant User as 调用方
    participant OCRRect as 接口层
    participant TextClassifier as 核心逻辑层 (分类)
    participant TextRecognizer as 核心逻辑层 (识别)

    User->>OCRRect: run(image, bounding_boxes)
    activate OCRRect
    loop 遍历每个 bounding_box
        Note over OCRRect, TextClassifier: OCRRect 调用与 TextClassifier 关联的辅助函数
        OCRRect->>TextClassifier: crop_image(image, box)
        activate TextClassifier
        TextClassifier-->>OCRRect: cropped_image
        deactivate TextClassifier

        OCRRect->>TextClassifier: classify(cropped_image)
        activate TextClassifier
        TextClassifier-->>OCRRect: direction, confidence
        deactivate TextClassifier

        Note over OCRRect, TextRecognizer: OCRRect 调用与 TextRecognizer 关联的辅助函数
        OCRRect->>TextRecognizer: rotate_image(cropped_image, direction)
        activate TextRecognizer
        TextRecognizer-->>OCRRect: rotated_image
        deactivate TextRecognizer

        OCRRect->>TextRecognizer: recognize(rotated_image)
        activate TextRecognizer
        TextRecognizer-->>OCRRect: text, confidence
        deactivate TextRecognizer
    end
    OCRRect-->>User: result_list
    deactivate OCRRect
```

---

## 2. 组件拆分与代码复用映射

这是本次重构任务的核心，确保所有功能均来自原始代码的忠实复用。

| 新组件 (位于 `ocr_rect` 模块) | 核心职责 | 原始代码复用来源 (位于 `ocr_inference` 项目) |
| :--- | :--- | :--- |
| **`OCRRect`** | **流程编排器 (统一入口)**<br>1. 接收图像和边框<br>2. 实例化并持有 `TextClassifier` 和 `TextRecognizer`<br>3. 编排完整的处理流程：`裁剪` -> `分类` -> `旋转` -> `识别`<br>4. 整合并返回最终结果 | **逻辑编排**: 复用 `modules/pipelines/ocr_pipeline.py` 中 `OCRPipeline.process_image` 方法的 **后半部分** 流程（即从调用 `classifier.classify` 开始的部分）。 |
| **`TextClassifier`** | **原子能力: 方向分类**<br>1. **(新增)提供图像裁剪辅助函数**<br>2. 接收裁剪后的图像块<br>3. 内部完成预处理、模型推理、后处理<br>4. 返回方向和置信度 | **图像裁剪**: 复用 `modules/utils/utils.py` 中的 `get_minarea_rect_crop` 函数，并将其作为模块内的辅助函数。<br>**初始化/推理/处理**: 复用 `modules/predictors/classifier.py`, `cls_pre_proc.py`, `cls_post_proc.py` 的逻辑。 |
| **`TextRecognizer`** | **原子能力: 文本识别**<br>1. **(新增)提供图像旋转辅助函数**<br>2. 接收 **已旋转校正** 的图像块<br>3. 内部完成预处理、批量推理、后处理<br>4. 实现可选的二次旋转识别策略<br>5. 返回文本和置信度 | **图像旋转**: 复用 `modules/pre_process/rec_pre_proc.py` 中 `RecPreProcess` 类里包含的图像旋转逻辑，并将其作为模块内的辅助函数。<br>**初始化/推理/处理**: 复用 `modules/predictors/recognizer.py`, `rec_pre_proc.py`, `rec_post_proc.py` 的逻辑。 |

---

## 3. 目录结构树

```
ocr_rect/
├── __init__.py         # 模块入口，可暴露 OCRRect 类
├── ocr_rect.py         # 定义 OCRRect 流程编排类
├── classifier.py       # 定义 TextClassifier 类及图像裁剪辅助函数
├── recognizer.py       # 定义 TextRecognizer 类及图像旋转辅助函数
├── configs/
│   └── rec_config_template.yaml # 模块配置文件模板
└── requirements.txt    # 模块的全部依赖
```

---

## 4. 数据流

**场景**: 调用方使用 `OCRRect` 模块识别一张图片中的多个文本框。

1.  **输入**: 调用方创建 `OCRRect` 实例，并调用 `run` 方法，传入 `numpy` 格式的原始图像和 `bounding_boxes` 列表。
2.  **裁剪**: `OCRRect` 遍历 `bounding_boxes`，对每个 `box`，调用定义在 `classifier.py` 模块中的辅助函数，从原始图像中提取出独立的、未校正的文本图像块 `cropped_image`。
3.  **分类**: `OCRRect` 将 `cropped_image` 传递给 `TextClassifier` 实例的 `classify` 方法。`TextClassifier` 内部执行预处理、ONNX推理和后处理，返回文本方向 `direction`。
4.  **校正**: `OCRRect` 根据返回的 `direction`，调用定义在 `recognizer.py` 模块中的辅助函数，将 `cropped_image` 旋转成 `rotated_image`。
5.  **识别**: `OCRRect` 将 `rotated_image` 传递给 `TextRecognizer` 实例的 `recognize` 方法。`TextRecognizer` 内部执行预处理、ONNX推理（可能包含多轮尝试）和后处理，返回识别文本 `text` 和置信度 `confidence`。
6.  **输出**: `OCRRect` 将 `bbox`, `direction`, `text`, `confidence` 组合成一个字典，并收集所有文本框的结果，最终返回一个结果列表给调用方。

(此流程的 Mermaid 图已在 `1. 架构概览` 中展示)

---

## 5. 数据模型设计

本模块为纯计算模块，不涉及持久化存储，因此不包含数据库实体关系设计。

---

## 6. API 接口定义

模块的公开API将通过 `OCRRect` 类提供。

- **`__init__(self, config_path: str)`**
  - **说明**: 初始化 `OCRRect` 模块。
  - **参数**: `config_path` - 指向 `rec_config.yaml` 配置文件的路径。
  - **内部操作**: 加载配置，并实例化内部的 `TextClassifier` 和 `TextRecognizer`。

- **`run(self, image: numpy.ndarray, bounding_boxes: List[List[List[int]]]) -> List[Dict]`**
  - **说明**: 执行完整的文本方向分类和识别流程。
  - **参数**:
    - `image`: `numpy` 数组格式的原始图像。
    - `bounding_boxes`: 文本框列表，每个框为 `[[x1, y1], ..., [x4, y4]]`。
  - **返回**: 一个结果列表，每个元素是包含 `bbox`, `words`, `confidence`, `direction` 的字典。

---

## 7. 迭代演进依据

此设计方案具备良好的可维护性和扩展性，原因如下：

1.  **单一职责原则**: 每个类（`OCRRect`, `TextClassifier`, `TextRecognizer`）的职责都非常明确和单一，降低了代码的复杂度和耦合度。
2.  **封装性**: `TextClassifier` 和 `TextRecognizer` 都是独立的“黑盒”，其内部复杂的预处理、后处理和模型推理逻辑被完全封装。未来如果需要更换其中任何一个的模型或算法，只需修改对应的类即可，不会影响到其他部分。
3.  **灵活性**: 由于核心能力被拆分为独立的原子组件，未来可以轻松地复用它们。例如，可以只使用 `TextClassifier` 来做一个独立的文本方向分类工具，而无需引入识别相关的代码。
