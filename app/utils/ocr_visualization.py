"""
OCR结果可视化工具模块
提供通用的OCR结果可视化函数，不依赖于特定的服务类
"""

import logging
from typing import Dict, Any, List
import re

from PIL import Image

from app.utils.visualization import (
    get_font, create_transparent_color,
    get_category_color, draw_polygon_text,
    latex_to_img, fit_img_polygon
)
from app.utils.visual_table import visual_table
from app.utils import image_locator
from app.core import errors

logger = logging.getLogger(__name__)


def _in_outerbbox(inner_bbox: List[int], outer_bbox: List[int], thre=0.65) -> bool:
    inter_poly = image_locator.inter_polygon(inner_bbox, outer_bbox)
    inner_area = image_locator.polygon_area(inner_bbox)
    if not inter_poly or not inner_area:
        return False
    inter_area = inter_poly.area / inner_area
    return inter_area > thre

def generate_text_detection_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成文字检测的可视化结果
    
    以原图为画布，绘制bbox和透明蒙版
    
    Args:
        original_img: 原始图像
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
    """
    img = original_img.copy().convert('RGBA')
    font = get_font(font_size=12)

    # 获取文字检测结果
    text_detection = ocr_result.get('text_detection', [])
    for item in text_detection:
        bbox = item.get('bbox', [])
        confidence = item.get('confidence', 0.0)
        line_color = create_transparent_color('red', 0.8)
        bg_color = create_transparent_color('red', 0.1)
        label = f"{confidence:.2f}" if confidence else None

        draw_polygon_text(
            img=img,
            quad_points=bbox,
            bg_color=bg_color,
            line_color=line_color,
            line_width=2,
            font=font,
            label=label,
            label_color=create_transparent_color('red', 0.9)
        )

    return img.convert('RGB')


def generate_text_recognition_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成文字识别的可视化结果
    
    以相同size的白色图为画布，绘制在bbox相应位置绘制文字和边框，不需要透明蒙版
    
    Args:
        original_img: 原始图像（用于获取尺寸）
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
    """
    # 创建白色背景图像
    img = Image.new('RGBA', original_img.size, 'white')
    font = get_font(font_size=14)

    # 获取文字识别结果
    text_recognition = ocr_result.get('text_recognition', [])
    for item in text_recognition:
        bbox = item.get('bbox', [])
        text = item.get('text', {}).get('value', '')

        if text:
            draw_polygon_text(
                img=img,
                quad_points=bbox,
                bg_color=None,  # 无背景色
                line_color=create_transparent_color('red', 0.8),
                font=font,
                text=text,
                text_color=(0, 0, 0),  # 黑色文字
            )

    return img.convert('RGB')


def generate_layout_analysis_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成版面分析的可视化结果
    
    以原图为画布，绘制bbox和覆盖透明蒙版
    
    Args:
        original_img: 原始图像
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
    """
    img = original_img.copy().convert('RGBA')
    font = get_font(font_size=12)

    # 获取版面分析结果
    layout_analysis = ocr_result.get('layout_analysis', [])
    for item in layout_analysis:
        bbox = item.get('bbox', [])
        layout_type = item.get('type', 'unknown')
        confidence = item.get('confidence', 0.0)

        # 获取该类型对应的颜色
        color_name = get_category_color(layout_type)
        # 创建半透明颜色
        bg_color = create_transparent_color(color_name, 0.3)
        line_color = create_transparent_color(color_name, 0.8)

        # 绘制带标签的矩形区域
        label = f"{layout_type} ({confidence:.2f})" if confidence else layout_type
        draw_polygon_text(
            img=img,
            quad_points=bbox,
            bg_color=bg_color,
            line_color=line_color,
            line_width=2,
            font=font,
            label=label,
            label_color=create_transparent_color(color_name, 0.9)
        )

    return img.convert('RGB')


def generate_table_recognition_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成表格识别的可视化结果
    
    以相同size的白色图为画布，绘制表格外边框、单元格边框、单元格内文字
    
    Args:
        original_img: 原始图像（用于获取尺寸）
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
    """
    # 创建白色背景图像
    img = Image.new('RGBA', original_img.size, 'white')

    # 获取表格识别结果
    table_recognition = ocr_result.get('table_recognition', [])
    for table_item in table_recognition:
        visual_table(table_item, img, show_text=True)

    return img.convert('RGB')

def render_formula(img, bbox, text, show_box):
    if not text:
        return
    # 将LaTeX公式渲染成图像
    latex = re.sub(rf'(\s*([{re.escape("_^{}()[]")}])\s*)', lambda m: m.group(2), text)  # 去掉特定字符前后空格
    latex = latex.replace(r"\(", "(").replace(r"\)", ")").replace(r"\[", "[").replace(r"\]", "]")
    latex = re.sub(r"\\big", "", latex, flags=re.IGNORECASE)
    latex = f"$ {latex} $"
    try:
        latex_img = latex_to_img(latex)
    except Exception as e:
        logging.info(f"公式{text}渲染错误: {e}")
    else:
        polygon = image_locator.to_polygon(bbox)
        # 将LaTeX图像缩放到bbox大小
        fitted_img = fit_img_polygon(latex_img, polygon)

        if fitted_img:
            text = None
            # 计算粘贴位置（多边形的最小外接矩形的左上角）
            xs = [p[0] for p in polygon]
            ys = [p[1] for p in polygon]
            paste_x, paste_y = int(min(xs))+5, int(min(ys))+5
            # 将缩放后的LaTeX图像粘贴到目标图像上
            if fitted_img.mode == 'RGBA':
                img.paste(fitted_img, (paste_x, paste_y), fitted_img)
            else:
                img.paste(fitted_img, (paste_x, paste_y))

    if show_box:
    # 可选：绘制边框和标签
        draw_polygon_text(
            img=img,
            quad_points=bbox,
            bg_color=None,
            line_color=create_transparent_color('yellow', 0.5),
            line_width=1,
            text=text,
            font=get_font(),
            label=f"Formula",
            label_color=create_transparent_color('yellow', 0.8)
        )
            
def generate_formula_recognition_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成公式识别的可视化结果

    以相同size的白色图为画布，将公式渲染成LaTeX图像，然后缩放到公式bbox大小贴到目标图中

    Args:
        original_img: 原始图像（用于获取尺寸）
        ocr_result: OCR结果数据

    Returns:
        可视化结果图像
    """
    # 创建白色背景图像
    img = Image.new('RGBA', original_img.size, 'white')
    font = get_font(font_size=14)

    # 获取公式识别结果
    formula_recognition = ocr_result.get('formula_recognition', [])
    for item in formula_recognition:
        bbox = item.get('bbox', [])
        text = item.get('text', {}).get('value', '')
        render_formula(img, bbox, text, True)

    return img.convert('RGB')


def generate_full_result_visualization(original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成全量OCR结果的可视化
    
    以相同size的白色图为画布，绘制所有内容
    
    Args:
        original_img: 原始图像
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
    """
    # 创建白色背景图像
    img = Image.new('RGBA', original_img.size, 'white')
    font = get_font(font_size=12)

    # 1. 首先处理版面分析结果
    layout_analysis = ocr_result.get('layout_analysis', [])
    formula_bboxes = []
    for item in layout_analysis:
        bbox = item.get('bbox', [])
        layout_type = item.get('type', 'unknown')

        # 获取该类型对应的颜色
        color_name = get_category_color(layout_type)

        # 对于image和unknown类型，从原图中裁剪并贴到相应位置
        layout_type_lower = layout_type.lower()
        if layout_type_lower in ('image', 'figure', 'unknown'):
            # bbox现在是8点格式 [x1, y1, x2, y2, x3, y3, x4, y4]
            # 计算最小外接矩形
            x_coords = [bbox[0], bbox[2], bbox[4], bbox[6]]
            y_coords = [bbox[1], bbox[3], bbox[5], bbox[7]]
            x1, y1, x2, y2 = min(x_coords), min(y_coords), max(x_coords), max(y_coords)
            x1, y1, x2, y2 = tuple(map(int, [x1, y1, x2, y2]))
            # 裁剪原图对应区域
            crop_img = original_img.crop((x1, y1, x2, y2))
            # 粘贴到结果图上
            img.paste(crop_img, (x1, y1))

            # 添加半透明边框和标签
            line_color = create_transparent_color(color_name, 0.8)
            draw_polygon_text(
                img=img,
                quad_points=bbox,
                bg_color=None,
                line_color=None,
                line_width=0,
                font=font,
                label=None, # label=layout_type,
                label_color=None # create_transparent_color(color_name, 0.9)
            )

        # 对于表格类型
        elif layout_type_lower == 'table':
            item["bbox"] = []
            visual_table(item, img, show_text=False)

        # 对于公式类型
        elif layout_type_lower == 'formula':
            formula_text = item.get('text', {}).get('value', '')
            if formula_text:
                formula_bboxes.append(bbox)
                render_formula(img, bbox, formula_text, False)

        # 对于其他类型，绘制半透明蒙版
        else:
            bg_color = create_transparent_color(color_name, 0.1)
            line_color = create_transparent_color(color_name, 0.8)
            draw_polygon_text(
                img=img,
                quad_points=bbox,
                bg_color=None,
                line_color=None,
                line_width=0,
                font=font,
                label=None, #layout_type,
                label_color=None #create_transparent_color(color_name, 0.9)
            )

    # 2. 处理文本识别结果
    text_recognition = ocr_result.get('text_recognition', [])
    for item in text_recognition:
        bbox = item['bbox']
        text = item.get('text', {}).get('value', '')

        # 如果不是特殊区域，绘制文本
        if text:
            in_formula = (_in_outerbbox(bbox, formula_bbox) for formula_bbox in formula_bboxes)
            if any(in_formula):
                continue
            draw_polygon_text(
                img=img,
                quad_points=bbox,
                bg_color=None,
                line_color=None,
                line_width=0,
                font=font,
                text=text,
                text_color=(0, 0, 0)
            )

    return img.convert('RGB')


# 可视化函数映射
VISUALIZATION_FUNCTIONS = {
    'text_detection': generate_text_detection_visualization,
    'text_recognition': generate_text_recognition_visualization,
    'layout_analysis': generate_layout_analysis_visualization,
    'table_recognition': generate_table_recognition_visualization,
    'formula_recognition': generate_formula_recognition_visualization,
    'full_result': generate_full_result_visualization,
}


def generate_ocr_visualization(view_type: str, original_img: Image.Image, ocr_result: Dict[str, Any]) -> Image.Image:
    """生成OCR结果的可视化图像
    
    Args:
        view_type: 可视化类型
        original_img: 原始图像
        ocr_result: OCR结果数据
        
    Returns:
        可视化结果图像
        
    Raises:
        ValueError: 当view_type不支持时
    """
    if view_type not in VISUALIZATION_FUNCTIONS:
        raise errors.ClientErr(errors.ErrCode.CLIENT_BAD_PARAMS, f"error type: {view_type}", f"不支持的可视化类型: {view_type}，支持的类型: {list(VISUALIZATION_FUNCTIONS.keys())}")

    visualization_func = VISUALIZATION_FUNCTIONS[view_type]
    return visualization_func(original_img, ocr_result)
