import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Spin, Alert, Image, Descriptions } from 'antd';
import { ocrApi, OcrApiError } from '../api/ocrApi';
import { ViewType } from '../types/ocr';
import axios from 'axios';

interface VisualizationViewerProps {
  requestId: string;
  viewType: ViewType;
}

export const VisualizationViewer: React.FC<VisualizationViewerProps> = ({ requestId, viewType }) => {
  const { data: imageUrl, isPending, isError, error } = useQuery({
    queryKey: ['visualization', requestId, viewType],
    queryFn: () => ocrApi.getVisualizationImage(requestId, viewType),
    enabled: !!requestId && !!viewType,
    retry: false, // 错误时不要重试
  });

  if (isPending) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>生成可视化结果中...</div>
      </div>
    );
  }

  if (isError) {
    // 解析错误信息
    let httpStatusCode = '未知';
    let apiStatusCode = '未知';
    let errorMessage = '未知错误';
    let requestId = '';
    
    // 确保直接使用我们自定义的OCR API错误类
    if (error instanceof OcrApiError) {
      httpStatusCode = error.httpCode.toString();
      apiStatusCode = error.statusCode;
      errorMessage = error.errorMessage;
      requestId = error.requestId || '';
    } else if (axios.isAxiosError(error)) {
      httpStatusCode = error.response?.status?.toString() || '未知';
      
      try {
        const data = error.response?.data;
        if (data) {
          // 尝试提取错误信息
          apiStatusCode = data.status || '未知';
          errorMessage = data.error_message || error.message || '未知错误';
          requestId = data.request_id || '';
        }
      } catch (e) {
        console.error('解析错误对象失败:', e);
        errorMessage = error.message || '未知错误';
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return (
      <Alert
        message="生成失败"
        description={
          <Descriptions column={1} size="small" bordered>
            <Descriptions.Item label="HTTP状态码">{httpStatusCode}</Descriptions.Item>
            <Descriptions.Item label="API状态码">{apiStatusCode}</Descriptions.Item>
            <Descriptions.Item label="错误信息">{errorMessage}</Descriptions.Item>
            {requestId && <Descriptions.Item label="请求ID">{requestId}</Descriptions.Item>}
          </Descriptions>
        }
        type="error"
        showIcon
      />
    );
  }

  if (!imageUrl) {
    return (
      <Alert
        message="暂无结果"
        description="可视化结果正在生成中，请稍后"
        type="info"
        showIcon
      />
    );
  }

  return (
    <div style={{ textAlign: 'center' }}>
      <Image 
        src={imageUrl}
        alt={`Visualization Result - ${viewType}`}
        style={{ maxWidth: '100%', maxHeight: '400px' }}
        preview={{
          mask: '点击查看大图',
        }}
      />
    </div>
  );
}; 