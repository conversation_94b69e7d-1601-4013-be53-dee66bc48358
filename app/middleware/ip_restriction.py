import logging
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import ipaddress
from app.core.config import settings
from app.core import errors

logger = logging.getLogger(__name__)

def get_client_ip(request: Request) -> str:
    """获取客户端真实IP地址"""
    # 优先从X-Forwarded-For头获取（适用于代理/负载均衡器场景）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，取第一个
        return forwarded_for.split(",")[0].strip()
    
    # 从X-Real-IP头获取（Nginx代理常用）
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # 最后使用直连IP
    return request.client.host if request.client else "unknown"

def check_ip_allowed(client_ip: str) -> bool:
    """检查IP是否在允许列表中"""
    if not settings.WEB_API_IP_RESTRICTION_ENABLED:
        return True
    
    allowed_ips = settings.WEB_API_ALLOWED_IPS
    
    # 处理localhost的各种表示形式
    if client_ip in ["127.0.0.1", "::1", "localhost"]:
        return any(ip in ["127.0.0.1", "::1", "localhost"] for ip in allowed_ips)
    
    # 检查是否直接匹配单个IP
    if client_ip in allowed_ips:
        return True
        
    # 检查是否在允许的CIDR网段内
    client_ip_obj = ipaddress.ip_address(client_ip)
    for allowed_ip in allowed_ips:
        # 检查是否是CIDR格式
        if "/" in allowed_ip:
            network = ipaddress.ip_network(allowed_ip, strict=False)
            if client_ip_obj in network:
                return True
        
    return False

async def ip_restriction_middleware(request: Request, call_next):
    """IP限制中间件 - 仅对前端专用API生效"""
    # 检查是否是前端专用API路径
    url_path = request.url.path # "/api/v1/web/" , "/api/v1/health"...
    path_segs = url_path.split("/")
    if len(path_segs) < 3 or path_segs[2] == "web" or path_segs[2] == "health":
        client_ip = get_client_ip(request)
        
        if not check_ip_allowed(client_ip):
            logger.warning(f"IP访问被拒绝: {client_ip} 尝试访问 {request.url.path}")
            return JSONResponse(
                status_code=403,
                content={
                    "request_id": "",
                    "status": errors.ErrCode.CLIENT_FORBIDDEN,
                    "error_message": f"Access denied for IP: {client_ip}",
                    "results": None
                }
            )
        
        logger.info(f"IP访问允许: {client_ip} 访问 {request.url.path}")
    
    response = await call_next(request)
    return response
