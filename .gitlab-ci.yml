image: golang:1.19

variables:
  VOLC_DEPLOY_CLS: hsyq-bj-common-01
  ALI_TEST_CONTAINER_IMAGE: harbor-aliyun.zhhainiao.com/aicamera/agileocr-test

  # 默认拉取submodule
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - check
  - build
  - deploy

cache:
  paths:
    - .cache

before_script:
  - >-
    export GO111MODULE=on &&
    mkdir -p .cache/gopath &&
    export GOPATH="${CI_PROJECT_DIR}/.cache/gopath" &&
    export GOPROXY=https://goproxy.cn,direct &&
    export GOFLAGS=-mod=vendor &&
    export GOPRIVATE=*.cmcm.com,gopkg.gitlab.liebaopay.com

include:
  - local: "/deploy/cicd/test.yml"

