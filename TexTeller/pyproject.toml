[build-system]
requires = ["hatchling", "hatch-vcs"]
build-backend = "hatchling.build"

[project]
name = "texteller"
authors = [
    { name="OleehyO", email="<EMAIL>" }
]
dynamic = ["version"]
description = "Texteller is a tool for converting rendered image to original latex code"
readme = "README.md"
license = { file = "LICENSE" }
requires-python = ">=3.10"
dependencies = [
    "click>=8.1.8",
    "colorama>=0.4.6",
    "opencv-python-headless>=*********",
    "pyclipper>=1.3.0.post6",
    "shapely>=2.1.0",
    "streamlit>=1.44.1",
    "streamlit-paste-button>=0.1.2",
    "torch>=2.6.0",
    "torchvision>=0.21.0",
    "transformers==4.47",
    "wget>=3.2",
    "optimum[onnxruntime]>=1.24.0",
    "python-multipart>=0.0.20",
    "ray[serve]>=2.44.1",
]

[tool.hatch.version]
source = "vcs"

[tool.ruff]
exclude = [".git", ".mypy_cache", ".ruff_cache", ".venv", "dist"]
target-version = "py310"
line-length = 100

[tool.ruff.format]
line-ending = "lf"
quote-style = "double"

[tool.ruff.lint]
select = ["E", "W"]
ignore = [
    "E999",
    "EXE001",
    "UP009",
    "F401",
    "TID252",
    "F403",
    "F841",
    "E501",
    "W291",
    "W293",
    "E741",
    "E712",
]

[tool.hatch.build.targets.wheel]
packages = ["texteller"]

[project.scripts]
texteller = "texteller.cli:cli"

[project.optional-dependencies]
onnxruntime-gpu = [
    "onnxruntime-gpu>=1.21.0",
]
test = [
    "pytest>=8.3.5",
]
train = [
    "accelerate>=1.6.0",
    "augraphy>=8.2.6",
    "datasets>=3.5.0",
    "tensorboardx>=*******",
]
docs = [
    "myst-parser>=4.0.1",
    "nbsphinx>=0.9.7",
    "sphinx>=8.1.3",
    "sphinx-book-theme>=1.1.4",
    "sphinx-copybutton>=0.5.2",
    "sphinx-design>=0.6.1",
]
