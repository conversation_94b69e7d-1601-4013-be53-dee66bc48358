# **系统概要设计 (High-Level Design): OCR 全流程处理系统**

## 1. 架构概览 (Architecture Overview)

本系统采用经典的三层单体架构，并引入了一个轻量级的持久化服务，以确保职责分离、满足数据持久化需求和未来的可维护性。

  * **表示层 (Presentation Layer)**: 由 **API 网关 (FastAPI)** 构成。它负责处理 `application/json` 类型的 HTTP 请求，包括解码 Base64 编码的图像数据、验证输入、路由请求，并提供一个 `GET` 接口用于查询历史结果。
  * **应用层 (Application/Service Layer)**: 核心是 **编排服务 (Orchestration Service)**。该服务是系统的“大脑”，它负责生成唯一的 `request_id`，根据请求类型调用一个或多个处理节点，管理业务流程，并在处理成功后调用持久化服务保存结果。
  * **处理层 (Processing Layer)**: 包含五个独立的 **OCR 处理服务节点**。每个节点封装一个特定的 AI 模型，执行核心计算任务。
  * **持久化层 (Persistence Layer)**: 由**结果持久化服务**构成。在 MVP 设计中，它负责将处理完成的 JSON 结果以文件形式存储在服务器本地磁盘上，并提供按 ID 读取的功能。

以下 `sequenceDiagram` 展示了系统最核心的端到端请求流程（以“最终组合结果”为例），包含了从请求接收到结果持久化的全过程。

```mermaid
sequenceDiagram
    participant User as 用户/客户端
    participant APIGateway as API 网关 (FastAPI)
    participant OrchestrationService as 编排服务
    participant ProcessingServices as 各OCR处理服务
    participant PersistenceService as 结果持久化服务

    User->>+APIGateway: POST /api/v1/ocr/full_result (JSON with base64 image)
    APIGateway->>APIGateway: 解码 Base64 图像数据
    APIGateway->>+OrchestrationService: process_full_result(image_data)
    OrchestrationService->>OrchestrationService: 生成唯一 request_id
    OrchestrationService->>+ProcessingServices: 按预定路径调用各处理服务...
    ProcessingServices-->>-OrchestrationService: 返回各项中间结果
    OrchestrationService->>OrchestrationService: 组合所有结果为最终JSON (包含request_id)
    OrchestrationService->>+PersistenceService: save_result(request_id, final_json)
    PersistenceService-->>-OrchestrationService: 确认保存成功
    OrchestrationService-->>-APIGateway: 返回最终JSON结果
    APIGateway-->>-User: 200 OK (application/json)
```

## 2. 组件拆分 (Components)

  * **表示层 (Presentation Layer)**

      * **API 网关 (API Gateway - FastAPI)**:
          * **职责**:
              * 定义所有对外 API 端点，包括 `POST /api/v1/ocr/*` 和 `GET /api/v1/results/{request_id}`。
              * 处理 `application/json` 类型的 HTTP 请求，使用 Pydantic 模型进行严格的数据验证。
              * 解码请求体中 `image_base64` 字段的字符串，将其还原为可供处理的二进制图像数据。
              * 调用应用层服务来处理业务逻辑。
              * 提供静态文件服务，用于托管编译后的 React 前端应用。

  * **应用层 (Application/Service Layer)**

      * **编排服务 (Orchestration Service)**:
          * **职责**:
              * 在处理新请求时，使用 `uuid` 库生成一个全局唯一的 `request_id`。
              * 根据 API 请求类型，按 PRD 中定义的逻辑路径，按顺序调用一个或多个处理层服务。
              * 管理中间数据的流转（例如，将文字定位的结果传递给文字识别服务）。
              * 调用结果格式化器，将各个处理节点的输出整合成最终的、符合规范的 JSON 结构。
              * 在流程成功结束后，调用持久化服务，将携带 `request_id` 的最终结果进行保存。

  * **处理层 (Processing Layer)**

      * **文字定位服务 (Text Detection Service)**:
          * **职责**: 封装文字定位模型。输入一张图片，输出所有检测到的文本行的包围盒（bbox）列表。
      * **文字识别服务 (Text Recognition Service)**:
          * **职责**: 封装文字识别模型。输入一张图片和一组包围盒，输出每个包围盒对应的文本内容和置信度。
      * **版面分析服务 (Layout Analysis Service)**:
          * **职责**: 封装版面分析模型。输入一张图片，输出页面中不同逻辑区域（如标题、段落、表格、公式）的类型和包围盒。
      * **表格识别服务 (Table Recognition Service)**:
          * **职责**: 封装表格识别模型。输入一张图片和一个表格区域的包围盒，输出结构化的表格内容（单元格、行列信息、文本）。
      * **公式识别服务 (Formula Recognition Service)**:
          * **职责**: 封装公式识别模型。输入一张图片和一个公式区域的包围盒，输出识别出的 LaTeX 字符串。

  * **持久化层 (Persistence Layer)**

      * **结果持久化服务 (Result Persistence Service)**:
          * **职责**:
              * 提供 `save(request_id, json_data)` 方法，将 OCR 结果序列化为 JSON 文件，并以 `{request_id}.json` 的形式存储在服务器的指定目录（如 `./results/`）下。
              * 提供 `get(request_id)` 方法，根据 `request_id` 从文件系统中读取对应的 JSON 文件并返回其内容。若文件不存在，则抛出未找到异常。

## 3. 目录结构树 (Directory Tree)

```
ocr_system/
├── app/                  # FastAPI 应用核心代码
│   ├── __init__.py
│   ├── main.py           # FastAPI 应用入口，Uvicorn 启动点
│   │
│   ├── api/              # API 表示层
│   │   ├── __init__.py
│   │   └── endpoints/
│   │       ├── __init__.py
│   │       ├── ocr.py      # 定义所有 POST /api/v1/ocr/* 路由
│   │       └── results.py  # 定义 GET /api/v1/results/{request_id} 路由
│   │
│   ├── services/         # 应用层
│   │   ├── __init__.py
│   │   ├── orchestration.py # 核心编排服务逻辑
│   │   └── persistence.py   # 结果持久化服务逻辑
│   │
│   ├── processing/       # 处理层，封装模型调用
│   │   ├── __init__.py
│   │   ├── text_detection.py
│   │   ├── text_recognition.py
│   │   ├── layout_analysis.py
│   │   ├── table_recognition.py
│   │   └── formula_recognition.py
│   │
│   ├── schemas/          # Pydantic 数据模型
│   │   ├── __init__.py
│   │   └── ocr_results.py # 定义 API 输入和输出的 JSON 结构
│   │
│   └── core/             # 共享配置、工具等
│       ├── __init__.py
│       └── config.py     # 应用配置 (如结果存储路径)
│
├── results/              # 存放OCR结果的JSON文件
│   └── .gitignore        # (重要) 必须添加此文件，内容为 "*", 确保结果文件不被提交到git
│
├── static/               # 存放编译后的 React 前端文件
│   ├── index.html
│   └── assets/
│
├── tests/                # 测试代码
│   ├── __init__.py
│   └── test_api.py
│
├── poetry.lock
└── pyproject.toml
```

## 4. 数据流 (Data Flow)

#### 4.1 场景一：生成并存储“最终组合结果”

此场景描述了用户提交一张图片，请求最完整处理结果的数据流。

```mermaid
sequenceDiagram
    participant APIGateway as API 网关
    participant OrchestrationService as 编排服务
    participant TextDetectionService as 文字定位
    participant TextRecognitionService as 文字识别
    participant LayoutAnalysisService as 版面分析
    participant TableRecognitionService as 表格识别
    participant FormulaRecognitionService as 公式识别
    participant PersistenceService as 结果持久化服务

    APIGateway->>+OrchestrationService: process_full_result(image)
    Note over OrchestrationService: 启动“最终组合结果”处理流程
    
    OrchestrationService->>+TextDetectionService: detect_text(image)
    TextDetectionService-->>-OrchestrationService: text_bboxes
    OrchestrationService->>+TextRecognitionService: recognize_text(image, text_bboxes)
    TextRecognitionService-->>-OrchestrationService: recognized_texts
    
    OrchestrationService->>+LayoutAnalysisService: analyze_layout(image)
    LayoutAnalysisService-->>-OrchestrationService: layout_blocks (含表格和公式区域)
    
    loop 对每个版面区块 (layout_block)
        alt 如果区块类型是 'table'
            OrchestrationService->>+TableRecognitionService: recognize_table(image, block.bbox)
            TableRecognitionService-->>-OrchestrationService: structured_table_content
        else 如果区块类型是 'formula'
            OrchestrationService->>+FormulaRecognitionService: recognize_formula(image, block.bbox)
            FormulaRecognitionService-->>-OrchestrationService: formula_latex_string
        end
    end
    
    OrchestrationService->>OrchestrationService: 整合所有结果为最终JSON
    OrchestrationService->>+PersistenceService: save_result(request_id, final_json)
    PersistenceService-->>-OrchestrationService: 保存成功
    OrchestrationService-->>-APIGateway: 返回最终JSON
```

#### 4.2 场景二：根据ID重新获取OCR结果

此场景描述了客户端（如刷新后的 Web 页面或开发者脚本）根据已有的 `request_id` 查询历史结果的数据流。

```mermaid
sequenceDiagram
    participant Client as 独立客户端
    participant APIGateway as API 网关 (FastAPI)
    participant PersistenceService as 结果持久化服务

    Client->>+APIGateway: GET /api/v1/results/a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8
    APIGateway->>+PersistenceService: get_result("a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8")
    Note over PersistenceService: 从文件系统读取 a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8.json 文件
    PersistenceService-->>-APIGateway: 返回文件内容 (JSON 字符串)
    APIGateway-->>-Client: 200 OK (application/json)
```

## 5. 数据模型设计 (Data Model Design)

此实体关系图（ERD）用于描述被持久化存储到文件系统的 **JSON 对象的逻辑结构与关系**。

```mermaid
erDiagram
    SAVED_REQUEST {
        string request_id PK "Unique identifier for the request"
    }

    SAVED_REQUEST ||--|{ OCR_RESULT : "has one"

    OCR_RESULT {
        string request_id FK "Matches the request ID"
        string status "e.g., 'ok' or 'error'"
        string error_message "null if status is 'ok'"
    }

    OCR_RESULT ||--o{ TEXT_RECOGNITION_ITEM : "contains"
    OCR_RESULT ||--o{ LAYOUT_ANALYSIS_ITEM : "contains"

    TEXT_RECOGNITION_ITEM {
        array bbox "Bounding box [x1, y1, x2, y2]"
        string words "Recognized text string"
        float confidence "Confidence score"
    }

    LAYOUT_ANALYSIS_ITEM {
        array bbox "Bounding box of the layout block"
        string type "e.g., 'title', 'table', 'formula'"
        float confidence "Confidence score of layout type"
    }

    LAYOUT_ANALYSIS_ITEM ||--o| TABLE_CONTENT : "can contain"
    LAYOUT_ANALYSIS_ITEM ||--o| FORMULA_CONTENT : "can contain"

    TABLE_CONTENT {
        float confidence "Overall confidence of table structure"
    }

    TABLE_CONTENT ||--|{ TABLE_CELL : "has many"

    TABLE_CELL {
        array bbox
        int row_start
        int col_start
        int row_span
        int col_span
        string text
        float confidence
    }

    FORMULA_CONTENT {
        string latex_string "Recognized LaTeX string"
        float confidence "Confidence of formula recognition"
    }
```

## 6. API接口定义 (API Definitions)

#### 6.1 核心处理接口 (POST)

  * **通用请求体格式**: 所有 `POST` 接口的请求体均为 `application/json`，格式如下：
    ```json
    {
      "image_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD...",
      "filename": "report.jpg"
    }
    ```
  * **通用成功响应**: 所有 `POST` 接口成功后返回的 JSON 结构与 PRD 中定义的一致，并必定包含顶级的 `request_id` 字段。

| 请求方法 | 端点路径                               | 简要说明                                                               |
| :------- | :------------------------------------- | :--------------------------------------------------------------------- |
| `POST`   | `/api/v1/ocr/text_detection`           | 上传图片，仅执行文字定位，保存并返回结果。                             |
| `POST`   | `/api/v1/ocr/text_recognition`         | 上传图片，执行文字定位和识别，保存并返回结果。                         |
| `POST`   | `/api/v1/ocr/layout_analysis`          | 上传图片，仅执行版面分析（不填充内容），保存并返回结果。                 |
| `POST`   | `/api/v1/ocr/table_recognition`        | 上传图片，执行版面分析和表格结构化识别，保存并返回结果。               |
| `POST`   | `/api/v1/ocr/formula_recognition`      | 上传图片，执行版面分析和公式识别，保存并返回结果。                     |
| `POST`   | `/api/v1/ocr/full_result`              | 上传图片，执行所有流程，将所有信息组合后，保存并返回最完整的结果。     |

#### 6.2 结果查询接口 (GET)

| 请求方法 | 端点路径                       | 简要说明                                               |
| :------- | :----------------------------- | :----------------------------------------------------- |
| `GET`    | `/api/v1/results/{request_id}` | 根据ID获取一个之前已处理并成功存储的OCR结果的完整JSON。 |

## 7. 关键决策的依据 (Rationale for Key Decisions)

1.  **决策: 采用单体架构 (Monolithic Architecture)**

      * **依据**: 这是项目初期的核心要求，旨在降低复杂度。对于 MVP 阶段，单体架构是最佳选择。它极大地简化了开发、调试、部署和测试的流程。所有代码都在一个代码库和一个进程中，避免了分布式系统带来的网络延迟、服务发现、数据一致性等复杂问题。当未来系统规模和团队扩大时，可以基于当前清晰的组件划分，逐步将 `processing` 层的服务重构为独立的微服务。

2.  **决策: 引入中心化的编排服务 (Orchestration Service)**

      * **依据**: PRD 中定义了多种、有条件的、复杂的处理路径。如果将这些流程逻辑分散在各个 API 端点处理函数中，会导致代码重复、逻辑混乱且难以维护。创建一个专门的 `OrchestrationService` 可以将这些业务流程逻辑集中管理，使得 API 层只负责“接线”，处理层只负责“计算”，实现了关注点分离（Separation of Concerns），让整个系统结构更清晰、更易于扩展新的处理路径。

3.  **决策: 采用文件系统进行持久化**

      * **依据**: 该决策是为了满足“重新获取结果”和“Web刷新后数据不丢失”的需求，同时严格遵守“MVP版本不需要数据库”的约束。使用本地文件系统是实现持久化最简单、最轻量级的方式，它没有外部依赖，易于实现和部署。
      * **未来展望与风险**: 此方案不适用于多实例集群部署（因为结果文件只存在于处理该请求的特定实例上）。当系统需要横向扩展时，持久化层应替换为网络附加存储（如 NFS）、对象存储（如 MinIO/S3）或真正的文档数据库（如 MongoDB，它非常适合存储JSON文档）。

4.  **决策: API 输入改为 JSON (含 Base64 图像)**

      * **依据**: 这是明确的业务需求，旨在统一接口规范。将 API 统一为 `application/json` 可以简化客户端的请求逻辑，所有交互都使用同一种 `Content-Type`。这在程序化调用时尤其方便，无需处理复杂的 `multipart/form-data` 边界。
      * **风险与权衡**: Base64 编码会使图像数据体积增大约 33%。对于非常大的图片，这会增加网络传输的负载和服务器端解码的 CPU 开销。在当前 MVP 阶段此影响可接受，但在高吞吐量或对延迟敏感的生产环境中，需要评估其性能影响。

5.  **决策: Web 前端使用 `localStorage` 保持状态**

      * **依据**: 这是实现“刷新后数据不丢失”功能最标准、最高效的前端方案。后端通过在响应中提供唯一的 `request_id` 来支持此功能。当前端成功获取 OCR 结果后，它会将 `request_id`、完整的 JSON 结果以及图像数据（如用户上传时的 Base64）存入浏览器的 `localStorage`。页面加载时，前端脚本会首先检查 `localStorage`，如果存在数据，则直接用它们来恢复页面状态，实现了纯客户端的状态保持，降低了服务端复杂性。