FROM harbor-aliyun.zhhainiao.com/aicamera/aienv:agileocr-v0.1.0

ENV LANG zh_CN.UTF-8
ENV WK_DIR=/mnt/ocr_service/agileocr

WORKDIR $WK_DIR

#RUN pip install -i https://download.pytorch.org/whl/cu124 \
#    torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 \
#    && rm -rf /root/.cache/pip \
#    && rm -rf /tmp/*

RUN pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple oss2==2.19.1 logzero==1.7.0 \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

COPY . .

# 设置脚本权限
RUN chmod +x -R deploy/scripts

# TODO

# 暴露端口
EXPOSE 6000 5000

# 设置入口点
ENTRYPOINT ["./deploy/scripts/launch_api.sh"]
