from fastapi import APIRouter, Depends

from app.core import errors
from app.schemas.ocr_results import (
    OCRRequest, FullOCRResponse
)
from app.services.orchestration import OrchestrationService
from app.services.injector import get_orchestration_service

# 创建路由器
router = APIRouter()

def check_image_input(request):
    """检查图像输入，确保至少提供image_url或image_base64之一"""
    if not request.image_url and not request.image_base64:
        raise errors.ClientErr(errors.ErrCode.CLIENT_IMAGE, "必须提供image_url或image_base64之一")
    
@router.post("/text_detection", response_model=FullOCRResponse)
async def web_text_detection(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用文本检测API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 文本检测结果，包含请求ID和检测到的边界框
    """
    check_image_input(request)
    return service.process_text_detection(image_base64=request.image_base64, image_url=request.image_url, persist=True)

@router.post("/text_recognition", response_model=FullOCRResponse)
async def web_text_recognition(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用文本识别API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 文本识别结果，包含请求ID和识别到的文本内容
    """
    check_image_input(request)
    return service.process_text_recognition(image_base64=request.image_base64, image_url=request.image_url, persist=True)

@router.post("/layout_analysis", response_model=FullOCRResponse)
async def web_layout_analysis(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用版面分析API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 版面分析结果，包含请求ID和版面分析项
    """
    check_image_input(request)
    return service.process_layout_analysis(image_base64=request.image_base64, image_url=request.image_url, persist=True)

@router.post("/table_recognition", response_model=FullOCRResponse)
async def web_table_recognition(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用表格识别API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 表格识别结果，包含请求ID和表格结构
    """
    check_image_input(request)
    return service.process_table_recognition(image_base64=request.image_base64, image_url=request.image_url, persist=True)

@router.post("/formula_recognition", response_model=FullOCRResponse)
async def web_formula_recognition(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用公式识别API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 公式识别结果，包含请求ID和LaTeX公式
    """
    check_image_input(request)
    return service.process_formula_recognition(image_base64=request.image_base64, image_url=request.image_url, persist=True)

@router.post("/full_result", response_model=FullOCRResponse)
async def web_full_ocr(
    request: OCRRequest,
    service: OrchestrationService = Depends(get_orchestration_service)
) -> FullOCRResponse:
    """前端专用全量OCR处理API - 带持久化

    Args:
        request: 包含图像数据的请求（支持Base64编码或URL）
        service: 编排服务实例（通过依赖注入）

    Returns:
        FullOCRResponse: 全量OCR结果，包含所有识别类型的结果
    """
    check_image_input(request)
    return service.process_full_ocr(image_base64=request.image_base64, image_url=request.image_url, persist=True)
